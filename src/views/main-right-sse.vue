<template>
  <div class="main-wrap-right" :class="[props.pageName]" @dragenter="dragEnter" @dragleave="dragLeave"
    @dragover="dragOver" @drop="dropFile" id="main-wrap-right">
    <div class="main-wrap-right-content">
      <div class="loadingH" v-if="hisMesLoading">
        <spin tip="Loading"></spin>
      </div>
      <com-conversition :msgList="msgList.list" :recommendList="recommendList" :recommendListLoading="recommendListLoading" :msgLoading="msgLoading" :messageId="props.messageId" :canAsk="canAsk" @reGenerate="reGenerate" @stopGenerate="stopGenerate" :robotobj="robotobj" @sendMsg="sendMsg" @answerClick="answerClick" :goDownSeeTag="goDownSeeTag" @scrollBtmInterval="scrollBtmInterval"/>
    </div>
    <!-- 当不是PEA助手时，显示发送消息组件  -->
    <!-- <div class="send-comp-box" v-show="robotobj.robotId !== 15"> -->
    <com-send 
      :pageName="props.pageName"  
      @sendMsg="sendMsg" 
      :canAsk="canAsk" 
      :messageId="props.messageId" 
      :hisMesLoading="props.hisMesLoading"
      :msgList="msgList.list" 
      :robotobj="robotobj" 
      :model="model" 
      :innet="innet" 
      @modelChange="modelChange" 
      @innetChange="innetChange"  
    />
    <!-- </div> -->
    <!-- PEA助手显示创建PEA按钮 -->
    <!-- <div class="peabtn" v-if="robotobj.robotId == 15" @click="createPea" > 
      创建新的PEA
    </div> -->
    <!-- PEA 报告组件 -->
    <!-- <pea_up @sendMsg="sendMsg" :modalVisible="peaVisible" @cancel="peaCancle" /> -->

  </div>
</template>

<script setup>
import {
  ref,
  defineProps,
  defineEmits,
  onMounted,
  reactive,
  onBeforeUnmount,
  onUnmounted,
  defineExpose,
  watch,
  toRefs,
  nextTick,
} from "vue";
import { fetchEventSource } from '@microsoft/fetch-event-source'
import Bus from "@/utils/bus.js";
import comSend from "@/components/com-send";
import comConversition from "@/components/com-conversation";
// import BScroll from 'better-scroll'
import { recommendMsg, termList, processList } from "@/api";
import { getCookie } from "@/utils/common";
import { message, Spin } from "ant-design-vue";
// import ComConversation from "@/components/com-conversation.vue";
// import pea_up from "@/components/helper_diff/pea_up.vue";


const props = defineProps({
  messageId: Number, // 消息编号 以后此消息框的所有请求都带着它
  robotobj: Object,
  // historyLoading: Boolean,
  // 历史消息加载中
  hisMesLoading: Boolean, 
  // 是否推荐问
  recommendStatus: Number,
  // 页面标识
  pageName: String 

});

const emits = defineEmits(["titleChange"]);

let controller = null; // 用于取消请求
// const showPullMore = ref(false);
// msgLoading 发送消息后，收到的消息是否加载中。
let msgLoading = ref(false);

/** canAsk
 * 1、同msgLoading，发消息后收到消息加载中，不能问问题。
 * 2、用于在左侧历史记录点击，底部消息框发送消息 的判定，如果收话加载中，不能点击或发送。
 *  */
let canAsk = ref(true);
// 聊天内容list
let msgList = reactive({
  list: [],
});

watch(
  canAsk,
  (canAsk, oldCanAsk) => {
    Bus.$emit("canAsk", canAsk);
  }
);
const filenum = ref(0)
const messageFileId = ref(0)

let txtCountTimer = null;
let stopTag = false; // 是否停止生成标识
// // 快捷功能 对应的 请求方法map
// const shortFnMap = {
//   '慧画：': 'doAskImg',
//   '慧搜：': 'smartSearch'
// }

const answerClick = (words, regenerateId, msgDetailId = 0) => {

  if(props.hisMesLoading) {
    message.warn("历史消息加载中，请稍后...");
    return;
  }
  canAsk.value = false;
  stopTag = false;
  
  recommendList.value = [] // 清空相似问列表
  recommendListLoading.value = false // 相似问loading状态
  recommendReqMsgid.value = '' // 判断相似问重复
  
  // let wordsTxt = words

  if (!regenerateId) {
    // 如果不是重新生成
    msgList.list.push(
      {
        type: "ask",
        content: words.question,
        converseType: 1, // 0 普通 4 企标 3 文档 1 图片
      },
      {
        type: "gpt",
        reasoningText: "",
        webSearchText: {},
        content: "",
        sourceData: "",
        msgDetailId: "",
        converseType: props.robotobj.robotId,
      }
    );
  } else {
    msgList.list[regenerateId]["reasoningText"] = ""; // 清空原消息
    msgList.list[regenerateId]["content"] = ""; // 清空原消息
    msgList.list[regenerateId]["webSearchText"] = {} // 清空联网列表
    msgList.list[regenerateId]["sourceDataText"] = {} // 清空引用来源接口
  }
  msgLoading.value = true;
  clearInterval(scrollBtmTimer); // 解决连续快速聊天时，聊天记录滚动不上去的问题
  scrollBtmInterval(); // 滚动到底部
  // 发送 聊天请求
  doAnswerAgentText(words, regenerateId, msgDetailId);
}

const sendMsg = (words, regenerateId, msgDetailId = 0) => {

  if(props.hisMesLoading) {
    message.warn("历史消息加载中，请稍后...");
    return;
  }
  canAsk.value = false;
  stopTag = false;
  
  recommendList.value = [] // 清空相似问列表
  recommendListLoading.value = false // 相似问loading状态
  recommendReqMsgid.value = '' // 判断相似问重复

  
  let wordsTxt = words
  // 兼容AI阅读中试题生成文本显示
  if(props.robotobj.askType == 5 && props.robotobj.askTypeClass == 2) {
    // {"singleNum": 3, //单选 
    //  "multiNum": 3,//多选
    //   "judgeNum": 3//判断
    // }
    wordsTxt = `单选题【${words.singleNum||'--'}】个，多选题【${words.multiNum||'--'}】个，判断题【${words.judgeNum||'--'}】个，生成试题的要求是：${words.text||'--'}`

    
  } 

  if (!regenerateId) {
    // 如果不是重新生成
    msgList.list.push(
      {
        type: "ask",
        content: wordsTxt,
        converseType: 1, // 0 普通 4 企标 3 文档 1 图片
      },
      {
        type: "gpt",
        reasoningText: "",
        webSearchText: {},
        content: "",
        sourceData: "",
        msgDetailId: "",
        converseType: props.robotobj.robotId,
      }
    );
  } else {
    msgList.list[regenerateId]["reasoningText"] = ""; // 清空原消息
    msgList.list[regenerateId]["content"] = ""; // 清空原消息
    msgList.list[regenerateId]["webSearchText"] = {} // 清空联网列表
    msgList.list[regenerateId]["sourceDataText"] = {} // 清空引用来源接口
  }
  msgLoading.value = true;
  clearInterval(scrollBtmTimer); // 解决连续快速聊天时，聊天记录滚动不上去的问题
  scrollBtmInterval(); // 滚动到底部
  // 发送 聊天请求
  if(props.robotobj.askType == 3) { // 术语搜索 非流式
    termSearchFn( words )

  } else if (props.robotobj.askType == 6) { // 生计流程查询助手 非流式
    doAskProcess( words );

  } else if(props.robotobj.askType == 5 && props.robotobj.askTypeClass == 2) { // ai阅读 试题生成
    doAskTestSseText(words, regenerateId, msgDetailId);

  } else {
    doAskSseText(words, regenerateId, msgDetailId);

  }
};

// 术语助手
const termSearchFn = (words) => {
  termList({"messageId": props.messageId, "word": words}).then((res) => {
    const _idx = msgList.list.length - 1;
    if(res.code == 200) {
      msgList.list[_idx].content = res.data.terminologyInfoList
      msgList.list[_idx].msgDetailId = res.data.messageDetailId
      msgLoading.value = false;
      canAsk.value = true;
    } else {
      msgList.list[_idx].content = []
      msgLoading.value = false;
      canAsk.value = true;
    }
  })
}

// 流程查询助手
const doAskProcess = (words) => {
  processList({"messageId": props.messageId, "text": words}).then((res) => {
    const _idx = msgList.list.length - 1;
    if(res.code == 200) {
      msgList.list[_idx].content = res.data.processList
      msgList.list[_idx].msgDetailId = res.data.messageDetailId
      msgLoading.value = false;
      canAsk.value = true;
    } else {
      msgList.list[_idx].content = []
      msgLoading.value = false;
      canAsk.value = true;
    }
  })
}

/**
 * sse请求 url地址map
 **/
/**
 * 质量体系
 * 质量判标 两个自带思考过程
 */
/**
 * askType 
 * 0:通用智能体
 * 1:AI助理
 * 2:质量体系
 * 3:术语助手
 * 4:IT小助手 //人力问答助手
 * 5:AI阅读 助手
 * 6:生计流程查询助手
 */
// agent平台的值 0
let agentApi = '/gbotApi/client/agent/askAgent' // 通用智能体助手
let urlMap = {
  1: '/gbotApi/client/agent/askText', // AI助理
  2: '/gbotApi/client/agent/qualitySse ', // 质量体系
  4: '/gbotApi/client/agent/itSse', // IT小助手
  5: { // AI阅读
    1: '/gbotApi/client/agent/readDocumentSse', // 文章检索
    2: '/gbotApi/client/agent/readTestSse' // 试题生成
  },
  7: '/gbotApi/client/agent/answerAgent', // 返回问答对式、智造云设备问答
}
// 文字问答接口
// 文字问答 sse接口
let xhr = null;
const model = ref(true)
const innet = ref(false)
const modelChange = (val) => {
  model.value = val;
}
const innetChange = (val) => {
  innet.value = val;
}
let abortController = null;

const doAskSseText = (text, regenerateId, msgDetailId) => {
  abortController = new AbortController();
  if (window.EventSource) {
    ///chat/demo/getSseMsg
    let list = reactive([]);
    const _idx = msgList.list.length - 1;
    // xhr = new XMLHttpRequest();
    // 特殊接口配置
    let url = urlMap[props.robotobj.askType]? urlMap[props.robotobj.askType] : agentApi;
    if(props.robotobj.askType == 5) { // ai阅读中有两种情况：askTypeClass：1 文章检测，2:试题生成
      if(props.robotobj.askTypeClass) {
        url = url[props.robotobj.askTypeClass]
      }
    }
    msgList.list[_idx].modelName = (props.robotobj.askType == 1)? model.value : '';
    fetchEventSource(url, {
      method: 'POST',
      headers: {
        "Content-Type": "application/json; charset=utf-8",
        "userToken": getCookie("userToken"),
        "Authorization": `Bearer ${getCookie("userToken")}`
      },
      body: JSON.stringify({
        messageId: props.messageId,
        text: text,
        messageDetailId: msgDetailId, // 用于重新生成 status=0代表第一次生成  1代表重新生成
        robotId: props.robotobj.robotId,
        // modelName: (props.robotobj.robotId== 1)? model.value:'',
        reasoningStatus: model.value,
        onlineStatus: innet.value,
        fileIds: messageFileId.value
        // 添加变量 模型、是否联网。
        // controller
      }),
      // 停止生成
      signal: abortController.signal,
      // 当页面隐藏时，仍保持连接
      openWhenHidden: true,
      onopen: async (res) => {
        if (res.status === 200) {
          // answerStr.value = ''
        } else if (res.status === 400) {
          delCookie("userToken");
          router.push("/login");
          return false;
        } else {
          // if (event.readyState === EventSource.CLOSED) {
          //   clearInterval(timer);
          //   console.log("连接关闭");
          // } else {
          //   console.log(event);
          // }
        }
      },
      onmessage: (msg) => {
        console.log("收到消息内容：", msg.data) // xhr.responseText);
        const message = msg.data

        list.push(message);
        // str.value += xhr.responseText
        parseSSE(message).then((res) => { });
      },
      onclose: () => {
        msgList.list[_idx].converseType = props.robotobj.robotId; // 0 普通 4 企标
        // 重置 点赞状态
        // msgList.list[_idx]['commentStatus'] = 0
        // if (!(xhr.responseText).search('PENDING') === -1) { // pending状态 始终loading
        // 此处添加获取相似问数据；
        // if(props.robotobj.robotId !== 7 && props.robotobj.robotId !== 3 && props.robotobj.robotId !== 5 && props.robotobj.robotId !== 6 && props.robotobj.robotId !== 13 && props.robotobj.robotId !== 15) {
          if(props.recommendStatus == 0) {
            getRecommendMsg(props.messageId, _idx);
          }
          recommendReqMsgid.value = props.messageId;

        // }
        msgLoading.value = false;
        // }
        canAsk.value = true;
      },
      onerror: (err) => {
        console.log(err);
        clearInterval(scrollBtmTimer);

        msgLoading.value = false;
        msgList.list[_idx].content = "很抱歉，我无法帮到您 ~~";
        // 重置 点赞状态
        // msgList.list[_idx]['commentStatus'] = 0
        canAsk.value = true;
      }
    })
  } else {
    // timer = null
    console.log("浏览器不支持SSE");
  }
};
// 智造云 问答对列表
const doAnswerAgentText = (ques_item, regenerateId, msgDetailId) => {
  abortController = new AbortController();
  if (window.EventSource) {
    ///chat/demo/getSseMsg
    let list = reactive([]);
    const _idx = msgList.list.length - 1;
    // xhr = new XMLHttpRequest();
    // 特殊接口配置
    let url = "/gbotApi/client/agent/answerResult"
    
    msgList.list[_idx].modelName = (props.robotobj.askType == 1)? model.value : '';
    fetchEventSource(url, {
      method: 'POST',
      headers: {
        "Content-Type": "application/json; charset=utf-8",
        "userToken": getCookie("userToken"),
        "Authorization": `Bearer ${getCookie("userToken")}`
      },
      body: JSON.stringify({
        messageId: props.messageId,
        text: ques_item.question,
        listId: ques_item.listId,
        answerId: ques_item.answerId
      }),
      // 停止生成
      signal: abortController.signal,
      // 当页面隐藏时，仍保持连接
      openWhenHidden: true,
      onopen: async (res) => {
        if (res.status === 200) {
          // answerStr.value = ''
        } else if (res.status === 400) {
          delCookie("userToken");
          router.push("/login");
          return false;
        } else {
          // if (event.readyState === EventSource.CLOSED) {
          //   clearInterval(timer);
          //   console.log("连接关闭");
          // } else {
          //   console.log(event);
          // }
        }
      },
      onmessage: (msg) => {
        console.log("收到消息内容：", msg.data) // xhr.responseText);
        const message = msg.data

        list.push(message);
        // str.value += xhr.responseText
        parseSSE(message).then((res) => { });
      },
      onclose: () => {
        msgList.list[_idx].converseType = props.robotobj.robotId; // 0 普通 4 企标
        // 重置 点赞状态
        // msgList.list[_idx]['commentStatus'] = 0
        // if (!(xhr.responseText).search('PENDING') === -1) { // pending状态 始终loading
        // 此处添加获取相似问数据；
        // if(props.robotobj.robotId !== 7 && props.robotobj.robotId !== 3 && props.robotobj.robotId !== 5 && props.robotobj.robotId !== 6 && props.robotobj.robotId !== 13 && props.robotobj.robotId !== 15) {
          // if(props.recommendStatus == 0) {
          //   getRecommendMsg(props.messageId, _idx);
          // }
          // recommendReqMsgid.value = props.messageId;

        // }
        msgLoading.value = false;
        // }
        canAsk.value = true;
      },
      onerror: (err) => {
        console.log(err);
        clearInterval(scrollBtmTimer);

        msgLoading.value = false;
        msgList.list[_idx].content = "很抱歉，我无法帮到您 ~~";
        // 重置 点赞状态
        // msgList.list[_idx]['commentStatus'] = 0
        canAsk.value = true;
      }
    })
  } else {
    // timer = null
    console.log("浏览器不支持SSE");
  }
};

// 试题生成
const doAskTestSseText = (textObj, regenerateId, msgDetailId) => {
  abortController = new AbortController();
  if (window.EventSource) {
    ///chat/demo/getSseMsg
    let list = reactive([]);
    const _idx = msgList.list.length - 1;
    // xhr = new XMLHttpRequest();
    // 特殊接口配置
    let url = 'gbotApi/client/agent/readTestSse';
    msgList.list[_idx].modelName = '';
    // msgList.list[_idx].modelName = 
    fetchEventSource(url, {
      method: 'POST',
      headers: {
        "Content-Type": "application/json; charset=utf-8",
        "userToken": getCookie("userToken"),
        "Authorization": `Bearer ${getCookie("userToken")}`
      },
      body: JSON.stringify({
        messageId: props.messageId,
        // text: text,
        "singleNum": textObj.singleNum, //单选 
        "multiNum": textObj.multiNum,//多选
        "judgeNum": textObj.judgeNum,//判断
        text: textObj.text,
        // messageDetailId: msgDetailId, // 用于重新生成 status=0代表第一次生成  1代表重新生成
        robotId: props.robotobj.robotId,
        // modelName: (props.robotobj.robotId== 1)? model.value:'',
        // reasoningStatus: model.value,
        // onlineStatus: innet.value,
        // fileIds: messageFileId.value
        // 添加变量 模型、是否联网。
        // controller
      }),
      // 停止生成
      signal: abortController.signal,
      // 当页面隐藏时，仍保持连接
      openWhenHidden: true,
      onopen: async (res) => {
        if (res.status === 200) {
          // answerStr.value = ''
        } else if (res.status === 400) {
          delCookie("userToken");
          router.push("/login");
          return false;
        } else {
          // if (event.readyState === EventSource.CLOSED) {
          //   clearInterval(timer);
          //   console.log("连接关闭");
          // } else {
          //   console.log(event);
          // }
        }
      },
      onmessage: (msg) => {
        console.log("收到消息内容：", msg.data) // xhr.responseText);
        const message = msg.data

        list.push(message);
        // str.value += xhr.responseText
        parseSSE(message).then((res) => { });
      },
      onclose: () => {
        msgList.list[_idx].converseType = props.robotobj.robotId; // 0 普通 4 企标
        // 重置 点赞状态
        // msgList.list[_idx]['commentStatus'] = 0
        // if (!(xhr.responseText).search('PENDING') === -1) { // pending状态 始终loading
        // 此处添加获取相似问数据；
        // if(props.robotobj.robotId !== 7 && props.robotobj.robotId !== 3 && props.robotobj.robotId !== 5 && props.robotobj.robotId !== 6 && props.robotobj.robotId !== 13 && props.robotobj.robotId !== 15) {
          if(props.recommendStatus == 0) {
            getRecommendMsg(props.messageId, _idx);
          }
          recommendReqMsgid.value = props.messageId;

        // }
        msgLoading.value = false;
        // }
        canAsk.value = true;
      },
      onerror: (err) => {
        console.log(err);
        clearInterval(scrollBtmTimer);

        msgLoading.value = false;
        msgList.list[_idx].content = "很抱歉，我无法帮到您 ~~";
        // 重置 点赞状态
        // msgList.list[_idx]['commentStatus'] = 0
        canAsk.value = true;
      }
    })
  } else {
    // timer = null
    console.log("浏览器不支持SSE");
  }
};

// 解析SSE
const parseSSE = async (msgdata) => {  
  const _idx = msgList.list.length - 1;
  if(msgdata.indexOf("[DONE]") > -1) {
    if(msgdata.length > 6){
      const msgDetailId = msgdata.split("[DONE]")
      msgList.list[_idx].msgDetailId = msgDetailId[0]
    }
  }else if(msgdata){
    let msgdata_parse;
    try {
      msgdata_parse = JSON.parse(msgdata);
    } catch (error) {
      console.error('JSON 解析失败:', error);
      return; // 或处理错误逻辑
    }
    if (msgdata_parse && typeof msgdata_parse === 'object') {
      if(msgdata_parse.type === "web_search") {
        msgList.list[_idx].webSearchText = {}
        msgList.list[_idx].webSearchText.list = msgdata_parse.list
        msgList.list[_idx].content = '\n'

        // 联网检索，将联网panel展开
        Bus.$emit("expand", msgList.list[_idx])
      } else if(msgdata_parse.type === "reasoning") {
        msgList.list[_idx].reasoningText += msgdata_parse.content
        msgList.list[_idx].content = '\n'
      } else if(msgdata_parse.type === "text") {
        msgList.list[_idx].content += msgdata_parse.content
      } else if(msgdata_parse.type === "sourceData") { // 质量体系
        msgList.list[_idx].sourceDataText = {}
        msgList.list[_idx].sourceDataText.content = msgdata_parse.content
      } else if(msgdata_parse.type === "answerListText") { // 问答对式
        msgList.list[_idx].content = {} // 兼容后端返回历史记录显示
        msgList.list[_idx].content = msgdata_parse
      }
    }
  }
};

watch(() => props.messageId, (newVal) => {
  if(newVal) {
    recommendList.value = []
    recommendListLoading.value = false
    recommendReqMsgid.value = ''
    peaVisible.value = false; // 切换聊天将PEA助手关闭
  }
});

// 获取相似问接口
const recommendList = ref([]) // 相似问列表
const recommendListLoading = ref(false) // 相似问骨架屏loading
const recommendReqMsgid = ref('') // 当前请求的messageId
const getRecommendMsg = (messageId, _idx) => {
  recommendListLoading.value = true;
  recommendMsg({ 'messageId': messageId }).then((res) => {
    if(res.code == 200) {
      recommendListLoading.value = false
      let idx = msgList.list.length-1
      if(_idx == idx && recommendReqMsgid.value == messageId) { // 会话长度不同，表示之前获取到的列表不加载，：后续优化判断是这个回话id
        recommendList.value = res.data.recommendList || []
        let timer = setTimeout(() => {
          clearInterval(scrollBtmTimer);
          clearTimeout(timer)
        }, 500);
      }
    } else {
      recommendListLoading.value = false
    }
  });
}

// 重新生成
const reGenerate = (obj) => {
  stopTag = false;
  const { item, idx, msgDetailId } = obj;
  // console.log(item, idx)
  msgLoading.value = true;
  // console.log(msgList.list[idx - 1].content)
  let content = msgList.list[idx - 1].content; // 获取前一个提问内容
  sendMsg(content, idx, msgDetailId);
};

//停止生成
const stopGenerate = (type) => {
  stopTag = true;
  clearInterval(scrollBtmTimer);
  console.log("停止生成");
  clearTimeout(txtCountTimer);
  msgLoading.value = false;
  // if(type === 'img') controller.abort() // 取消图片生成请求（文字请求因为是流请求 无法调用abort方法取消）
  // controller.abort() // 取消图片生成请求（文字请求因为是流请求 无法调用abort方法取消）
  // xhr.abort();
  abortController.abort();
  canAsk.value = true;
};


// PEA弹窗显示控制
const peaVisible = ref(false);

// 自组件关闭弹窗
const peaCancle = (val) => {
  peaVisible.value = val;
};

const createPea = () => {
  peaVisible.value = true;
};

// 添加拖拽上传事件
const dragEnter = (event) => {
  event.preventDefault();
};
const dragLeave = (event) => {
  event.preventDefault();
};
const dragOver = (event) => {
  event.preventDefault();
};
const dropFile = (event) => {
  event.preventDefault();

  // console.log(event, "事件。。。。");
  if (event.target.className != "shortcut file" && props.robotobj.robotId == 2 && props.robotobj.robotId == 3 && props.robotobj.robotId == 5 && props.robotobj.robotId == 6) {
    const updata = {
      'file': event.dataTransfer.files[0]
    }
    Bus.$emit("upfile", updata);
  }
}

// 是否滚动到底部
const goDownSeeTag = ref(false); 

onMounted(() => {
  console.log("sse-page");
  console.log('挂在消息====')
  Bus.$on("historyList", (list) => {
    console.log('接收到消息')
    let intTimer = null;
    msgList.list = list;
    // debugger
    // list.map(item => msgList.push(item))
    clearTimeout(intTimer);
    chatWrap.scrollTop = 0;
    let timer = setTimeout(() => {
      chatWrap.scrollTop = 999999999999999;
      clearTimeout(timer)
      // chatWrap.scrollTop = (document.getElementById('scroll-box').offsetHeight - chatWrap.offsetHeight) + 100
    }, 100);
    nextTick(() => {
      // 首页、欢迎页有待发送内容 处理发送
      let msgDataObjStr = localStorage.getItem(props.messageId)
      if( msgDataObjStr ) {
        let msgDataObj = JSON.parse(msgDataObjStr)
        model.value = msgDataObj.model
        innet.value = msgDataObj.innet
        sendMsg(msgDataObj.msg)
        emits('titleChange', msgDataObj.msg) // 修改聊天标题内容
        localStorage.removeItem(props.messageId)
      }
    })
  });
  Bus.$on('filenum', (num) => {
    filenum.value = num
  })
  Bus.$on('messageFileId', (id) => {
    messageFileId.value = id
  })
  // 监听文件删除
  Bus.$on("fileDel", (idx) => {
    msgList.list.splice(idx, 1);
    // 修改当前文件标题内容
    if (!msgList.list.length) {
      // 如果没有 了 则 删除当前会话窗口
      // Bus.$emit('editName', {
      //   name: '新建对话窗口',
      //   messageId: props.messageId
      // })
      Bus.$emit("delConverse", props.messageId);
    } else {
      let name = msgList.list[0]["file"]
        ? msgList.list[0]["file"]["name"]
        : msgList.list[0]["content"];
      Bus.$emit("editName", {
        name,
        messageId: props.messageId,
      });
    }
  });

  chatWrap = document.getElementById("converse-box"); // 滚动元素

  chatWrap.onmousewheel = () => {
    //解决 typing时 滚动不了页面的问题s
    clearInterval(scrollBtmTimer);
    // 如果滚动到底部，继续自动滚动到底部
    if (chatWrap.scrollTop + chatWrap.clientHeight >= chatWrap.scrollHeight - 10) {
      goDownSeeTag.value = false
      if(canAsk.value == false) {
        scrollBtmInterval()
      }
    } else {
      // 如果没有滚动到底部，停止自动滚动到底部
      goDownSeeTag.value = true
      clearTimeout(scrollBtmTimer);

    }
  };
  // 监听点赞
  Bus.$on("comment", (obj) => {
    console.log("点赞", obj);
    msgList.list.map((item) => {
      if (item.msgDetailId === obj.id) {
        item["commentStatus"] = obj.commentStatus;
      }
    });
  });
});
onUnmounted(() => {
  // Bus.$off('historyList')
  Bus.$off('comment')
  Bus.$off('fileDel')
  Bus.$off('filenum')
  Bus.$off('messageFileId')
  
})
//滚动逻辑
let chatWrap = null;
let scrollBtmTimer = null;
const scrollBtmInterval = () => {
  if(canAsk.value) {
    // 内部逻辑用于com-ComConversation.vue中滚动到底部，但是回到底部按钮没有消失逻辑修复
    goDownSeeTag.value = false
    return
  }
  clearTimeout(scrollBtmTimer);
  scrollBtmTimer = setInterval(() => {
    // console.log('ssssss====')
    // const chatBoxHeight = document.getElementById('scroll-box').offsetHeight //
    // console.log(chatBoxHeight, chatBoxHeight - chatWrap.offsetHeight + 40)
    chatWrap.scrollTop = 999999999;
  }, 200);
};
</script>

<style lang="less" scoped>
.main-wrap-right {
  // min-width: 1000px;?
  // padding: 0px 32px 32px;
  min-width: 559px;
  position: relative;
  flex: 1;
  overflow: hidden;
  // max-width: 832px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  .send-wrap .send-box {
    width: 100% !important;
  }
}

.main-wrap-right-content {
  // transform: translate3d(0px, 0px, 0px);
  // position: relative;
  // top: 0;
  transition: all 0.5s ease-in-out;
  // height: calc(100% - 90px);
  // border-top: 1px solid #fdfeff80;
  overflow: hidden;
  flex: 1;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 12px;
    background: linear-gradient(
      180deg,
      #f1f2f5 0%,
      #f1f2f5 10%,
      rgba(241, 242, 245, 0) 100%
    );
    z-index: 99;
  }
  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 12px;
    background: linear-gradient(
      to top,
      #f1f2f5 0%,
      #f1f2f5 10%,
      rgba(241, 242, 245, 0) 100%
    );
    z-index: 99;
  }
}

.main-wrap-right {
  .main-wrap-right-content {
    position: relative;
    transition: all 0.5s ease-in-out;
  }

  .converse-wrap {
    top: 0px;
  }

  .hello_panel {
    position: relative;
    top: -100%;
  }
}

.loadingH {
  // padding-top: 10px;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9;

  .ant-spin-spinning {
    background: rgba(255, 255, 255, 0.8);
    padding: 10px;
    border-radius: 10px;
  }
}

.send-comp-box {
  position: absolute;
  bottom: 40px;
  // left: 50%;
  // transform: translate(-48%, 0);
  width: 100%;
}
.peabtn{
  width: 90%;
  max-width: 890px;
  border: 1px solid #e5dcff;
  margin: 0 auto;
  padding: 15px 0px;
  border-radius: 10px;
  cursor: pointer;
  background-color: #fefefe;
  box-shadow: 0px 0px 5px #0000001c;
  font-size: 15px;
  font-weight: bolder;
  &:hover {
    background-color: #fefefe;
    box-shadow: 0px 0px 10px #00000039;
    color: #5517fecc;
  }
}

@media (max-width: 1300px) {
 
}

</style>
