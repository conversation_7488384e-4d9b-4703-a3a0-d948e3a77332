<template>
  <div class="right-panel">
    <span class="close-btn" @click="closePanel"></span>
    <div class="content" v-if="props.rightPanelObj && props.rightPanelObj.webSearchText">
      <!-- {{ JSON.stringify(props.rightPanelObj.webSearchText.list) }} -->
      <com-net :webobj = "props.rightPanelObj.webSearchText.list" />

    </div>

  </div>
  
</template>

<script setup>
import {
  ref,
  defineProps,
  defineEmits,
  onMounted,
  reactive,
  onBeforeUnmount,
  onUnmounted,
  defineExpose,
  watch,
  toRefs,
  nextTick,
} from "vue";
import { message, Spin } from "ant-design-vue";
import comNet from "@/components/rightpanelComponents/com-net.vue"

import Bus from "@/utils/bus.js"

const props = defineProps({
  rightPanelObj: {
    type: Object,
    default: () => ({}),
  },
  type: { // \net \image \pdf \
    type: String,
    default: '',
  },


});

const closePanel = () => {
  Bus.$emit('collapsed')
};


onMounted(() => {

});
onUnmounted(() => {
  
})
</script>

<style lang="less" scoped>
.right-panel {
  height: calc(100% - 70px); width: 35%;  max-width: 490px; transition: all 0.1s linear;overflow-x: hidden; overflow-y: auto; position: relative; margin: 0px 18px 32px 18px; align-self: end;
  border-radius: 16px;
  border: 1px solid #FFF;
  background: linear-gradient(180deg, #EBEFFF 0%, #FFF 100%);
  box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
  backdrop-filter: blur(4.300000190734863px);
  position: relative;
  .close-btn {
    position: absolute;
    top: 22px;
    right: 22px;
    cursor: pointer;
    background-image: url("@/assets/image/close.svg");
    background-size: contain;
    width: 20px;
    height: 20px;


  }
  .content {
    height: 100%;
  }
  &.expanded {
    width: 32%;
  }
  &.collapsed {
    width: 0px;
    border: none;
    margin: 0px;
  }
  .fileBtn {
    position: absolute;
  }
}
</style>
