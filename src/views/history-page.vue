<template>
  <div class="history-container">
    <div class="content-wrap" ref="historyView">
      <div class="header layoutcss">
        <div class="title">历史会话</div>
        <div class="searchbox">
          <img src="@/assets/image/icon_his_search.svg" class="search-icon"><input placeholder="搜索历史会话,按回车搜索" class="search-input" v-model="search_word" @keyup.enter="handleSearch" />
        </div>
      </div>

      <div class="history-list layoutcss">
        <div v-for="(item, idx) in historyList" :key="idx">
          <div class="history-date">{{ item.groupName }}</div>
          <div class="history-item" v-for="(chat, chatIdx) in item.messageListSonVOList" :key="chatIdx">
            <div class="item-title" @click="viewChat(chat)">{{ chat.messageName }}</div>
            <div class="right-wrap">
              <div class="item-time">{{ chat.dateTime }}</div>
              <div class="item-control">
                <a type="link" @click.stop="editChat(chat)">
                  <img src="@/assets/image/edit.svg" class="edit-icon">
                </a>
                <Popconfirm
                  title="确定删除此条记录嘛?"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="deleteChat('1',chat)"
                  @cancel="deleteChat('0',chat)"
                >
                  <a type="link" @click.stop>
                    <img src="@/assets/image/delete.svg" class="delete-icon">
                  </a>
                </Popconfirm>
              </div>
            </div>
          </div>
        </div>
        <div class="empty" v-if="historyList.length === 0 && loading === false">
          -- 记录为空 --
        </div>
        <Spin v-if="loading" tip="历史记录加载中..." style="transform: translateX(-50%); margin-left: 50%;" />
        <p class="max-show-text" v-if="historyList.length != 0">
          <template v-if=" pageTotal < 50 && loading === false">
            -- 已全部加载 --
          </template>
          <template v-else>
            -- 最多显示50条 --
          </template>
        </p>
      </div>
    </div>
  </div>
  <Modal v-model:visible="mdVisible" title="修改名称" class="title-modal" :okText="'确定'" :cancelText="'取消'" @cancel="cancelName" @ok="handleOk">
    <p><a-input v-model:value="editItemTit" :maxlength="20" placeholder="输入修改后的名称" /></p>

  </Modal>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { messageList, deleteMessage, updateMessageName } from '@/api/index.js';
import { message, Modal, Popconfirm, Spin } from 'ant-design-vue';
const router = useRouter();


const mdVisible = ref(false);
const hisItem = ref({});
const editItemTit = ref('')
const editChat = (item) => {
  hisItem.value = item
  mdVisible.value = true;
  editItemTit.value = item.messageName;
};

const deleteChat = (tag, item) => {
  if(tag === '1') {
    if(item.askType == 5) {
      message.warning('AI阅读会话，不能删除');
      return 
    }

    deleteMessage({ messageId: item.messageId }).then((res) => {
      if (res.code === 200) {
        message.success('会话已删除');
        handleSearch();
      }
    })
  } else {
    message.warning('删除已取消');
  }
};

const viewChat = (item) => {
  if(item.askType == 5) {
    router.push({ path: 'ai-reader', query: { robotId: item.robotId, askType: item.askType } });
  } else {
    router.push({ name: 'chat', params: { messageId: item.messageId } });
  }
};

// 取消修改名称
const cancelName = () => {
  mdVisible.value = false;
  hisItem.value = {};
  editItemTit.value = '';
};

// 确认修改名称
const handleOk = () => {
  let data = {
    messageId: hisItem.value.messageId,
    messageName: editItemTit.value
  }
  updateMessageName(data).then((res) => {
    if(res.code === 200) {
      message.success('修改成功');
      mdVisible.value = false;
      hisItem.value = {};
      editItemTit.value = '';
      handleSearch()
    } else {
      // message.error('修改失败');
    }
  }).catch(err => {
    // message.error('修改失败');
    console.log(err);
  })
};

// 搜索历史会话
const handleSearch = (event) => {
  // search_word.value = event.target.value;
  page.value = 1; // 重置页码
  historyList.value = [] // 清空历史记录
  getHistoryList(); // 调用获取历史列表的方法
};

const page = ref(1);
const pageSize = ref(50);
const pageTotal = ref(0);
const search_word = ref(''); // 搜索关键词

const loading = ref(false);
const historyList = ref([]);
const getHistoryList = () => {
  if(loading.value) return;
  loading.value = true;
  messageList({ page: page.value, pageSize: pageSize.value, word: search_word.value }).then(res => {
    loading.value = false;
    if(res.code === 200) {
      if(res.data.messageList.length < pageSize.value) {
        loadingFinish.value = true
      }
      pageTotal.value = res.data.total
      historyList.value = historyList.value.concat(res.data.messageList);
    }
  }).catch(err => {
    console.error('获取历史消息列表失败', err);
    loading.value = false;
  });
}

const loadingFinish = ref(false);
const historyView = ref(null);

onMounted(() => {
  getHistoryList();
  // historyView.value.addEventListener('scroll', () => {
  //   const { scrollTop, scrollHeight, clientHeight } = historyView.value;
  //   if (scrollTop + clientHeight >= scrollHeight - 10) {
  //     page.value++;
  //     getHistoryList();
  //   }
  // });
});




</script>
<style lang="less" scoped>
.history-container { 
  background: url(@/assets/image/ibg.png) no-repeat center center;
  background-size: cover;
  width: calc(100% - 108px) !important; // 元素位于除导航外的部分
  height: 100%;
  margin-left: 108px;
  padding-left: 0px !important;

 }
 .content-wrap {
  // max-width: ;
  // max-width: 768px;
  // padding: 0px 32px;
  // margin: 0 auto;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  & > div {
    width: 100%;
  }
 }
 .header {
  position: sticky;
  position: -webkit-sticky;
  top: -74px;
  z-index: 9;
 }
.title {
  font-size: 32px; color: #000; padding: 12px 0px;
}
.searchbox {
  border-radius: 12px;
  border: 1px solid var(--Color-Fill-fill-color-darker, #E0E1E6);
  background: rgb(250 252 253);
  box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.05);
  display: flex;
  padding: 12px;
  vertical-align: middle;
  position: sticky;
  position: -webkit-sticky;
  top: 0px;
  .search-icon {
    padding-right: 4px;
  }
  input {
    flex: 1; background: none; border: none; outline: none; font-size: 14px;
  }
  margin-bottom: 24px;
}
.history-list {
  text-align: left;
  flex: 1;
  padding-bottom: 40px;
  position: relative;
  // overflow-y: auto;
  .history-date {
    font-size: 24px;
    color: #000;
    padding-bottom: 12px;
  }
  .history-item{
    display: flex;
    border-radius: 12px;
    border: 1px solid #FFF;
    background: rgba(255, 255, 255, 0.50);
    box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
    padding: 12px;
    border: 12px;
    align-items: center;
    justify-content: space-between;
    // cursor: pointer;
    margin-bottom: 12px;
    .item-title {
      color: #1C2024; 
      max-width: calc(100% - 90px); /* 可设置具体宽度或用 max-width 限制最大宽度 */
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1; /* 限制文本显示的行数为 3 行 */
      text-overflow: ellipsis;
      flex: 1;
      cursor: pointer;
      &:hover {
        color: #0d00ff;
      }
    }
    .right-wrap {
      display: flex; 
      align-items: center;
      height: 22px;
      overflow: hidden;
      margin-left: 30px;
      .item-time {
        color: #1C2024; 
        font-size: 14px; 
        padding-right: 12px;
        width: 85px;
        overflow: hidden;
        transition: all 0.2s linear;
        height: 22px;
        line-height: 22px;
      }
      .item-control {
        a {
          img {
            transition: all 0.1s linear;
          }
          &:hover {
            img{ transform: scale(1.3) };
          }
        }
        a:first-child { margin-right: 12px; }
        width: 0px; overflow: hidden;
        transition: all 0.2s linear;
        height: 100%;
        overflow: hidden;
      }
    }
    &:hover {
      .right-wrap {
        .item-time {
          width: 0px;
          padding: 0px;
        }
        .item-control {
          width: 50px;
        }
      }
    }

  }
  .empty {
    text-align: center;
  }
  .max-show-text {
    width: 100%;
    text-align: center;
    margin-bottom: 20px;
    position: absolute;
    bottom: 0px;
    left: 0px;
    color: #0000003f;
  }

}



</style>