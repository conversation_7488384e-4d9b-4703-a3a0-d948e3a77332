<template>
  <div class="img_layloutcss chat-page-container">
    <h1 class="page_title">
      <span>通用图片信息提取</span>
    </h1>
    <div class="page_desc">上传图片可快速提取信息</div>
    
    <div class="upImgBox">
      <fileupImageCom @uploadSus="uploadSus" :imageId="imageData.imageId" :imageDetailId="imageData.imageDetailId" :image_url="imageData.image_url" :side="imageData.side" :imageType="imageData.imageType" />
    </div>
    <div class="resultBox">
      <div class="resultTxt">
        <div class="resultTxtScroll">
          <p class="noResult" v-if="!identifyResultData.length">暂无识别结果</p>
          <p v-for="(value, idx) in identifyResultData" :key="idx">
            {{ value }}
          </p>
        </div>
      </div>
      <!-- <div class="controlBox" style="display: none;">
        <div class="repanel">
          <i class="icon_i re"></i>
          重新生成
        </div>
        <div class="copanel">
          <i class="icon_i co"></i>
          复制
        </div>
      </div> -->
      <div class="idCard_regResult_control">
        <div class="repanel" @click="recognizeFn">
          <i class="icon_i re"></i>
          重新生成
        </div>
        <div class="copanel" @click="copyFn">
          <i class="icon_i co"></i>
          复制
        </div>
      </div>

    </div>

    <bottomsBtns @recognizeFn="recognizeFn" @languageChange="languageChange" :languageTag="true" />
  </div>
  <div class="loading" v-if="recognizeLoading"> 识别中,请稍等... </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
// import {  message } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';

import { Upload, Spin, message } from "ant-design-vue";

import bottomsBtns from "./bottoms-btns-com.vue";
import fileupImageCom from "./fileup-image-com.vue";
import { imageIdentify, imageDetail } from '@/api/index'


const route = useRoute()
const router = useRouter()

const imageId = ref(route.query.imageId || '0')
const imageDetailId = ref(route.query.imageDetailId || '0')
const languageValue = ref(route.query.languageValue || 'Chinese')


// 初始化空、设置查询到的接口详情
const imageData = reactive({
  imageType: 0, // 0通用图片 1身份证 2驾驶证
  imageId: imageId.value,
  imageDetailId: imageDetailId.value,
  side: 'face',
  image_url: ''
})

const languageChange = (val) => {
  languageValue.value = val
}

// const imageDetailData = ref({})
const identifyResultData = ref({})

const recognizeLoading = ref(false)
const recognizeFn = () => {
  if(recognizeLoading.value) {
    return
  }
  if(imageData.imageId == '0') {
    message.warning('请先上传图片')
    return
  }
  recognizeLoading.value = true
  // 调用识别接口

  imageIdentify({ imageId: imageData.imageId, side: languageValue.value}).then(res => {
    recognizeLoading.value = false;
    if(res.code == 200) {
      identifyResultData.value = res.data

      message.success({
        key: "identify",
        content: "识别成功",
      });
      // console.log(res.data, 'res.data===')
      
    
    }
  })


  // setTimeout(function(){
  //   recognizeLoading.value = false
  // }, 2000)

}

const uploadSus = (imgobj) => {
  imageData.imageId = imgobj.imageId
  imageData.imageDetailId = imgobj.imageDetailId
  imageData.image_url = imgobj.image_url
  identifyResultData.value = {}
}

const copyFn = () => {
  // 调用复制接口
  let text = ``
  identifyResultData.value.forEach(item => {
    text += `${item}\n`
  })
  // let text = "产品名称、产品分类、产品保质期、储存条件。等等相关。"
  text = text.replace(/^\n/, "");
  const save = function (e) {
    e.clipboardData.setData("text/plain", text);
    e.preventDefault(); // 阻止默认行为
  };
  document.addEventListener("copy", save, { once: true }); // 添加一个copy事件
  document.execCommand("copy"); // 执行copy方法
  // message.success("复制成功");
  message.success({
    key: "copy",
    content: "复制成功",
  });

}


onMounted(() => {
  // debugger
  if(imageData.imageId && imageData.imageId != '0') {
    imageDetail({ imageId: imageData.imageId }).then(res => {
      if(res.code == 200) {
        // imageDetailData.value = res.data[0]
        imageData.imageDetailId = res.data[0].imageDetailId
        imageData.image_url = res.data[0].imageUrl
        imageData.side = res.data[0].side
        imageData.imageType = 0
        imageData.imageId = res.data[0].imageId
        recognizeFn()
      }
    })
  }
})



</script>

<style lang="less" scoped>
.img_layloutcss {
  max-width: 1209px;
  width: 80%;
  padding-left: 108px;
  margin: 0px auto 0px;
  position: relative;
  padding-top: 24px;
}

.loading {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  background-color: #ffffff8c;
  display: flex;
  align-items: center;
  justify-content: space-around;
  color: #001aff;


}

.page_title {
  text-align: center;
  font-size: 42px;
  font-weight: 700;
  display: flex;
  justify-content: space-around;
  margin-bottom: 0px;
  line-height: normal;
  span {
    background: linear-gradient(
      90deg,
      #0fd3ff 0.07%,
      #364afd 12.07%,
      #4c4efb 32.69%,
      #b457ff 99.85%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.page_desc {
  color: #8b8d98;
  text-align: center;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 5px;
  padding: 8px 0px 32px;
}



/deep/ .upImgBox {
  border-radius: 16px;
  border: 1.5px solid #FFF;
  background: linear-gradient(180deg, #E3E7FF 0%, #EAEEFF 100%);
  box-shadow: 0 4px 24px 0 rgba(129, 136, 255, 0.10);
  backdrop-filter: blur(4.300000190734863px);
  padding: 20px;
  

}

.resultBox {
  padding: 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.70);
  margin-top: 20px;

  .resultTxt {
    border-radius: 14px;
    background: rgba(235, 235, 235, 0.70);
    height: 220px;
    margin-bottom: 40px;
    padding: 20px;
    color: #000;
    line-height: 24px;
    text-align: left;
    // max-height: 220px;
    .resultTxtScroll {
      overflow-y: auto;
      width: 100%;
      height: 100%;
      font-size: 14px;
      line-height: 24px;

    }
  }

  .controlBox {
    display: flex;
    justify-content: space-between;
  }

  .idCard_regResult_control {
    display: flex;
    justify-content: space-between;
    width: 100%;
    color: #8B8D98;
    > div {
      cursor: pointer;
    }
    .icon_i {
      cursor: pointer;
      display: inline-block;
      width: 18px;
      height: 18px;
      background-size: auto;
      opacity: 1;
      background-repeat: no-repeat;
      background-position: center center;
    }

    .repanel {
      display: flex;
      .re {
        background-image: url(@/assets/image/icon_regenerate.svg);
      }

      &:hover {
        opacity: 0.8;
      }
    }

    .copanel {
      display: flex;
      align-items: center;
      .co {
        background-image: url(@/assets/image/icon_copy.svg);
      }

      &:hover {
        opacity: 0.8;
      }
    }

  }


}
/deep/ .ant-upload.ant-upload-select {
  display: block;
}
.noResult { color: #8B8D98; }



</style>
