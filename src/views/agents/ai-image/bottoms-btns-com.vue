<template>
  <div class="bottomBtns">
    <div class="btnLeft">
      <div class="identifyBtn" @click="toRecognizeFn">立即识别</div>
      <!-- @change="handleChange" -->
      <Select
        ref="select"
        v-model:value="side"
        class="selectBox"
        v-if="props.languageTag"
        @change="languageChangeFn"
      >
        <Select.Option value="Chinese">中文</Select.Option>
        <Select.Option value="English">英文</Select.Option>
        <Select.Option value="Arabic">阿拉伯语</Select.Option>
        <Select.Option value="Russian">俄语</Select.Option>
        <Select.Option value="Spanish">西班牙语</Select.Option>
        <Select.Option value="Portuguese">葡萄牙语</Select.Option>
        <Select.Option value="French">法语</Select.Option>
        <Select.Option value="German">德语</Select.Option>
      </Select>
    </div>
    <div class="moreBtn" @click="goMoreAgents()">更多智能体>></div>
  </div>
</template>

<script type="ts" setup> 
import { ref, defineProps, defineEmits } from 'vue'
import { useRoute, useRouter } from 'vue-router';
import { Select } from 'ant-design-vue';

const router = useRouter()
const route = useRoute()

const props = defineProps({
  languageTag: {
    type: Boolean,
    default: false
  },
  languageValue: {
    type: String,
    default: 'Chinese'
  }

})

const side = ref(props.languageValue)

const emits = defineEmits(['recognizeFn', 'languageChange'])

const toRecognizeFn = () => {
  emits('recognizeFn')
}

const languageChangeFn = () => {
  emits('languageChange', side.value)
}

// 跳转更多智能体
const goMoreAgents = () => {
  router.push('/agent-page')
}

</script>
<style lang="less">
.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: 1px solid #ffffff;
  box-shadow: none;
  background: #fff;
  border-radius: 5px;
  width: 120px;
}

</style>

<style lang="less" scoped>

.bottomBtns {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 0px;

  .btnLeft {
    display: flex;
    align-items: center;
  }
  .selectBox {
    &:hover {
      opacity: 0.8;
    }
  }
  .identifyBtn {
    border-radius: 1rem;
    background: #3133FF;
    backdrop-filter: blur(4.300000190734863px);
    color: #FFF;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    padding: 16px 24px;
    cursor: pointer;
    margin-right: 10px;
    &:hover {
      opacity: 0.8;
    }
    
  }

  .moreBtn {
    color: #3133FF;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }


  }
}
/deep/ .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
  height: 50px;
  padding: 16px;
}
/deep/ .ant-select-single .ant-select-selector .ant-select-selection-item, .ant-select-single .ant-select-selector .ant-select-selection-placeholder {
  line-height: 18px;
}
/deep/ .ant-select {
  display: flex;
  width: 108px;
  padding: 0px;
  justify-content: center;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
  align-self: stretch;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.80);
  box-shadow: 0 4px 29.2px 0 rgba(67, 72, 169, 0.13);
  backdrop-filter: blur(4.300000190734863px);
}
/deep/ .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  border: none !important;
}
/deep/ .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
  box-shadow: none;
}
/deep/ .ant-select:not(.ant-select-customize-input) .ant-select-selector {
  background: none;
}
/deep/ .ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: none !important;
}


</style>
