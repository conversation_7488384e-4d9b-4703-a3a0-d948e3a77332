<template>
  <Upload
    name="file"
    :show-upload-list="false"
    :customRequest="customRequest"
    :before-upload="beforeUpload"
    ref="uploadRef"
    accept="image/jpeg,image/png"
    class="upLoadSpan"
  >
    <Spin :spinning="upLoading" @click="uploadChange">
      <div class="upImgPanel" v-if="!props.image_url">
        <div class="upImgControl">
          <div class="upImgTag">
            <img src="@/assets/img/up_img_tag.png" />
          </div>
          <div class="upImgDesc">点击或将图片拖拽到这里上传</div>
          <div class="upImgAccept">支持扩展名: jpg、jpeg、png</div>
        </div>
      </div>
      <!-- @click.stop="tipsControlFn"  -->
      <div class="upImageBox" v-else>
        <img :src="props.image_url" />

      </div>
    </Spin>
  </Upload>
</template>

<script setup>
import { ref, defineProps, watch, defineEmits, reactive } from "vue";
import { Upload, Spin, message } from "ant-design-vue";
import { imageUp } from '@/api/index'

const props = defineProps({
  imageType: { // 1身份证 2驾驶证 0通用图片
    type: Number,
    default: 0
  },
  imageId: { // 页面级图片会话id
    default: '0'
  },
  imageDetailId: { // 图片详情id
    default: '0'
  },
  side: { // 人像面 正面
    type: String,
    default: 'face'
  },
  image_url: { // 图片url
    type: String,
    default: ''
  }
})

const emits = defineEmits(['uploadSus'])

const upLoading = ref(false)

// const tipsControlFn = () => {
//   console.log('click===')
// }

// console.log(props.imageType, 'props.imageType')

const imageData = reactive({
  imageType: props.imageType,
  imageId: props.imageId,
  imageDetailId: props.imageDetailId,
  side: props.side,
  image_url: props.image_url
})


// 上次调用 
const customRequest = (data) => {
  upLoading.value = true;
  const file = data.file;
  const formData = new FormData();
  // console.log(imageData, 'imageData')
  formData.append("file", file);
  formData.append("imageId", imageData.imageId);
  formData.append("imageType", imageData.imageType); // 1身份证 2驾驶证 0通用图片
  formData.append("imageDetailId", imageData.imageDetailId);
  formData.append("side", imageData.side);
  // 将调用上传文件接口
  imageUp(formData).then(res => {
    upLoading.value = false;
    if(res.code == 200) {
      message.success({
        key: "fileup",
        content: "文件上传成功",
      });

      imageData.imageId = res.data.imageId
      imageData.imageDetailId = res.data.imageDetailId
      imageData.image_url = res.data.imgUrl
      // imageData.side = imageData.side
    
      emits('uploadSus', imageData)
      
    }
  })
}

const beforeUpload = file => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('请上传jpg或png格式的图片')
  }
  return isJpgOrPng;
};


// 
const uploadChange = (event) => {
  // 确定哪个参数为判断标准
  // let judgeParam = tag == 'front' ? cardLoading : cardLoading
  // console.log(judgeParam.value, 'judgeParam.value')
  // let newval = ( tag == 'front' ? cardFrontLoading.value === true : cardBackLoading.value === true )
  if (upLoading.value === true) {
    message.warning({
      key: "fileup",
      content: "有文件正在上传，请稍后再试！",
    });
    event.stopPropagation();
    return;
  }
}


</script>

<style lang="less" scoped>
.upImgBox {
  text-align: left;
  h1 {
    img {
      width: 18px;
      height: 18px;
      margin-right: 5px;
    }
    display: flex;
    font-size: 14px;
    padding: 24px 0px 10px;
    line-height: 18px;
    margin-bottom: 0px;
  }

  .upLoadSpan {
    display: block;
    .ant-upload {
      width: 100%;
    }
  }

  .upImgPanel {
    border-radius: 1rem;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0 4px 29.2px 0 rgba(67, 72, 169, 0.13);
    backdrop-filter: blur(4.300000190734863px);
    padding: 1rem;
  }

  .upImgControl {
    border-radius: 10px;
    border: 1px dashed #d0d0d0;
    background: #f5f5f5;
    text-align: center;
    padding: 25px 0px;
    cursor: pointer;

    .upImgTag {
      margin-bottom: 35px;
    }

    .upImgDesc {
      color: #000;
      font-size: 0.875rem;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }

    .upImgAccept {
      color: #60646C;
      font-size: 0.875rem;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
  .upImageBox {
    max-width: 100%;
    overflow: hidden;
    border-radius: 20px;
    background: #fff;
    text-align: center;
    cursor: pointer;
    img {
      max-width: 100%;
      max-height: 320px;
      min-height: 120px;
      /* // max-height: 320px; */
    }
  }

}


</style>
