<template>
      <a class="goback" @click="goImageHome()">返回</a>

</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';

const route = useRoute()
const router = useRouter()

const goImageHome = () => {
  router.push('/ai-image')
}

</script>

<style lang="less" scoped>
.goback {
  position: absolute;
  top: 28px;
  left: 108px;
  color: #5E6772;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.80);
  box-shadow: 0 4px 29.2px 0 rgba(67, 72, 169, 0.13);
  backdrop-filter: blur(4.300000190734863px);
  padding: 16px;
  z-index: 10;
}


</style>
