<template>
  <div class="img_layloutcss chat-page-container">
    <h1 class="page_title">
      <span>图片分析</span>
    </h1>
    <div class="page_desc">你的办公助手，我的智能伙伴</div>

    <div class="ability_list">
      <div class="ability_item" @click="routerSkip('/id-card')">
        <img class="ability_item_img" src="@/assets/img/img_ability1.png" />
        <div class="ability_item_bottom">
          <div class="ability_item_title">
            <span class="abilityTit">
              身份证信息提取
            </span>
          </div>
          <div class="ability_item_desc">
            上传身份证照片正反面可快速提取信息
          </div>
        </div>
      </div>

      <div class="ability_item" @click="routerSkip('/id-driver')">
        <img src="@/assets/img/img_ability2.png" />
        <div class="ability_item_bottom">
          <div class="ability_item_title">
            <span class="abilityTit">
              驾驶证信息提取
            </span>
          </div>
          <div class="ability_item_desc">
            支持上传驾驶证正页，智能识别姓名、证号等信息
          </div>
        </div>
      </div>

      <div class="ability_item await_box">
        <img src="@/assets/img/img_ability3.jpg" />
        <div class="ability_item_bottom">
          <div class="ability_item_title">
            <span class="abilityTit">
              行驶证信息提取
            </span>
            <a>
              <span>敬请期待</span>
            </a>
          </div>
          <div class="ability_item_desc">
            快速提取行驶证内容，高效录入车辆数据
          </div>
        </div>
      </div>

      <div class="ability_item await_box">
        <img src="@/assets/img/img_ability4.png" />
        <div class="ability_item_bottom">
          <div class="ability_item_title">
            <span class="abilityTit">
              营业执照信息提取
            </span>
            <a>
              <span>敬请期待</span>
            </a>
          </div>
          <div class="ability_item_desc">
            自动识别营业执照内容，高效获取企业资料
          </div>
        </div>
      </div>
    </div>

    <div class="upImgBox">
      <h1>
        <img src="@/assets/img/title_tag.png" />
        通用图片信息提取
      </h1>
      <fileupImageCom @uploadSus="uploadSus" :imageId="imageData.imageId" :imageDetailId="imageData.imageDetailId" :image_url="imageData.image_url" :side="imageData.side" :imageType="imageData.imageType" />
    </div>

    <bottomsBtns @recognizeFn="recognizeFn" @languageChange="languageChange" :languageTag="true" />
  </div>
  <div class="loading" v-if="recognizeLoading"> 识别中,请稍等... </div>
</template>

<script setup>
import { reactive, ref } from 'vue';
// import {  message } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';

import { Upload, Spin, message } from "ant-design-vue";
import bottomsBtns from "./bottoms-btns-com.vue";

import fileupImageCom from "./fileup-image-com.vue";
import { imageIdentify } from '@/api/index'



const route = useRoute()
const router = useRouter()

const routerSkip = ( routerPath ) => {
  router.push( routerPath )



}


const languageValue = ref('Chinese')
const languageChange = (val) => {
  languageValue.value = val
}


const recognizeLoading = ref(false)
const recognizeFn = () => {
  if(recognizeLoading.value) {
    return
  }
  recognizeLoading.value = true
  // 调用识别接口
  // router.push('/ai-image-general', {imageId: imageData.imageId})
  router.push({ path: '/ai-image-general', query: { imageId: imageData.imageId, imageDetailId: imageData.imageDetailId, languageValue: languageValue.value } });

  // imageIdentify({ imageId: imageData.imageId }).then(res => {
  //   recognizeLoading.value = false;
  //   if(res.code == 200) {
  //     identifyResultData.value = res.data

  //     message.success({
  //       key: "identify",
  //       content: "识别成功",
  //     });
  //     console.log(res.data, 'res.data===')
      
    
  //   }
  // })

  // setTimeout(function(){
  //   recognizeLoading.value = false
  //   routerSkip('/ai-image-general')
  // }, 2000)

}

const imageData = reactive({
  imageType: 0, // 0通用图片 1身份证 2驾驶证
  imageId: '0',
  imageDetailId: '0',
  side: 'face',
  image_url: ''
})

const uploadSus = (imgobj) => {
  imageData.imageId = imgobj.imageId
  imageData.imageDetailId = imgobj.imageDetailId
  imageData.image_url = imgobj.image_url
}



</script>

<style lang="less" scoped>
.upImgBox {
  text-align: left;
  h1 {
    img {
      width: 18px;
      height: 18px;
      margin-right: 5px;
    }
    display: flex;
    font-size: 14px;
    padding: 24px 0px 10px;
    line-height: 18px;
    margin-bottom: 0px;
  }
}
.img_layloutcss {
  max-width: 1209px;
  width: 80%;
  padding: 24px 0px 0px 108px;
  margin: 0 auto;
  position: relative;
  padding-top: 24px;
}


.loading {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  background-color: #ffffff8c;
  display: flex;
  align-items: center;
  justify-content: space-around;
  color: #001aff;

}


.page_title {
  text-align: center;
  font-size: 42px;
  font-weight: 700;
  display: flex;
  justify-content: space-around;
  margin-bottom: 0px;
  line-height: normal;
  span {
    background: linear-gradient(
      90deg,
      #0fd3ff 0.07%,
      #364afd 12.07%,
      #4c4efb 32.69%,
      #b457ff 99.85%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.page_desc {
  color: #8b8d98;
  text-align: center;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 5px;
  padding: 8px 0px 32px;
}

.ability_list {
  display: flex;
  gap: 16px;
  .ability_item {
    background: #fff;
    border-radius: 16px;
    border: 1.5px solid #fff;
    background: linear-gradient(180deg, #e3e7ff 0%, #eaeeff 100%);
    box-shadow: 0px 4px 24px 0px rgba(129, 136, 255, 0.1);
    backdrop-filter: blur(4.300000190734863px);
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    transition: all 0.2s linear;
    img {
      width: 100%;
      
    }
    .ability_item_bottom {
      /* border-radius: 0 0 8px 8px; */
      background: rgba(255, 255, 255, 0.70);
      text-align: left;
      padding: 12px;
      height: 100%;

      .ability_item_title {
        font-size: 16px;
        color: #1c2024;
        font-weight: 700;
        display: flex;
        justify-content: space-between;
        height: 24px;
        margin-bottom: 6px;
        .abilityTit {
          white-space: nowrap;      /* 禁止换行 */
          overflow: hidden;         /* 超出隐藏 */
          text-overflow: ellipsis;  
        }
        a {
          background-color: #fff;
          padding: 2px 16px;
          font-size: 12px;
          border-radius: 999px;
          display: flex;
          align-items: center;
          cursor: not-allowed;
          // white-space: nowrap;      /* 禁止换行 */
          // overflow: hidden;         /* 超出隐藏 */
          // text-overflow: ellipsis;
          // justify-content: space-around;
          width: 80px;
          min-width: 80px;
          span {
            background: linear-gradient(90deg, #0FD3FF 0.07%, #364AFD 12.07%, #4C4EFB 32.69%, #B457FF 99.85%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            line-height: 16px;
          }
        }
      }

      .ability_item_desc {
        color: #60646C;
        font-size: 12px;
        font-weight: 400;
      }
    }
  }
  .await_box {
    cursor: not-allowed;
    .ability_item_bottom {
      .ability_item_title {
        color: #60646C;
      }
    }
  }
  .ability_item:not(.await_box) :hover {
    .ability_item_title {
      color: #001aff;
    }
    .ability_item_img {
      transform: scale(1.02);
    }
  }
}

/deep/ .ant-upload.ant-upload-select {
  display: block;
}


/* .bottomBtns {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 0px;

  .identifyBtn {
    border-radius: 1rem;
    background: #3133FF;
    backdrop-filter: blur(4.300000190734863px);
    color: #FFF;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    padding: 16px 24px;
    cursor: pointer;
  }

  .moreBtn {
    color: #3133FF;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    cursor: pointer;



  }
} */

</style>
