<!-- 驾驶证识别 -->

<template>
  <div class="img_layloutcss chat-page-container">
    <goImageBack />
    <h1 class="page_title">
      <span>驾驶证信息提取</span>
    </h1>
    <div class="page_desc">上传驾驶证照片可快速提取信息</div>
    
    <div class="idCardBox">
      <div class="idCardImg">
        <Upload
          name="file"
          :show-upload-list="false"
          :customRequest="customRequest"
          ref="uploadRef"
          accept="image/jpeg,image/png"
          :before-upload="beforeUpload"
        >
          <Spin tip="上传中..." :spinning="cardLoading" @click="uploadChange($event, 'front')" >
            <div class="idCardFrontImg" v-if="!image_url">
              <span>
                <img src="@/assets/img/idcard_front.png" />
              </span>
              <div class="imgDesc">
                点击上传驾驶证正页
              </div>
            </div>
            <div class="upImageBox" v-else>
              <img :src="image_url" />

            </div>
          </Spin>
        </Upload>
      </div>
      <div class="idCard_regResult">
        <div class="idCard_regResult_content">
          <div class="resultItem">
            <span>姓名：</span>
            <div class="resultContent"> {{ 
            identifyResultData.name || ''
            }} </div>
          </div>
          <div class="resultItem">
            <span>性别：</span>
            <div class="resultContent"> {{ 
            identifyResultData.sex || ''
            }} </div>
          </div>
          <div class="resultItem">
            <span>出生日期：</span>
            <div class="resultContent"> {{ 
            identifyResultData.birth_date || ''
            }} </div>
          </div>
          <!-- <div class="resultItem">
            <span>地址：</span>
            <div class="resultContent"> {{ 
            identifyResultData.addr || ''
            }} </div>
          </div> -->
          <div class="resultItem">
            <span>证件号码：</span>
            <div class="resultContent"> {{ 
            identifyResultData.num || ''
            }} </div>
          </div>
          <div class="resultItem">
            <span>国籍：</span>
            <div class="resultContent"> {{ 
            identifyResultData.nation || ''
            }} </div>
          </div>
          <div class="resultItem">
            <span>准驾车型‌：</span>
            <div class="resultContent"> {{ 
            identifyResultData.vehicle_type || ''
            }} </div>
          </div>
          <div class="resultItem">
            <span>初次领证日期‌：</span>
            <div class="resultContent"> {{ 
            identifyResultData.issue_date || ''
            }} </div>
          </div>
          <div class="resultItem">
            <span>有效期限开始：</span>
            <div class="resultContent"> {{ 
            identifyResultData.start_date || ''
            }} </div>
          </div>
          <div class="resultItem">
            <span>有效期限结束：</span>
            <div class="resultContent"> {{ 
            identifyResultData.end_date || ''
            }} </div>
          </div>
          <!-- <div class="resultItem">
            <span>发证单位：</span>
            <div class="resultContent"> {{ 
            identifyResultData.issue_organization || ''
            }} </div>
          </div> -->
        </div>
        <div class="idCard_regResult_control">
          <div class="repanel" @click="recognizeFn">
            <i class="icon_i re"></i>
            重新生成
          </div>
          <div class="copanel" @click="copyFn">
            <i class="icon_i co"></i>
            复制
          </div>
        </div>
      </div>
    </div>

    <bottomsBtns @recognizeFn="recognizeFn" />
  </div>
  <div class="loading" v-if="recognizeLoading"> 识别中,请稍等... </div>
</template>

<script setup>
import { Upload, Spin, message } from "ant-design-vue";
import { ref } from "vue";
import bottomsBtns from "./bottoms-btns-com.vue";
import { imageUp, imageIdentify } from '@/api/index'
import { useRouter, useRoute } from 'vue-router';
import goImageBack from './goImageBack.vue';

// const cardFrontLoading = ref(false)

// const cardBackLoading = ref(false)
const route = useRoute()
const router = useRouter()


// 上传中状态 点击
const uploadChange = (event, tag) => {
  // 确定哪个参数为判断标准
  // let judgeParam = tag == 'front' ? cardLoading : cardLoading
  // console.log(judgeParam.value, 'judgeParam.value')
  // let newval = ( tag == 'front' ? cardFrontLoading.value === true : cardBackLoading.value === true )
  if (cardLoading.value === true) {
    message.warning({
      key: "fileup",
      content: "有文件正在上传，请稍后再试！",
    });
    event.stopPropagation();
    return;
  }
}

const imageId = ref('0') // 页面级图片会话id
const imageDetailId = ref('0') // 正面图片id
// const imageDetailId_back = ref('0') // 背面图片id    // 最新会话以上参数为0； 更新后赋值后端返回参数 
const image_url = ref('') // 正面图片url
// const image_back_url = ref('') // 正面图片url
// 图片加载中
const cardLoading = ref(false)

const customRequest = (data) => {
  cardLoading.value = true;
  const file = data.file;
  const formData = new FormData();
  formData.append("file", file);
  formData.append("imageId", imageId.value);
  formData.append("imageType", 2); // 1身份证 2驾驶证 0通用图片
  formData.append("imageDetailId", imageDetailId.value);
  formData.append("side", 'face');
  // 将调用上传文件接口
  imageUp(formData).then(res => {
    cardLoading.value = false;
    if(res.code == 200) {
      message.success({
        key: "fileup",
        content: "文件上传成功",
      });

      imageId.value = res.data.imageId
      imageDetailId.value = res.data.imageDetailId
      image_url.value = res.data.imgUrl
    
      
    }
  })

  
}

const beforeUpload = file => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('请上传jpg或png格式的图片')
  }
  return isJpgOrPng;
};



const identifyResultData = ref({})
const recognizeLoading = ref(false)
const recognizeFn = () => {
  if(recognizeLoading.value) {
    return
  }
  // recognizeLoading.value = true
  // 调用识别接口
  if(!image_url.value) {
    message.warning({
      key: "fileup",
      content: "请先上传驾驶证正页",
    });
    return
  }
  // if(!image_back_url.value) {
  //   return
  // }    
  recognizeLoading.value = true
  // 调用识别接口
  imageIdentify({ imageId: imageId.value }).then(res => {
    recognizeLoading.value = false;
    if(res.code == 200) {
      identifyResultData.value = res.data

      message.success({
        key: "identify",
        content: "识别成功",
      });
      
    
    }
  })
}

const copyFn = () => {
  // 调用复制接口
  let text = `
  姓名：${identifyResultData.value.name || ''}
  性别：${identifyResultData.value.sex || ''}
  出生日期：${identifyResultData.value.birth_date || ''}
  证件号码：${identifyResultData.value.num || ''}
  国籍：${identifyResultData.value.nation || ''}
  准驾车型‌：${identifyResultData.value.vehicle_type || ''}
  初次领证日期‌：${identifyResultData.value.issue_date || ''}
  有效期限开始：${identifyResultData.value.start_date || ''}
  有效期限结束：${identifyResultData.value.end_date || ''}
  `
  text = text.replace(/^\n/, "");
  const save = function (e) {
    e.clipboardData.setData("text/plain", text);
    e.preventDefault(); // 阻止默认行为
  };
  document.addEventListener("copy", save, { once: true }); // 添加一个copy事件
  document.execCommand("copy"); // 执行copy方法
  // message.success("复制成功");
  message.success({
    key: "copy",
    content: "复制成功",
  });

}






</script>


<style lang="less">
.img_layloutcss {
  max-width: 1209px;
  width: 80%;
  padding-left: 108px;
  margin: 24px auto 0px;
  margin: 0px auto 0px;
  position: relative;
  padding-top: 24px;
}

.goback {
  position: absolute;
  top: 28px;
  left: 108px;
  color: #5E6772;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  cursor: pointer;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.80);
  box-shadow: 0 4px 29.2px 0 rgba(67, 72, 169, 0.13);
  backdrop-filter: blur(4.300000190734863px);
  padding: 16px;
}

.loading {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  background-color: #ffffff8c;
  display: flex;
  align-items: center;
  justify-content: space-around;
  color: #001aff;

}

.page_title {
  text-align: center;
  font-size: 42px;
  font-weight: 700;
  display: flex;
  justify-content: space-around;
  margin-bottom: 0px;
  line-height: normal;
  position: relative;
  span {
    background: linear-gradient(
      90deg,
      #0fd3ff 0.07%,
      #364afd 12.07%,
      #4c4efb 32.69%,
      #b457ff 99.85%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.page_desc {
  color: #8b8d98;
  text-align: center;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 5px;
  padding: 8px 0px 24px;
}

.idCardBox {
  display: flex;
  gap: 20px;
  .idCardImg {
    border-radius: 16px;
    border: 1.5px solid #FFF;
    background: linear-gradient(180deg, #E3E7FF 0%, #EAEEFF 100%);
    box-shadow: 0 4px 24px 0 rgba(129, 136, 255, 0.10);
    backdrop-filter: blur(4.300000190734863px);
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    justify-content: space-around;


    .ant-upload {
      display: block;
    }

  }
  
  .idCard_regResult {
    flex: 1;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.70);
    display: flex;
    padding: 16px;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    flex: 1 0 0;
    align-self: stretch;
    .idCard_regResult_content {
      padding: 16px;
      width: 100%;
      border-radius: 20px;
      background: rgba(255, 255, 255, 0.70);
    }

    .idCard_regResult_control {
      display: flex;
      justify-content: space-between;
      width: 100%;
      color: #8B8D98;
      > div {
        cursor: pointer;
      }
      .icon_i {
        cursor: pointer;
        display: inline-block;
        width: 18px;
        height: 18px;
        background-size: auto;
        opacity: 1;
        background-repeat: no-repeat;
        background-position: center center;
      }

      .repanel {
        display: flex;
        .re {
          background-image: url(@/assets/image/icon_regenerate.svg);
        }

        &:hover {
          opacity: 0.8;
        }
      }

      .copanel {
        display: flex;
        align-items: center;
        .co {
          background-image: url(@/assets/image/icon_copy.svg);
        }

        &:hover {
          opacity: 0.8;
        }
      }

    }
    
  }

  .resultItem {
    display: flex;
    width: 100%;
    font-size: 14px;
    color: #60646C;
    font-weight: 400;
    padding: 14px 0px;
    .resultContent {
      flex: 1;
      border-bottom: 1px solid #DBDBDB;
      text-align: left;
      padding-left: 10px;
      align-items: end;
    }
  }

  .idCardFrontImg, .idCardBackImg {
    background-color: #FFF;
    padding: 20px;
    border-radius: 20px;
    cursor: pointer;
    span {
      border-radius: 9.232px;
      border: 0.839px dashed #DBDBDB;
      display: inline-block;
      padding: 8px;
    }
    .imgDesc {
      color: #364AFD;
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      padding-top: 17px;
    }

  }
    .upImageBox {
      max-width: 100%;
      overflow: hidden;
      border-radius: 20px;
      img {
        max-width: 100%;
        min-height: 120px;
        /* // max-height: 320px; */
      }
    }
  
  /* .idCardBackImg {
    
  } */
}

.bottomBtns {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 0px;

  .identifyBtn {
    border-radius: 1rem;
    background: #3133FF;
    backdrop-filter: blur(4.300000190734863px);
    color: #FFF;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    padding: 16px 24px;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }


  }

  .moreBtn {
    color: #3133FF;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }


  }
}

</style>
