<template>
  <div class="pageControl">
    <div class="pageBox">
      <div class="pageUp" 
      :class="{'prevent': props.page == 1}" 
      @click="pageDown('minus')">
        <img src="@/assets/image/page_control/left.svg" />
        <img src="@/assets/image/page_control/leftCur.svg" />
      </div>
      <div class="page"><a-input @pressEnter="pagechange" @input="handleInput" v-model:value="pagenum" /> / {{ props.total }}</div>
      <div class="pageDown" 
      :class="{'prevent': props.page == props.total}" 
      @click="pageDown('add')">
        <img src="@/assets/image/page_control/right.svg" />
        <img src="@/assets/image/page_control/rightCur.svg" />
      </div>
    </div>
    <div class="line"></div>
    <div class="zoomBox">
      <div class="zoomOut" @click="zoomChange('in')">
        <img src="@/assets/image/page_control/zoomOut.svg" />
        <img src="@/assets/image/page_control/zoomOutCur.svg" />
      </div>

      <div class="zoomRate">{{ (props.scaleReate * 100).toFixed(0) }} %</div>

      <div class="zoomIn" @click="zoomChange('out')">
        <img src="@/assets/image/page_control/zoomIn.svg" />
        <img src="@/assets/image/page_control/zoomInCur.svg" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { Tag } from 'ant-design-vue';
import { ref, onMounted, nextTick, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  page: {
    type: Number,
    default: 0
  },
  total: {
    type: Number,
    default: 10
  },
  scaleReate: {
    type: Number,
    default: 1
  }

})

const emits = defineEmits(['turnPage', 'zoomfn'])

const pageDown = (tag) => {
  if(tag == 'minus') {
    // 页码减少
    if(props.page == 1) { return }
    let setPage = props.page - 1
    emits('turnPage', setPage)
  } else {
    // 页码增加
    if(props.page == props.total) { return }
    let setPage = props.page + 1
    emits('turnPage', setPage)
  }
}

const zoomChange = ( tag ) => {
  emits('zoomfn', tag)
}

const pagenum = ref(props.page)

watch(
  () => props.page, 
  ( val ) =>  {
  pagenum.value = val
})
const handleInput = (e) => {
  // 只允许数字输入（包括退格、删除、方向键等）
  pagenum.value = e.target.value.replace(/[^0-9]/g, '');
}

const pagechange = ( e ) => {
  console.log(e.target.value, 'pagechange')
  let num = e.target.value
  if (num > props.total) {
    pagenum.value = props.total
  } else if (num <= 0) {
    pagenum.value = 1
  }
  emits('turnPage', pagenum.value, 'auto')
}


</script>

<style lang="less">
.pageControl{
  display: flex; 
  margin: 12px auto 0px; 
  line-height: 32px;
  gap: 6px;
  align-items: center;
  padding: 2px 6px;
  background-color: #FFF;
  border-radius: 8px;

  .pageBox, .zoomBox { 
    display: flex; 
    align-items: center;
    gap: 8px;
  }
  .line {
    width: 1px;
    height: 90%;
    background-color: #EBEDF7;

  }

  .pageUp, .pageDown, .zoomIn, .zoomOut {
    cursor: pointer;
    img:first-child {
      display: block;
    }
    img:nth-child(2) {
      display: none;
    }
    &:hover {
      img:first-child {
        display: none;
      }
      img:nth-child(2) {
        display: block;
      }
    }
  }
  .pageUp, .pageDown, .zoomIn, .zoomOut {
    &.prevent {
      cursor: not-allowed;
      &:hover {
        img:first-child {
          display: block;
        }
        img:nth-child(2) {
          display: none;
        }
      }
    }
  }
  .zoomRate {
    // background-color: #F5F6F7; 
    border-radius: 8px; 
    font-size: 12px; 
    line-height: 20px; 
    color: #171a1f; 
    font-weight: 400;
    width: 35px;
    // padding: 6px 15px;
  }
  .page {
    font-weight: 400;
    input {
      display: inline;
      width: 35px;
      padding: 0px;
      border-radius: 5px;
      background-color: #e2e2e2;
      border: navajowhite;
      text-align: center;
    }
  }
}

</style>