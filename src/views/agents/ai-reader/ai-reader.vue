<template>
  <div class="ai-reader-container">
    <div class="header">
      AI阅读:支持解读文档内容,提炼核心要点,文章内容搜索,助您高效吸收信息!
    </div>
    <div class="content">
      <div class="spinwarp" v-if="pageloading">
        <Spin class="spinLoading" tip="文件切换中..."/>
      </div>
      <file-panel @fileChange="fileChange" @pdfViewChange="pdfViewChange" @pagePdfLoad="pagePdfLoad" :currentPage="currentPage" :fileId="currentFileId" :total="pageTotal" :pageList="pageList" :loadedPage="loadedPage" :robotobj="robotobj"></file-panel> 
      <file-content @pagePdfLoad="pagePdfLoad" :currentPage="currentPage" :fileId="currentFileId"  :fileName="fileName" :total="pageTotal" :pageList="pageList" :loadedPage="loadedPage" @pageChange="pageChange" @reLoadPage="reLoadPage"></file-content>
      <ai-relate-panel :fileId="currentFileId" ></ai-relate-panel>
    </div>
  </div>
</template>
<script setup>
import filePanel from './file-panel.vue'; // 引入文件面板组件
import fileContent from './file-content.vue'; // 引入文件内容组件
import aiRelatePanel from './ai-relate-panel.vue'; // 引入AI相关面板组件

import { ref, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import * as PDF from 'pdfjs-dist';
import PdfjsWorker from "pdfjs-dist/build/pdf.worker.entry";

import { requesetDocFileBlob } from '@/api/index'
import { isElementInViewport, viewPanel } from '@/utils/common.js'
import Bus from '@/utils/bus';
import { Spin } from 'ant-design-vue';

const router = useRouter()
const route = useRoute() // 使用 useRoute 获取当前路由信息
const robotId = ref(route.query.robotId)
const askType = ref(route.query.askType)
const robotobj = ref({
  'robotId': robotId.value,
  'askType': askType.value
})

// 点击切换文件时，整个页面的loading
const pageloading = ref(false)


const currentFileId = ref(0)    // pdfID
// 总页数
const pageTotal = ref(10)
const pageList = ref([])
const loadedPage = ref([])



// 根据页面获取对应页码数据
const getPdfDataByNum =  async (nowPage, fileId)  => {
  pageList.value[nowPage-1].loadedState = "loading" ;
  return new Promise((resolve, reject) => {
    requesetDocFileBlob({
      nowPage,
      fileId,
      noMsgTips: true // 请求后不提示错误
    }).then( res => {
      // console.log(res)
      let blob = window.URL.createObjectURL(res)
      // let blob = await res.arrayBuffer()
      // console.log(blob)
      // if (res.ret === 200) {
      loadedPage.value.push(nowPage * 1)
      // 只画一个框
      pageList.value[nowPage - 1]['data'] = blob // 显示 pdf的这一页
      pageList.value[nowPage - 1].loadedState = "loaded"
      setTimeout(() => {
        // console.log(pdfArr.list)
        resolve(blob)
      }, 1000)
      // }
    }).catch(e => {
      pageList.value[nowPage - 1].loadedState = "failed"
    })
  })
}
// 加载失败后，组件中触发重新加载
const reLoadPage = ( page ) => {
  getPdfDataByNum(page, currentFileId)
}

// 根据 fileId 获取当前文件的 总页数
const getPdfTotalPage = (fileId) => {
  return new Promise((resolve, reject) => {
    requesetDocFileBlob({
      nowPage: 1,
      fileId,
      getHeaders: true, // 请求后获取headers
      noMsgTips: true // 请求后不提示错误
    }).then(res => {
      resolve(res.responseHeaders.total * 1)
    })
  })
}



// 子组件触发，去调用对应页码数据。
const pagePdfLoad = async (pageItem) => {
  // 根据pageItem 获取 当前页的数据信息
  if(pageList.value[pageItem.page-1].loadedState == 'loading'){
    return

  }
  await getPdfDataByNum(pageItem.page, pageItem.fileId)


  // 将饭回来的数据包装成图片 赋值
  // pageList.value[pageItem.page-1].data = "赋值..."

  // loadedPage.value.push(pageItem.page * 1)

}


// 初始化pdf页码
const pageListInit = () => {
  loadedPage.value = []
  fragmentObj = [] // 标注数据标空
  // console.log(pageList.value.length, 'pageList.value')
  pageList.value.length = pageTotal.value
  for(let i=0; i < pageTotal.value; i++) {
    pageList.value[i] = {
      id: i, // pageid值
      data: null, // 页面数据
      page: i+1, // 当前页
      loadedState: 'preload' // 'preload'、 'loading'、 'loaded', 'failed'
    }
  }
}

// 切换文件
const fileName = ref('文件名称')
const fileChange = async (pdfItem) => {
  console.log(pdfItem, 'pdfItem...')
  const filepanel = document.getElementById('pdfPanel-'+1)
  const pdfviewpanel = document.getElementById('pdfPanelView-'+1)
  if(filepanel && pdfviewpanel) {
    viewPanel(filepanel, 'auto')
    viewPanel(pdfviewpanel, 'auto')
  }
  pageloading.value = true
  // 先要清空原有两组件内容
  pageTotal.value = await getPdfTotalPage(pdfItem.messageFileId)
  currentPage.value = 1
  // 最新的pageTotal获取之后，再将传递到子组件的id修改。
  currentFileId.value = pdfItem.messageFileId
  fileName.value = pdfItem.fileName
  pageListInit()
  pageloading.value = false

}

// 当前页
const currentPage = ref(1)
// 文件预览模块 滚动到对应页同步主组件
const pageChange = ( page ) => {
  // console.log('pagechange=====', page)
  // 如果直接跳页很多，直接滚动到对应位置
  let rollstyle = ''
  if(Math.abs(page-currentPage.value) > 6) {
    rollstyle = 'auto'
  } else {
    rollstyle = 'smooth'
  }
  currentPage.value = page
  const toViewPanel = document.getElementById('pdfPanel-'+page)
  if(!isElementInViewport(toViewPanel)) {
    // 滚动到可视范围
    viewPanel(toViewPanel, rollstyle)
  } 
}

// 切换页码toview / 点击右侧列表切换预览页
const pdfViewChange = (page) => {
  const toViewPanel = document.getElementById('pdfPanelView-'+page)
  currentPage.value = page
  // 滚动到可视范围
  viewPanel(toViewPanel, 'auto')
  // nextTick(() => 
  //   {
      // drawRect(page)
  //   }
  // )
}



// 调用drawRect之前先对 page、 fileId、 areaslist赋值。
// 之后进行drawRect。
// 或者scaleReate变化后进行重绘直接调用drawRect。

// 检测结果中，点击检查后
// 赋值page， fileId，areas数组


// const areas = {
// 'page': 25, 
// 'page_height': 651.97,
//  'page_width': 476.22,
//  'total_pages': 240,
//  'coordinate': [
//     [249.45, 254.15, 405.35, 262.65],
//     [81.26, 255.75, 229.6, 264.25],
//    [257.01, 268.15, 405.35, 276.65], 
//    [70.87, 329.65, 222.93, 338.15], 
//    [78.43, 345.54, 232.44, 354.04], 
//    [78.43, 359.54, 163.43, 368.04], 
//    [70.87, 375.43, 154.93, 383.93], 
//    [78.43, 391.33, 232.27, 399.83],
//     [78.43, 405.33, 232.33, 413.83], 
//     [78.43, 419.33, 120.93, 427.83],
//      [70.87, 435.22, 232.27, 443.72],
//       [78.43, 449.22, 232.45, 457.72], 
//       [78.43, 463.22, 95.43, 471.72], 
//       [78.43, 479.11, 232.33, 487.61], 
//       [78.43, 493.11, 180.43, 501.61], 
//       [78.43, 509.0, 232.39, 517.5], 
//       [78.43, 524.89, 232.44, 533.39], 
//       [78.43, 538.89, 95.43, 547.39], 
//       [78.43, 554.78, 232.14, 563.28], 
//       [78.43, 568.78, 188.93, 577.28]
//   ]
// }

// currentFileId
// 高亮的片段，包含n页
const fragmentControl = ( fragmentObj ) => {
  // drawRect(fragmentObj[0], currentFileId.value)
    // 删除 历史 标注框
  let dom = document.querySelectorAll('div[class="annotion-box"]')
  if (dom.length) {
    Array.from(dom).map(item => {
      item.remove()
    })
  }

  fragmentObj.forEach((elItem, idx) => {
    // (function(elItem) {
    if(idx == 0) {
      (function(elItem) {
        console.log('elItem.page', elItem.page)
        let pageDiv = document.getElementById('pdfPanelView-'+elItem.page)
        viewPanel(pageDiv, 'auto')
      })(elItem)
    }
    drawRect(elItem, currentFileId.value)
    // })(elItem)
  });
}


// 根据检索结果高亮画框 部分逻辑
const drawRect = async (elItem, fileId) => {
  // debugger
  let page = elItem.page
  const pdfHeight = elItem.page_height // 595.28  // 841.89
  const pdfWidth = elItem.page_width // 419.53  // 595.276
  const arealist = elItem.coordinate 
      // [[215.43, 289.92, 342.93, 298.42], [215.43, 307.66, 293.43, 320.66], [39.69, 363.89, 130.69, 376.89], [39.68, 384.48, 201.18, 392.98], [39.68, 398.48, 201.26, 406.98], [39.69, 412.48, 158.69, 420.98], [39.69, 428.37, 201.19, 436.87], [343.13, 442.37, 377.01, 447.37], [39.69, 444.27, 191.32, 452.77], [215.43, 452.66, 376.97, 461.16], [39.69, 460.16, 106.75, 468.66], [215.43, 466.66, 317.43, 475.16], [39.69, 476.05, 189.23, 484.55], [215.43, 482.55, 377.01, 491.05], [39.69, 491.94, 106.75, 500.44], [223.0, 496.55, 282.5, 505.05], [215.43, 512.44, 377.01, 520.94], [223.0, 526.44, 282.5, 534.94]]

  let pageDiv = document.getElementById('pdfPanelView-'+page)
  // debugger
  // currentFileId.value == fileId && 
  // debugger
  if( loadedPage.value.indexOf(page * 1) > -1 ) {
    pageDiv = document.getElementById('pdfPanelView-'+page)
    // viewPanel(pageDiv, 'auto') 在最初定位位置，滚动
  } else {
    await getPdfDataByNum(page, fileId)
  }
  nextTick(() => {
    // debugger
    // 需要重新赋值，如果之前未取到防止为空情况。
    pageDiv = document.getElementById('pdfPanelView-'+page)
    const pdfPageDiv = pageDiv.getElementsByClassName('page')[0]


    // const page = area[4]
    // const area = [81.26-28.34, 255.75-28.34, 229.6-28.34, 264.25-28.34],
    // const area = [81.26, 255.75, 229.6, 264.25]
    
  
    // debugger


    for( let i=0; i<arealist.length; i++) {
      let area = arealist[i]
      const x0 = area[0]
      const y0 = area[1]
      const x1 = area[2]
      const y1 = area[3]
      // debugger
      //根据缩放比例 换算坐标位置
      // console.log(window.gbotShadowDom.querySelector)
      const pageHeight = (pdfPageDiv.clientHeight).toFixed(1) // 841.89
      const pageWidth = (pdfPageDiv.clientWidth).toFixed(1) // 595.276 

      // console.log(pageHeight,pageWidth,'pageHeight, pageWidth')

      // const pdfHeight = area[5]  // 841.89
      // const pdfWidth = area[6]  // 595.276
      const x0_ = (x0 === 0 && x1 === 0 && y0 === 0 && y1 === 0) ? 0 : x0 / pdfWidth * pageWidth // 如果坐标都为0 则定位到左上角 
      const x1_ = x1 / pdfWidth * pageWidth
      // const y0_ = (x0 === 0 && x1 === 0 && y0 === 0 && y1 === 0) ? 0 : (pdfHeight - y1) / pdfHeight * pageHeight // 如果坐标都为0 则定位到左上角 
      const y0_ = (x0 === 0 && x1 === 0 && y0 === 0 && y1 === 0) ? 0 : y0 / pdfHeight * pageHeight // 如果坐标都为0 则定位到左上角 
      const y1_ = y1 / pdfHeight * pageHeight
      const w_ = x1_ - x0_
      const h_ = y1_ - y0_
     
      // console.log('坐标起始点：', `${x0_},${y1_}`, '宽度：', `${w_}`, '高度：', `${h_}`)
      const posDiv = pageDiv.querySelector('div[class="textLayer"]')
      const coverDiv = document.createElement('div');
      coverDiv.setAttribute('style', `width:${w_}px;height:${h_}px;background:#499cff;position:absolute;top:${y0_}px;left:${x0_}px`)
      coverDiv.setAttribute("class", `annotion-box`)
      // console.log(div, posDiv)
      posDiv.insertBefore(coverDiv, posDiv.firstChild)
    }

    // setTimeout(() => {
    // 跳转到 标注出
  })
  

}

let fragmentObj = []
onMounted(() => {
  Bus.$on('coordinateHighlight', (fragmentObjIn) => {
    // 收到重绘指令、使用存储数据、重绘当前页
    fragmentObjIn ? fragmentObj = fragmentObjIn : ''
    
    fragmentControl(fragmentObj)
  })
})





</script>
<style lang="less" scoped>
.ai-reader-container {
  display: flex;
  flex-direction: column;
  padding: 12px 12px 12px 118px;
  overflow: hidden;
  text-align: left;
  height: 100%;
  .header {
    color: #1C2024;
    font-size: 18px;
    padding: 0px 0px 8px;
    font-weight: 400;
  }
  .content {
    flex: 1;
    display: flex;
    gap: 8px;
    overflow: hidden;
  }
  .spinwarp {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    background-color: #ffffff50;
    z-index: 9;
  }
  .spinLoading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

  



</style>




