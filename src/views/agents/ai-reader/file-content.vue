<template>
  <div class="fileContent">
    <p class="fileTit">{{ props.fileName }}</p>
    <div class="fileContentBody">
      <!-- 文件内容将在这里显示 -->
      <div class="pdfPage" v-if="props.pageList.length > 0" :page-data="JSON.stringify({ page: item.page, fileId: props.fileId })" v-for="(item, index) in props.pageList" :key="index" :id="'pdfPanelView-'+(index+1)">
        <pdfview v-if="item.data" :dataUrl="item.data" :page="item.page" panelName="pdfPanelView" :scaleReate="scaleReate" />
        <div class="loadingSpan" v-else-if="item.loadedState !== 'failed'">加载中...</div>
        <div class="loadedFailed" v-else-if="item.loadedState == 'failed'">加载失败<div style="color: blue;" @click="reLoadedPage(item.page)">重新加载</div></div>
      </div>
      

    </div>
    <page-control 
      @turnPage="turnPage" 
      :page="props.currentPage" 
      :total="props.total" 
      :scaleReate="scaleReate" 
      @zoomfn="zoomfn"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, defineProps, defineEmits, watch } from 'vue';
import { Spin } from 'ant-design-vue';
import pageControl from './page-control.vue'
import pdfview from '@/components/com-pdf.vue'
import { viewPanel } from '@/utils/common'


const emits = defineEmits(['fileChange', 'pagePdfLoad', 'pageChange', 'reLoadPage'])

const props = defineProps({
  fileId: {
    type: Number,
    default: 0
  },
  total: {
    type: Number,
    default: 10
  },
  pageList: {
    type: Array,
    default: () => []
  },
  loadedPage: {
    type: Array,
    default: () => []
  },
  currentPage: {
    default: 1
  },
  fileName: {
    type: String,
    default: ''
  }

})

watch(
  () => props.fileId,
  (val) => {
    // 获取dom、绑定dom事件
    nextTick(() => {
      // createBindObserve()
      unbindAllObservers()
    })
  }
)


// 使用 Map 存储 dom 和 observer 的对应关系
const domObserverMap = new Map();

// 绑定 dom 方法
const bindObserver = (dom) => {
  let observer = new IntersectionObserver(
    (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          let { page, fileId } = JSON.parse(dom.getAttribute('page-data'));
          emits('pageChange', page);
          if (props.loadedPage.indexOf(page * 1) > -1) {
            return;
          }
          emits('pagePdfLoad', { page, fileId });
        }
      });
    },
    { threshold: 0.5 }
  );
  observer.observe(dom);
  domObserverMap.set(dom, observer);
};

// 创建绑定
const createBindObserve = () => {
  let domPanelList = document.querySelectorAll(".pdfPage");
  if (domPanelList.length) {
    Array.from(domPanelList).map(ele => {
      bindObserver(ele);
    });
  }
};

// 解绑所有观察
const unbindAllObservers = () => {
  domObserverMap.forEach((observer, dom) => {
    observer.disconnect();
  });
  domObserverMap.clear();
  // 添加监听
  createBindObserve()
};


// 翻页
const turnPage = (page, tag) => {
  const pdfviewpanel = document.getElementById('pdfPanelView-'+page)
  emits('pageChange', page)
  let rollstyle = tag ? tag : 'smooth'
  viewPanel(pdfviewpanel, rollstyle)
}

// const pdfPointer = ref(null); // 设置选中的pdf对象
// const pageNum = ref(0); // 前文件页码
// const pageTotal = ref(props.total); // 当前文件总页数

// 设置缩放比例
const scaleReate = ref(1)

// 放大
let d_val = 0.1
const zoomfn = ( type ) => {
  if(type == 'in') {
    scaleReate.value -= d_val
  } else {
    scaleReate.value += d_val
  }
}

const reLoadedPage = ( page ) => {
  emits('reLoadPage', page)
}



</script>

<style scoped lang="less">
.fileContent {
  // flex: 1;
  background: rgba(255, 255, 255, 0.50);
  border: 1px solid #fff;
  border-radius: 8px;
  color: #1C2024;
  line-height: 32px;
  font-weight: bolder;
  text-align: center;
  padding: 12px 0px 0px;
  display: flex;
  flex-direction: column;
  // width: 700px;
  width: calc(100% - 760px);
  .fileTit {
    padding-bottom: 5px;
    font-size: 16px;
    text-align: left;
    padding-left: 10px;
  }
  .fileContentBody {
    flex: 1;
    // background: #eee;
    overflow: auto;
    .pdfPage {
      // width: 100%;
      min-height: 100%;
      padding-bottom: 5px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      // overflow: hidden;
    }
  }
}
@media (max-width: 1500px) {
  .fileContent {
    width: calc(100% - 730px)!important;
  }
}


</style>