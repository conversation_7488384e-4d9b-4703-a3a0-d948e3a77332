<template>
  <div class="filePanel">
    <div class="agent-search">
      <input
        class="agent-search-input"
        type="text"
        placeholder="输入关键字，回车查找"
        v-model="search_word" 
        @keyup.enter="handleSearch"
      />
      <img @click="handleSearch" src="@/assets/image/icon_his_search.svg" class="search-icon" />
    </div>
    <Upload
      name="file"
      :show-upload-list="false"
      :customRequest="customRequest"
      ref="uploadRef"
      class="uploadspan"
      :before-upload="beforeUpload"
    >
      <!-- shortcut file 不要修改 -->
      <div class="fileUpBtn">
        <span>上传文档</span>
        <span>PDF/Word/TXT</span>
        <div class="loading-file" v-if="fileupLoading" >
          <LoadingOutlined/>
        </div>
      </div>
    </Upload>

    <!-- <div class="pageReader" style="display: none;"> -->
      <!-- <div class="pageReaderBorder"> -->
        <!-- <span>阅读网页</span> -->
      <!-- </div> -->
    <!-- </div> -->

    <div class="line" style="display: none;"></div>

    <div class="historyTitle">历史文件列表</div>
    <div class="historyPanel">
      <div class="historyList"> 
        <div class="historyItem" :class="{ cur: item.messageFileId == fileId }" @click="fileChecked(item)" v-for="(item, idx) in fileList" :key="idx">
          <span><img src="@/assets/image/pdfTag.svg" class="historyIcon" /></span>
          <span :title="item.fileName">{{ item.fileName }}</span>
          <template v-if="item.upType !== 1">
          <Popconfirm
            title="确定删除此文件嘛?"
            ok-text="确定"
            cancel-text="取消"
            @confirm="deleteFn('1', item)"
            @cancel="deleteFn('0', item)"
          >
            <span @click.stop><img src="@/assets/image/delbtn.svg" class="historyDelete" /></span>
          </Popconfirm>
          </template>
          <span v-else></span>
          <!-- <span @click="deleteFn(item)"><img src="@/assets/image/delbtn.svg" class="historyDelete" /></span> -->
        </div>
      </div>
      <div class="historyViewClass" ref="historyView">
        <!-- <Spin tip="Loading..." v-if="pdfviewListLoading">
          <a-alert
            message="Alert message title"
            description="Further details about the context of this alert."
          ></a-alert>
        </Spin> -->
        <div class="historyViewItem pdfPanelThum" :class="{'viewCur': props.currentPage == item.page}" v-if="props.pageList.length > 0" :page-data="JSON.stringify({ page: item.page, fileId: fileId })" v-for="(item, index) in props.pageList" :key="index" :id="'pdfPanel-'+(index+1)" @click="domIntoView(item.page)">
          <pdfview v-if="item.data" :dataUrl="item.data" :page="item.page" panelName="pdfPanelThum" />
          <div v-else>加载中...</div>
          <span class="page_number"> {{ item.page }} </span>
        </div>
        <!-- <div class="loadingMore" v-if="viewPageNum < props.total && !pdfviewListLoading">
          加载中...
        </div> -->
      </div>
      

    </div>

  </div>
</template>
<script setup>
import * as PDF from 'pdfjs-dist';
import PdfjsWorker from "pdfjs-dist/build/pdf.worker.entry";
import { ref, onMounted, nextTick, defineEmits, defineProps, watch, onUnmounted } from 'vue';
import { Spin, message, Popconfirm, Upload } from 'ant-design-vue';
import { readFileUp, readFileList, readDeleteFile } from '@/api/index'
import pdfview from '@/components/com-pdf.vue'
import {
  LoadingOutlined
} from '@ant-design/icons-vue';
import { useRoute, useRouter } from "vue-router";
import Bus from "@/utils/bus.js";

const route = useRoute() // 使用 useRoute 获取当前路由信息
const robotId = ref(route.query.robotId)
const askType = ref(route.query.askType)




let url = ref('http://oss-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/gbot/deep-seek/2025-05-10/1234741013485870377026.pdf');
let url1 = ref('http://oss-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/gbot/deep-seek/2025-05-10/1236869457042843929214.pdf')
const fileList = ref([{ id: 146, url: '' },{ id: 166, url: '' },{ id: 188, url: '' }])
const fileListForSearch = ref([])

const emits = defineEmits(['fileChange', 'pagePdfLoad', 'pdfViewChange'])

const props = defineProps({
  robotobj: {
    type: Object
  },
  fileId: {
    type: Number,
    default: 0
  },
  total: {
    type: Number,
    default: 0
  },
  pageList: {
    type: Array,
    default: () => []
  },
  loadedPage: {
    type: Array,
    default: () => []
  },
  currentPage: {
    default: 1
  }
})

watch(
  () => props.fileId,   // 父组件更新完成之后，跟更新fileId，子组件更具此ID更新任务。
  (val) => {
    // 获取dom、绑定dom事件
    nextTick(() => {
      // createBindObserve()
      unbindAllObservers()
    })
  }
)


const fileupLoading = ref(false)
const customRequest = (data) => {
  fileupLoading.value = true;
  const file = data.file;
  const formData = new FormData();
  formData.append("file", file);
  formData.append("robotId", props.robotobj.robotId+'');
  console.log(formData,'formData=====')
  // Bus.$emit("preFile", file);

  readFileUp(formData)
    .then((res) => {
      fileupLoading.value = false;
      if (res.code === 200) {
        message.success({
          key: "fileup",
          content: "文件上传成功",
        });
        // 上传成功获取文件列表数据
        getFileListFn()


      } else {
        
      }
    })
    .catch((e) => {
      fileupLoading.value = false;
    });
};

const beforeUpload = file => {
  const isLt10M = file.size / 1024 / 1024 <= 10;
  if (!isLt10M) {
    message.error('上传文件必须小于10M!');
  }
  return isLt10M;
};










// 让元素滚动到视口内
// const toPage = ref(1)

const domIntoView = (page) => {
  // toPage.value = page
  emits("pdfViewChange", page)
}


// 假设 domObserverMap 是一个 Map
const domObserverMap = new Map();

const bindObserver = (dom) => {
  // 为每个DOM元素创建一个计时器引用
  let timer = null;

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // 当元素进入视口时，设置一个延迟计时器
          timer = setTimeout(() => {
            try {
              const { page, fileId } = JSON.parse(dom.getAttribute('page-data'));
              if (props.loadedPage.includes(page * 1)) {
                return;
              }
              // 触发加载事件
              emits('pagePdfLoad', { page, fileId });
            } catch (error) {
              console.error('解析 page-data 属性失败:', error);
            }
          }, 500); // 延迟500毫秒后执行加载
        } else {
          // 当元素离开视口时，清除计时器
          if (timer) {
            clearTimeout(timer);
            timer = null;
          }
        }
      });
    },
    { threshold: 0.1 } // 进入视口的阈值
  );
  
  observer.observe(dom);
  
  // 存储映射关系，现在存储一个对象包含 observer 和 timer
  domObserverMap.set(dom, { observer, timer });
};

// 注意：如果后续需要取消观察，需要从 domObserverMap 中取出 observer 和 timer 进行清理
const unbindObserver = (dom) => {
  if (domObserverMap.has(dom)) {
    const { observer, timer } = domObserverMap.get(dom);
    if (timer) {
      clearTimeout(timer);
    }
    observer.unobserve(dom);
    domObserverMap.delete(dom);
  }
};

/**
 * 创建并绑定所有需要观察的 DOM 元素
 */
const createBindObserve = () => {
  const domPanelList = document.querySelectorAll(".pdfPanelThum");
  if (domPanelList.length) {
    Array.from(domPanelList).forEach(ele => {
      bindObserver(ele);
    });
  }
};

/**
 * 解绑所有观察者
 */
const unbindAllObservers = () => {
  domObserverMap.forEach((value, dom) => {
    value.observer.disconnect(); // 正确访问 observer
  });
  domObserverMap.clear();
  // 解绑后绑定对应dom元素
  createBindObserve()
};

const fileId = ref(0); // 当前文件id

// 文件列表点击事件
const fileChecked = (item) => {
  if(canAsk.value) {
    // 当前文件再次点击无效
    if(item.messageFileId == fileId.value) return
    fileId.value = item.messageFileId;
    emits('fileChange', item)
  } else {
    message.warning("回答生成中，请稍后切换！")
  }
  
}

// 上一页面传递fileId过来值
let pagePassFileID = ""
const filterIdChecked = ( fileId ) => {
  fileList.value.filter(item => {
    if(item.messageFileId == fileId) {
      fileChecked(item)
    }
  })
}


const getFileListFn = ( tag ) => {
  let params = {
    'robotId': props.robotobj.robotId
  }
  readFileList(params).then(res => {
    if(res.code == 200) {
      fileList.value = res.data
      fileListForSearch.value = res.data
      nextTick(() => {
        // 如果 local 中有fileId，则选中该fileId。并在选择后删除该文件存储。
        if(localStorage.getItem('aiReaderFileId')) {
          pagePassFileID = localStorage.getItem('aiReaderFileId')
          filterIdChecked(pagePassFileID)
          localStorage.removeItem('aiReaderFileId')
        } else { // 文件列表加载完成之后、列表的第一条数据选中
          tag?
          fileChecked(fileList.value[0])
          :''
        }
      })
    }
  })

}

const deleteFn = ( tag, item ) => {
  if(tag === '1') {
    // 调用删除文件的API
    readDeleteFile({fileId: item.messageFileId}).then(res => {
      if(res.code == 200) {
        if(item.messageFileId == props.fileId) { // 删除当前选中的ID，则获取列表后选中第一条
          getFileListFn(true)
        } else {
          getFileListFn()
        }
        
      }
    }).catch(error => {
      
    });
  } else {
    message.warning('取消操作')
  }
}

const search_word = ref()
const handleSearch = ( event ) => {
  fileList.value = fileListForSearch.value.filter(item => {
    // console.log(search_word.value, 'keywords')
    // console.log(item.fileName.indexOf(search_word.value), 'item.fileName')
    return item.fileName.indexOf(search_word.value) > -1
  })

}

const canAsk = ref(true)
onMounted(() => {
  // 获取文件列表
  getFileListFn( true )

  Bus.$on('canAsk', (val) => {
    canAsk.value = val
  })

});

onUnmounted (() => {
  Bus.$off('canAsk')
})



</script>

<style lang="less" scoped>
.ant-spin-nested-loading {
  width: 100%;
  height: 100%;
}
.filePanel {
  width: 260px;
  border-radius: 8px;
  display: flex;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;

  border: 2px solid #FFF;
  background: rgba(255, 255, 255, 0.80);
  box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
  backdrop-filter: blur(4.300000190734863px);

  
}
.agent-search {
  border-radius: 8px;
  border: 1px solid #e0e1e6;
  background: #fff;
  box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.05);
  backdrop-filter: blur(4.300000190734863px);
  padding: 8px 16px;
  width: 100%;
  display: flex;
  input {
    border: none;
    outline: none;
    flex: 1;
    height: 18px;
    line-height: 18px;
    color: #8b8d98;
  }
  img {
    width: 18px;
    height: 18px;
  }
}
.uploadspan {
  width: 100%;
  /deep/ .ant-upload {
    display: block;
  }
}
.fileUpBtn {
  padding: 8px 16px;
  border-radius: 8px;
  background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
  justify-content: space-around;
  cursor: pointer;
  position: relative;
  .loading-file {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.5); // 半透明背景
    border-radius: 8px; // 与按钮的圆角保持一致
    z-index: 10; // 确保加载动画在按钮内容之上
  }
  span:first-child {
    color: white !important;
    font-size: 14px;
    font-weight: bolder;
    line-height: 14px;
    padding-bottom: 3px;
  }
  span:nth-child(2) {
    color: white !important;
    font-size: 12px;
    line-height: 12px;
  }
}
.pageReader {
  border-radius: 8px;
  background-image: linear-gradient(to right, #36C0D2 2px, #3651FF, #AB60F1);
  width: 100%;
  text-align: center;
  padding: 1px;
  .pageReaderBorder {
    background-color: #fff;
    border-radius: 7px;
    padding: 8px 8px;
  }
  span{
    display: inline-block;
    background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 14px;
    font-weight: bolder;
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: #CDCED6;
}
.historyTitle {
  font-size: 14px;
  color: #1C2024;
  font-weight: bolder;
}
.historyPanel {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1;
  overflow: hidden;
  gap: 12px;
  .historyList {
    overflow-y: auto;
    max-height: 40%;
    .historyItem {
      padding: 8px 5px;
      border-radius: 8px;
      background-color: #fff;
      display: flex;
      margin-bottom: 4px;
      align-items: center;
      cursor: pointer;

      span:first-child {
        padding: 3px 5px;
        margin-right: 8px;
        img {
          display: block;
        }
      }

      span:nth-child(2) {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      
      span:last-child{
        width: 24px;
        height: 24px;
        border-radius: 8px;
        display: block;
        visibility: hidden;
        img { display: block; }
      }
      &:hover{
        background-color: #F0F0F3;
        span:last-child {
          visibility: visible;
        }
        span:last-child :hover {
          background-color: #E0E1E6;
          border-radius: 8px;
        }
      }
      .historyDelete {
        align-self: flex-end;
      }
    }
    .historyItem.cur {
      background: linear-gradient(69deg, rgba(61, 187, 215, 0.20) 2.55%, rgba(54, 74, 253, 0.20) 32.25%, rgba(183, 110, 241, 0.20) 120.42%);
      span:last-child :hover {
        background-color: #fff;
        border-radius: 8px;
      }
      span:nth-child(2) {
        opacity: var(--13, 1);
        background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

  }
  .historyViewClass {
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
    .historyViewItem {
      background-color: #EBEDF7;
      height: 118px;
      flex-shrink: 0;
      border-radius: 8px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: space-around; 
      border: 1px solid #EBEDF7;
      &.viewCur {
        // background-color: #ffffff1c;
        border: 1px solid #705bf7;
      }
    }
    .pdfPanelThum {
      cursor: pointer;
      position: relative;
      // padding: 3px;
      .page_number {
        position: absolute;
        padding: 2px 5px;
        bottom: 5px;
        right: 5px;
      }
    }
    .loadingMore {
      width: 100%;
      text-align: center;
      color: #364AFD;
    }

  }
}

@media (max-width: 1500px) {
  .filePanel {
    width: 230px;
  }
}

</style>