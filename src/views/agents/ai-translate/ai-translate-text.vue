<template>
  <div class="translate_layloutcss ai-translate-main-container">
    <div class="translate-header">
      <div class="back-btn" @click="goBack">
        <span class="back-arrow">←</span>
        <span>返回</span>
      </div>
      <h1>翻译结果</h1>
    </div>

    <!-- 翻译信息 -->
    <div class="translate-info">
      <div class="translate-type">
        <span class="type-label">翻译类型：</span>
        <span class="type-value">{{ getTranslateTypeName(translateType) }}</span>
      </div>
      <div class="translate-languages">
        <span class="lang-label">语言：</span>
        <span class="lang-value">{{ fromLanguage }} → {{ toLanguage }}</span>
      </div>
      <div class="translate-model">
        <span class="model-label">翻译模型：</span>
        <span class="model-value">{{ modelType === 'bmodal' ? '大模型翻译' : '小牛翻译' }}</span>
      </div>
    </div>

    <!-- 翻译结果展示 -->
    <div class="translate-result-container">
      <!-- 加载状态 -->
      <div class="loading-container" v-if="isTranslating">
        <Spin size="large" />
        <div class="loading-text">正在翻译中，请稍候...</div>
      </div>

      <!-- 翻译结果 -->
      <div class="translate-result" v-else-if="translateResult">
        <div class="result-header">
          <h3>翻译完成</h3>
          <div class="result-actions">
            <span class="copy-btn" @click="copyResult">复制译文</span>
            <span class="download-btn" @click="downloadResult" v-if="translateType === 'file' || translateType === 'video'">下载结果</span>
            <span class="new-translate-btn" @click="newTranslate">新建翻译</span>
          </div>
        </div>

        <div class="result-content">
          <!-- 原文 -->
          <div class="original-text" v-if="originalText">
            <div class="text-header">
              <div class="text-label">原文</div>
              <div class="text-info">{{ getOriginalTextInfo() }}</div>
            </div>
            <div class="text-content">{{ originalText }}</div>
          </div>

          <!-- 译文 -->
          <div class="translated-text">
            <div class="text-header">
              <div class="text-label">译文</div>
              <div class="text-info">{{ getTranslatedTextInfo() }}</div>
            </div>
            <div class="text-content">{{ translateResult }}</div>
          </div>
        </div>
      </div>

      <!-- 翻译失败 -->
      <div class="translate-error" v-else-if="translateError">
        <div class="error-icon">⚠️</div>
        <div class="error-message">{{ translateError }}</div>
        <div class="error-actions">
          <span class="retry-btn" @click="retryTranslate">重试</span>
          <span class="back-btn" @click="goBack">返回</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Spin, message } from 'ant-design-vue'

const route = useRoute()
const router = useRouter()

// 翻译参数
const translateType = ref('')
const fromLanguage = ref('')
const toLanguage = ref('')
const modelType = ref('')
const sourceContent = ref('')
const fileName = ref('')

// 翻译状态
const isTranslating = ref(false)
const translateResult = ref('')
const originalText = ref('')
const translateError = ref('')

// 获取翻译类型名称
const getTranslateTypeName = (type) => {
  const typeMap = {
    'text': '文本翻译',
    'file': '文件翻译',
    'voice': '语音翻译',
    'video': '视频翻译'
  }
  return typeMap[type] || '未知类型'
}

// 获取原文信息
const getOriginalTextInfo = () => {
  if (translateType.value === 'file') {
    return fileName.value ? `文件：${fileName.value}` : '文件内容'
  } else if (translateType.value === 'voice') {
    return fileName.value ? `音频文件：${fileName.value}` : '语音识别结果'
  } else if (translateType.value === 'video') {
    return fileName.value ? `视频文件：${fileName.value}` : '视频字幕内容'
  }
  return `${originalText.value.length} 字符`
}

// 获取译文信息
const getTranslatedTextInfo = () => {
  return `${translateResult.value.length} 字符`
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 新建翻译
const newTranslate = () => {
  router.push('/ai-translate')
}

// 复制结果
const copyResult = async () => {
  if (!translateResult.value) {
    message.warning('没有翻译结果可复制')
    return
  }

  try {
    await navigator.clipboard.writeText(translateResult.value)
    message.success('复制成功')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = translateResult.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    message.success('复制成功')
  }
}

// 下载结果
const downloadResult = () => {
  if (!translateResult.value) {
    message.warning('没有翻译结果可下载')
    return
  }

  const blob = new Blob([translateResult.value], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `翻译结果_${new Date().getTime()}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  message.success('下载成功')
}

// 重试翻译
const retryTranslate = () => {
  translateError.value = ''
  startTranslate()
}

// 开始翻译
const startTranslate = async () => {
  isTranslating.value = true
  translateError.value = ''

  try {
    // 根据翻译类型调用不同的API
    let result
    if (translateType.value === 'text') {
      result = await translateText({
        text: sourceContent.value,
        from: fromLanguage.value,
        to: toLanguage.value,
        model: modelType.value
      })
    } else if (translateType.value === 'file') {
      result = await translateFile({
        content: sourceContent.value,
        fileName: fileName.value,
        from: fromLanguage.value,
        to: toLanguage.value,
        model: modelType.value
      })
    } else if (translateType.value === 'voice') {
      result = await translateVoice({
        content: sourceContent.value,
        from: fromLanguage.value,
        to: toLanguage.value,
        model: modelType.value
      })
    } else if (translateType.value === 'video') {
      result = await translateVideo({
        content: sourceContent.value,
        from: fromLanguage.value,
        to: toLanguage.value,
        model: modelType.value
      })
    }

    translateResult.value = result.translatedText
    originalText.value = result.originalText || sourceContent.value
  } catch (error) {
    translateError.value = error.message || '翻译失败，请重试'
    console.error('Translation error:', error)
  } finally {
    isTranslating.value = false
  }
}

// API 调用函数（模拟实现）
const translateText = async (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        translatedText: `[${params.to}] 这是翻译后的文本内容: ${params.text}`,
        originalText: params.text
      })
    }, 2000)
  })
}

const translateFile = async (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        translatedText: `[${params.to}] 这是翻译后的文件内容...`,
        originalText: '原始文件内容...'
      })
    }, 3000)
  })
}

const translateVoice = async (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        translatedText: `[${params.to}] 这是翻译后的语音内容...`,
        originalText: params.content
      })
    }, 2500)
  })
}

const translateVideo = async (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        translatedText: `[${params.to}] 这是翻译后的视频字幕...`,
        originalText: '原始视频字幕...'
      })
    }, 4000)
  })
}

// 页面初始化
onMounted(() => {
  // 从路由参数获取翻译信息
  translateType.value = route.query.type || ''
  fromLanguage.value = route.query.from || ''
  toLanguage.value = route.query.to || ''
  modelType.value = route.query.model || ''
  sourceContent.value = route.query.content || ''
  fileName.value = route.query.fileName || ''

  // 开始翻译
  if (translateType.value && sourceContent.value) {
    startTranslate()
  } else {
    translateError.value = '翻译参数不完整'
  }
})
</script>

<style lang="less" scoped>
.translate_layloutcss {
  max-width: 1265px;
  width: 80%;
  padding: 24px 0px 0px 108px;
  margin: 0 auto;
  position: relative;
  padding-top: 24px;
}

.translate-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .back-btn {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-right: 24px;
    padding: 8px 16px;
    border-radius: 8px;
    background: #F0F0F3;
    transition: all 0.3s ease;

    &:hover {
      background: #E0E0E3;
    }

    .back-arrow {
      font-size: 16px;
      margin-right: 8px;
    }

    span {
      font-size: 14px;
      color: #1C2024;
    }
  }

  h1 {
    color: #1C2024;
    font-size: 32px;
    line-height: 42px;
    margin: 0;
  }
}

.translate-info {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: #F8F9FA;
  border-radius: 12px;

  > div {
    display: flex;
    align-items: center;

    .type-label, .lang-label, .model-label {
      font-size: 14px;
      color: #6B7280;
      margin-right: 8px;
    }

    .type-value, .lang-value, .model-value {
      font-size: 14px;
      color: #1C2024;
      font-weight: 500;
    }
  }
}

.translate-result-container {
  background: #fff;
  border-radius: 20px;
  padding: 20px;
  min-height: 400px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;

  .loading-text {
    margin-top: 16px;
    font-size: 16px;
    color: #6B7280;
  }
}

.translate-result {
  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #1C2024;
    }

    .result-actions {
      display: flex;
      gap: 12px;

      .copy-btn, .download-btn, .new-translate-btn {
        padding: 8px 16px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;
      }

      .copy-btn {
        background: #F0F0F3;
        color: #1C2024;
        &:hover {
          background: #E0E0E3;
        }
      }

      .download-btn {
        background: #364AFD;
        color: white;
        &:hover {
          background: #2A3BCC;
        }
      }

      .new-translate-btn {
        background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
        color: white;
        &:hover {
          opacity: 0.9;
        }
      }
    }
  }

  .result-content {
    .original-text, .translated-text {
      margin-bottom: 24px;

      .text-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .text-label {
          font-size: 16px;
          font-weight: 600;
          color: #1C2024;
        }

        .text-info {
          font-size: 12px;
          color: #6B7280;
        }
      }

      .text-content {
        padding: 16px 20px;
        background: #F8F9FA;
        border-radius: 12px;
        font-size: 14px;
        line-height: 1.8;
        color: #1C2024;
        white-space: pre-wrap;
        min-height: 120px;
      }
    }

    .translated-text .text-content {
      background: linear-gradient(135deg, rgba(54, 74, 253, 0.05) 0%, rgba(171, 96, 241, 0.05) 100%);
      border: 1px solid rgba(54, 74, 253, 0.1);
    }
  }
}

.translate-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;

  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .error-message {
    font-size: 16px;
    color: #FF4D4F;
    margin-bottom: 24px;
    text-align: center;
  }

  .error-actions {
    display: flex;
    gap: 12px;

    .retry-btn, .back-btn {
      padding: 8px 16px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .retry-btn {
      background: #364AFD;
      color: white;
      &:hover {
        background: #2A3BCC;
      }
    }

    .back-btn {
      background: #F0F0F3;
      color: #1C2024;
      &:hover {
        background: #E0E0E3;
      }
    }
  }
}
</style>