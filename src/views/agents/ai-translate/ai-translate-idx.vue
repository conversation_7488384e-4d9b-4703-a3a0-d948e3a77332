<template>
  <div class="translate_layloutcss ai-translate-container">
    <h1>Al翻译：支持文本、文档、语音、视频文件的在线多语言跚译</h1>
    <div class="ai-translate-types">
      <div class="ai-translate-type-item" :class="{ cur: currentTranslateType === 'text' }" @click="changeTranslateType('text')">
        <div class="item-icon">
          <img src="@/assets/image/ai-translate/txt_tab.svg" />
        </div>
        <div class="item-txt">
          文本翻译
        </div>
      </div>
      <div class="ai-translate-type-item" :class="{ cur: currentTranslateType === 'file' }" @click="changeTranslateType('file')">
        <div class="item-icon">
          <img src="@/assets/image/ai-translate/file_tab.svg" />
        </div>
        <div class="item-txt">
          文件翻译
        </div>
      </div>
      <div class="ai-translate-type-item" :class="{ cur: currentTranslateType === 'voice' }" @click="changeTranslateType('voice')">
        <div class="item-icon">
          <img src="@/assets/image/ai-translate/voice_tab.svg" />
        </div>
        <div class="item-txt">
          语音翻译
        </div>
      </div>
      <div class="ai-translate-type-item" :class="{ cur: currentTranslateType === 'video' }" @click="changeTranslateType('video')">
        <div class="item-icon">
          <img src="@/assets/image/ai-translate/video_tab.svg" />
        </div>
        <div class="item-txt">
          视频翻译
        </div>
      </div>
    </div>
    <div class="send-box-warp">
      <div class="send-box-control">
        <div class="model-control" :class="{'rightPanel': modalVal=='xnmodal'}">
          <div class="cur"></div>
          <div class="modalC bmodal" @click="modalChange('bmodal')">
            大模型翻译
          </div>
          <div class="modalC xnmodal" @click="modalChange('xnmodal')">
            小牛翻译
          </div>
        </div>
        <div class="detect-control">
          <Select
            ref="select"
            v-model:value="in_language"
          >
            <Select.Option value="auto">自动检测</Select.Option>
            <Select.Option value="Chinese">中文</Select.Option>
            <Select.Option value="English">英语</Select.Option>
            <Select.Option value="Japanese">日语</Select.Option>
            <Select.Option value="Korean">韩语</Select.Option>
            <Select.Option value="French">法语</Select.Option>
            <Select.Option value="German">德语</Select.Option>
            <Select.Option value="Spanish">西班牙语</Select.Option>
            <Select.Option value="Russian">俄语</Select.Option>
            <Select.Option value="Arabic">阿拉伯语</Select.Option>
          </Select>
          <span class="switch-icon" @click="switchLanguages"><img src="@/assets/image/ai-translate/switch.svg" /></span>
          <Select
            ref="select"
            v-model:value="out_language"
            @change="handleChange"
          >
            <Select.Option value="English">英语</Select.Option>
            <Select.Option value="Chinese">中文</Select.Option>
            <Select.Option value="Japanese">日语</Select.Option>
            <Select.Option value="Korean">韩语</Select.Option>
            <Select.Option value="French">法语</Select.Option>
            <Select.Option value="German">德语</Select.Option>
            <Select.Option value="Spanish">西班牙语</Select.Option>
            <Select.Option value="Russian">俄语</Select.Option>
            <Select.Option value="Arabic">阿拉伯语</Select.Option>
          </Select>
        </div>
      </div>

      <!-- 文本翻译输入区域 -->
      <div class="mainControl">
        <div class="msgsendbox" v-if="currentTranslateType === 'text'">
          <comSend
            pageName="home"
            :canAsk="canAsk"
            @sendMsg="handleTextTranslate"
            ref="textSendRef"
          />
        </div>

        <!-- 文件翻译上传区域 -->
        <div class="file-upload-area" v-if="currentTranslateType === 'file'">
          <Upload
            name="file"
            :show-upload-list="false"
            :customRequest="handleFileUpload"
            accept=".pdf,.doc,.docx,.txt,.ppt,.pptx"
            :disabled="fileUploading"
          >
            <div class="upload-box">
              <div class="upload-icon">
                <img src="@/assets/image/ai-translate/file_tab.svg" />
              </div>
              <div class="upload-text">
                <div class="upload-title">点击上传文件</div>
                <div class="upload-desc">支持 PDF、Word、TXT、PPT 格式文件</div>
              </div>
              <div class="upload-loading" v-if="fileUploading">
                <Spin />
              </div>
            </div>
          </Upload>
        </div>

        <!-- 语音翻译区域 -->
        <div class="voice-translate-area" v-if="currentTranslateType === 'voice'">
          <Upload
            name="audio"
            :show-upload-list="false"
            :customRequest="handleVoiceUpload"
            accept=".mp3,.wav,.m4a,.aac,.flac,.ogg"
            :disabled="voiceUploading"
          >
            <div class="upload-box voice-upload">
              <div class="upload-icon">
                <img src="@/assets/image/ai-translate/voice_tab.svg" />
              </div>
              <div class="upload-text">
                <div class="upload-title">点击上传语音文件</div>
                <div class="upload-desc">支持 MP3、WAV、M4A、AAC 等格式音频文件</div>
              </div>
              <div class="upload-loading" v-if="voiceUploading">
                <Spin />
              </div>
            </div>
          </Upload>
        </div>

        <!-- 视频翻译区域 -->
        <div class="video-translate-area" v-if="currentTranslateType === 'video'">
          <Upload
            name="video"
            :show-upload-list="false"
            :customRequest="handleVideoUpload"
            accept=".mp4,.avi,.mov,.wmv,.flv,.mkv"
            :disabled="videoUploading"
          >
            <div class="upload-box video-upload">
              <div class="upload-icon">
                <img src="@/assets/image/ai-translate/video_tab.svg" />
              </div>
              <div class="upload-text">
                <div class="upload-title">点击上传视频</div>
                <div class="upload-desc">支持 MP4、AVI、MOV 等格式视频文件</div>
              </div>
              <div class="upload-loading" v-if="videoUploading">
                <Spin />
              </div>
            </div>
          </Upload>
        </div>
      </div>

      <div class="bottom-control">
        <div class="translate_up">
          <span>翻译增强：</span>
          <div class="termbasebox">
            <Select
              ref="select"
              v-model:value="termbase"
            >
              <Select.Option value="">选择术语库</Select.Option>
              <Select.Option value="dp">底盘</Select.Option>
              <Select.Option value="cs">车身</Select.Option>
              <Select.Option value="fd">发动机</Select.Option>
              <Select.Option value="lt">轮胎</Select.Option>
              <Select.Option value="zs">智能系统</Select.Option>
            </Select>
          </div>
          <div class="botC add_termbase">
            <span>新增术语库</span>
          </div>
          <div class="botC view_termbase">
            <span>查看历史术语库</span>
          </div>
        </div>
        <div class="basic_config">
          <span>基础配置：</span>
          <div class="checkbox-group">
            <Checkbox v-model:checked="basicConfig.keepFormat">保留文档格式</Checkbox>
            <Checkbox v-model:checked="basicConfig.termMark">术语库采纳标记</Checkbox>
            <Checkbox v-model:checked="basicConfig.bilingualCheck">双语对照检查</Checkbox>
          </div>
        </div>
      </div>
      <div class="bottom-btn-box">
        <a href="javascript:;" class="goTranslate">开始翻译</a>
      </div>


    </div>


  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Select, Upload, Spin, message, Checkbox } from 'ant-design-vue'
import comSend from '@/components/com-send-translate.vue'

const router = useRouter()

// 翻译类型
const currentTranslateType = ref('text')
const changeTranslateType = (type) => {
  currentTranslateType.value = type
}

// 模型选择
const modalVal = ref('bmodal')
const modalChange = (val) => {
  modalVal.value = val
}

// 语言选择
const in_language = ref('auto')
const out_language = ref('English')
const handleChange = () => {
  // 语言变化处理

}

// 基础配置
const basicConfig = ref({
  keepFormat: false,      // 保留文档格式
  termMark: false,        // 术语库采纳标记
  bilingualCheck: false   // 双语对照检查
})

// 语言切换功能
const switchLanguages = () => {
  if (in_language.value === 'auto') {
    message.warning('自动检测语言无法切换')
    return
  }
  const temp = in_language.value
  in_language.value = out_language.value
  out_language.value = temp
}

// 翻译相关状态
const canAsk = ref(true)

// 文本翻译
const textSendRef = ref(null)
const handleTextTranslate = (text) => {
  if (!text.trim()) {
    message.warning('请输入要翻译的内容')
    return
  }

  // 跳转到翻译结果页面
  router.push({
    path: '/ai-translate-text',
    query: {
      type: 'text',
      from: in_language.value,
      to: out_language.value,
      model: modalVal.value,
      content: text
    }
  })
}

// 文件翻译相关
const fileUploading = ref(false)

const handleFileUpload = (data) => {
  const file = data.file
  const allowedTypes = ['.pdf', '.doc', '.docx', '.txt', '.ppt', '.pptx']
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase()

  if (!allowedTypes.includes(fileExtension)) {
    message.error('不支持的文件格式')
    return
  }

  if (file.size > 10 * 1024 * 1024) { // 10MB限制
    message.error('文件大小不能超过10MB')
    return
  }

  router.push({
    path: '/ai-translate-text',
    query: {
      type: 'file',
      from: in_language.value,
      to: out_language.value,
      model: modalVal.value,
      content: 'file_content', // 这里应该是文件内容或文件ID
      fileName: file.name
    }
  })
  
}

// 语音翻译相关
const voiceUploading = ref(false)

const handleVoiceUpload = (data) => {
  const file = data.file
  const allowedTypes = ['.mp3', '.wav', '.m4a', '.aac', '.flac', '.ogg']
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase()

  if (!allowedTypes.includes(fileExtension)) {
    message.error('不支持的音频格式')
    return
  }

  if (file.size > 50 * 1024 * 1024) { // 50MB限制
    message.error('音频文件大小不能超过50MB')
    return
  }

  // 跳转到翻译结果页面
  router.push({
    path: '/ai-translate-text',
    query: {
      type: 'voice',
      from: in_language.value,
      to: out_language.value,
      model: modalVal.value,
      content: 'voice_content', // 这里应该是音频内容或音频ID
      fileName: file.name
    }
  })
}

// 视频翻译相关
const videoUploading = ref(false)

const handleVideoUpload = (data) => {
  const file = data.file
  const allowedTypes = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase()

  if (!allowedTypes.includes(fileExtension)) {
    message.error('不支持的视频格式')
    return
  }

  if (file.size > 100 * 1024 * 1024) { // 100MB限制
    message.error('视频文件大小不能超过100MB')
    return
  }

  // 跳转到翻译结果页面
  router.push({
    path: '/ai-translate-text',
    query: {
      type: 'video',
      from: in_language.value,
      to: out_language.value,
      model: modalVal.value,
      content: 'video_content', // 这里应该是视频内容或视频ID
      fileName: file.name
    }
  })
}

// 选择术语库
const termbase = ref('')

</script>

<style lang="less" scoped>
.translate_layloutcss {
  max-width: 1265px;
  width: 80%;
  padding: 42px 0px 0px 108px;
  margin: 0 auto;
  position: relative;
  padding-top: 42px;
}

h1 {
  color: #1C2024;
  font-size: 32px;
  text-align: left;
  line-height: 42px;
}

.ai-translate-types {
  display: flex;
  margin-bottom: 24px;
  .ai-translate-type-item {
    display: flex;
    background-color: #fff;
    border-radius: 16px;
    padding: 12px 16px;
    align-items: center;
    margin-right: 16px;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
    .item-icon {
      margin-right: 6px;
      width: 28px;
      height: 28px;
      text-align: center;
      line-height: 28px;
    }

    .item-txt {
      line-height: 28px;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    &.cur {
      .item-txt {
        background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}

.send-box-warp {
  display: flex;
  padding: 20px;
  flex-direction: column;
  gap: 16px;
  align-self: stretch;
  border-radius: 20px;
  background-color: #fff;
  color: #1C2024;
}

.send-box-control {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .model-control {
    display: flex;
    border-radius: 12px;
    background: #F0F0F3;
    gap: 4px;
    position: relative;
    div {
      padding: 8px 16px;
      border-radius: 12px;
      transition: all 0.3s linear;
      cursor: pointer;
      &.cur {
        background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        position: absolute;
        height: 100%;
        left: 0px;
        top: 0px;
        width: 102px;
        z-index: 1;
        transition: all 0.3s ease-in-out;
      }
      &.bmodal {
        width: 102px;
        z-index: 2;
        color: #fff;
      }
      &.xnmodal {
        z-index: 2;
        width: 88px;
        color: #1C2024;
      }
    }
  }
  .rightPanel {
    .cur {
      left: 106px !important;
      width: 88px !important;
    }
    .bmodal {
      color: #1C2024 !important;
    }
    .xnmodal {
      color: #fff !important;
    }
  }
  .detect-control {
    display: flex;
    padding: 8px 16px;
    justify-content: center;
    align-items: center;
    gap: 20px;
    border-radius: 12px;
    background: #F0F0F3;
    .switch-icon {
      cursor: pointer;
      transition: transform 0.3s ease;
      &:hover {
        transform: scale(1.1);
      }
    }
    /deep/.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      height: 21px !important;
      line-height: 21px;
    }
    /deep/ .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border: none !important;
      background: none !important;
    }
    /deep/ .ant-select {
      line-height: 21px;
      outline: none !important;
      width: 96px;
    }
    /deep/ .ant-select-single:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input {
      height: 21px;
    }
    /deep/ .ant-select-single .ant-select-selector .ant-select-selection-item, .ant-select-single .ant-select-selector .ant-select-selection-placeholder{
      line-height: 21px;
    }
    /deep/ .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
      // box-shadow: 0 0 0 2px rgb(221 208 255 / 24%)
      box-shadow: none;
    }
    // /deep/ .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {

    // }
    
  }

}
/deep/ .ant-select-dropdown {
  border-radius: 6px !important;
  overflow: hidden;
}
/deep/ .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: #e6ebff !important;
}

// 文件上传区域样式
.file-upload-area, .video-translate-area, .voice-translate-area {
  .upload-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px 16px;
    border-radius: 16px;
    border: 1px dashed #D9D9D9;
    background: rgba(255, 255, 255, 0.80);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    height: 191px;

    &:hover {
      border-color: #364AFD;
      background: rgba(54, 74, 253, 0.05);
    }

    .upload-icon {
      width: 26px;
      height: 26px;
      margin-bottom: 8px;
      img {
        width: 100%;
        height: 100%;
      }
    }

    .upload-text {
      text-align: center;
      .upload-title {
        font-size: 14px;
        // font-weight: 500;
        color: blue;
        // margin-bottom: 8px;
      }
      .upload-desc {
        font-size: 14px;
        color: #8B8D98;
      }
    }

    .upload-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }


}

// 底部区域样式调整
.bottom-control {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .translate_up {
    display: flex;
    align-items: center;
    gap: 8px;

    span {
      font-size: 14px;
      color: #1C2024;
      font-weight: 700;
    }
    .termbasebox {
      /deep/.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
        height: 37px !important;
        line-height: 37px;
        padding: 8px 0px;
      }
      /deep/ .ant-select:not(.ant-select-customize-input) .ant-select-selector {
        border: none !important;
        background: none !important;
      }
      /deep/ .ant-select {
        line-height: 21px;
        outline: none !important;
        width: 96px;
      }
      /deep/ .ant-select-single:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input {
        height: 21px;
      }
      /deep/ .ant-select-single .ant-select-selector .ant-select-selection-item, .ant-select-single .ant-select-selector .ant-select-selection-placeholder{
        line-height: 21px;
      }
      /deep/ .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
        // box-shadow: 0 0 0 2px rgb(221 208 255 / 24%)
        box-shadow: none;
      }
      padding: 0px 16px;
      border-radius: 12px;
      background: #F0F0F3;
    }
    .botC {
      border-radius: 12px;
      background: linear-gradient(69deg, rgba(61, 187, 215, 0.10) 2.55%, rgba(54, 74, 253, 0.10) 32.25%, rgba(183, 110, 241, 0.10) 120.42%);
      display: flex;
      padding: 8px 16px;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      gap: 4px;
      span {
        display: block;
        width: 100%;
        height: 100%;
        background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 14px;
        font-weight: 600;
      }
      &:hover {
        opacity: 0.8;
      }
    }
  }

  .basic_config {
    display: flex;
    align-items: center;
    gap: 12px;

    span {
      font-size: 14px;
      color: #1C2024;
      font-weight: 700;
    }

    .checkbox-group {
      display: flex;
      gap: 8px;
    }

    /deep/ .ant-checkbox-wrapper {
      font-size: 14px;
      color: #1C2024;
    }

    /deep/ .ant-checkbox-checked .ant-checkbox-inner {
      background-color: #364AFD;
      border-color: #364AFD;
    }

    /deep/ .ant-checkbox:hover .ant-checkbox-inner {
      border-color: #364AFD;
    }

    /deep/ .ant-checkbox + span {
      padding-left: 5px;
      padding-right: 5px;
    }
  }
}
.bottom-btn-box {
  display: flex;
  justify-content: flex-end;
  .goTranslate {
    color: #FFF;
    font-size: 14px;
    font-weight: 700;
    display: flex;
    width: 196px;
    height: 56px;
    padding: 0 16px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
    &:hover {
      opacity: 0.8;
    }
  }
}




@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(54, 74, 253, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(54, 74, 253, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(54, 74, 253, 0);
  }
}


// 使用场景展示区域样式
.use-cases-section {
  margin-top: 60px;
  padding: 40px 0;
  background: linear-gradient(135deg, rgba(54, 74, 253, 0.02) 0%, rgba(171, 96, 241, 0.02) 100%);
  border-radius: 20px;

  .cases-header {
    text-align: center;
    margin-bottom: 40px;

    h2 {
      font-size: 28px;
      font-weight: 600;
      color: #1C2024;
      margin-bottom: 12px;
    }

    p {
      font-size: 16px;
      color: #6B7280;
      margin: 0;
    }
  }

  .cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    padding: 0 20px;

    .case-item {
      background: #fff;
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      transition: all 0.3s ease;
      border: 1px solid rgba(54, 74, 253, 0.1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0px 6px 25px rgba(54, 74, 253, 0.12);
        border-color: rgba(54, 74, 253, 0.3);
      }

      .case-icon {
        width: 40px;
        height: 40px;
        margin: 0 auto 12px;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .case-content {
        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #1C2024;
          margin-bottom: 6px;
        }

        p {
          font-size: 13px;
          color: #6B7280;
          line-height: 1.5;
          margin: 0;
        }
      }
    }
  }
}
/deep/ .ant-upload.ant-upload-select {
  display: block;
}

// 响应式设计
@media (max-width: 768px) {
  .translate-features-section {
    margin-top: 40px;
    padding: 30px 0;

    .features-header {
      margin-bottom: 30px;

      h2 {
        font-size: 24px;
      }

      p {
        font-size: 14px;
      }
    }

    .features-grid {
      grid-template-columns: 1fr;
      gap: 16px;

      .feature-card {
        padding: 20px;
      }
    }
  }

  .use-cases-section {
    margin-top: 40px;
    padding: 30px 0;

    .cases-header {
      margin-bottom: 30px;

      h2 {
        font-size: 24px;
      }

      p {
        font-size: 14px;
      }
    }

    .cases-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      padding: 0 16px;

      .case-item {
        padding: 16px;
      }
    }
  }
}

@media (max-width: 480px) {
  .use-cases-section .cases-grid {
    grid-template-columns: 1fr;
  }
}

</style>
