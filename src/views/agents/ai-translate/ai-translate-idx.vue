<template>
  <div class="translate_layloutcss ai-translate-container">
    <h1>Al翻译：支持文本、文档、语音、视频文件的在线多语言跚译</h1>
    <div class="ai-translate-types">
      <div class="ai-translate-type-item" :class="{ cur: currentTranslateType === 'text' }" @click="changeTranslateType('text')">
        <div class="item-icon">
          <img src="@/assets/image/ai-translate/txt_tab.svg" />
        </div>
        <div class="item-txt">
          文本翻译
        </div>
      </div>
      <div class="ai-translate-type-item" :class="{ cur: currentTranslateType === 'file' }" @click="changeTranslateType('file')">
        <div class="item-icon">
          <img src="@/assets/image/ai-translate/file_tab.svg" />
        </div>
        <div class="item-txt">
          文件翻译
        </div>
      </div>
      <div class="ai-translate-type-item" :class="{ cur: currentTranslateType === 'voice' }" @click="changeTranslateType('voice')">
        <div class="item-icon">
          <img src="@/assets/image/ai-translate/voice_tab.svg" />
        </div>
        <div class="item-txt">
          语音翻译
        </div>
      </div>
      <div class="ai-translate-type-item" :class="{ cur: currentTranslateType === 'video' }" @click="changeTranslateType('video')">
        <div class="item-icon">
          <img src="@/assets/image/ai-translate/video_tab.svg" />
        </div>
        <div class="item-txt">
          视频翻译
        </div>
      </div>
    </div>
    <div class="send-box-warp">
      <div class="send-box-control">
        <div class="model-control" :class="{'rightPanel': modalVal=='xnmodal'}">
          <div class="cur"></div>
          <div class="modalC bmodal" @click="modalChange('bmodal')">
            大模型翻译
          </div>
          <div class="modalC xnmodal" @click="modalChange('xnmodal')">
            小牛翻译
          </div>
        </div>
        <div class="detect-control">
          <Select
            ref="select"
            v-model:value="in_language"
          >
            <Select.Option value="auto">自动检测</Select.Option>
            <Select.Option value="Chinese">中文</Select.Option>
            <Select.Option value="English">英语</Select.Option>
            <Select.Option value="Japanese">日语</Select.Option>
            <Select.Option value="Korean">韩语</Select.Option>
            <Select.Option value="French">法语</Select.Option>
            <Select.Option value="German">德语</Select.Option>
            <Select.Option value="Spanish">西班牙语</Select.Option>
            <Select.Option value="Russian">俄语</Select.Option>
            <Select.Option value="Arabic">阿拉伯语</Select.Option>
          </Select>
          <span class="switch-icon" @click="switchLanguages"><img src="@/assets/image/ai-translate/switch.svg" /></span>
          <Select
            ref="select"
            v-model:value="out_language"
            @change="handleChange"
          >
            <Select.Option value="English">英语</Select.Option>
            <Select.Option value="Chinese">中文</Select.Option>
            <Select.Option value="Japanese">日语</Select.Option>
            <Select.Option value="Korean">韩语</Select.Option>
            <Select.Option value="French">法语</Select.Option>
            <Select.Option value="German">德语</Select.Option>
            <Select.Option value="Spanish">西班牙语</Select.Option>
            <Select.Option value="Russian">俄语</Select.Option>
            <Select.Option value="Arabic">阿拉伯语</Select.Option>
          </Select>
        </div>
      </div>

      <!-- 文本翻译输入区域 -->
      <div class="msgsendbox" v-if="currentTranslateType === 'text'">
        <comSend
          pageName="translate"
          :canAsk="canAsk"
          @sendMsg="handleTextTranslate"
          ref="textSendRef"
        />
      </div>

      <!-- 文件翻译上传区域 -->
      <div class="file-upload-area" v-if="currentTranslateType === 'file'">
        <Upload
          name="file"
          :show-upload-list="false"
          :customRequest="handleFileUpload"
          accept=".pdf,.doc,.docx,.txt,.ppt,.pptx"
          :disabled="fileUploading"
        >
          <div class="upload-box">
            <div class="upload-icon">
              <img src="@/assets/image/ai-translate/file_tab.svg" />
            </div>
            <div class="upload-text">
              <div class="upload-title">点击上传文件</div>
              <div class="upload-desc">支持 PDF、Word、TXT、PPT 格式文件</div>
            </div>
            <div class="upload-loading" v-if="fileUploading">
              <Spin />
            </div>
          </div>
        </Upload>
      </div>

      <!-- 语音翻译区域 -->
      <div class="voice-translate-area" v-if="currentTranslateType === 'voice'">
        <div class="voice-recorder">
          <div class="recorder-controls">
            <div class="record-btn" :class="{ recording: isRecording }" @click="toggleRecording">
              <img :src="isRecording ? '@/assets/image/mic_pause_icon.svg' : '@/assets/image/sendbox/mic.svg'" />
            </div>
            <div class="record-text">
              {{ isRecording ? '录音中...' : '点击开始录音' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 视频翻译区域 -->
      <div class="video-translate-area" v-if="currentTranslateType === 'video'">
        <Upload
          name="video"
          :show-upload-list="false"
          :customRequest="handleVideoUpload"
          accept=".mp4,.avi,.mov,.wmv,.flv,.mkv"
          :disabled="videoUploading"
        >
          <div class="upload-box video-upload">
            <div class="upload-icon">
              <img src="@/assets/image/ai-translate/video_tab.svg" />
            </div>
            <div class="upload-text">
              <div class="upload-title">点击上传视频</div>
              <div class="upload-desc">支持 MP4、AVI、MOV 等格式视频文件</div>
            </div>
            <div class="upload-loading" v-if="videoUploading">
              <Spin />
            </div>
          </div>
        </Upload>
      </div>

      <!-- 翻译结果显示区域 -->
      <div class="translate-result" v-if="translateResult">
        <div class="result-header">
          <h3>翻译结果</h3>
          <div class="result-actions">
            <span class="copy-btn" @click="copyResult">复制</span>
            <span class="download-btn" @click="downloadResult" v-if="currentTranslateType === 'file'">下载</span>
          </div>
        </div>
        <div class="result-content">
          <div class="original-text" v-if="originalText">
            <div class="text-label">原文</div>
            <div class="text-content">{{ originalText }}</div>
          </div>
          <div class="translated-text">
            <div class="text-label">译文</div>
            <div class="text-content">{{ translateResult }}</div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Select, Upload, Spin, message } from 'ant-design-vue'
import comSend from '@/components/com-send-translate.vue'

// 翻译类型
const currentTranslateType = ref('text')
const changeTranslateType = (type) => {
  currentTranslateType.value = type
  // 清空之前的结果
  translateResult.value = ''
  originalText.value = ''
}

// 模型选择
const modalVal = ref('bmodal')
const modalChange = (val) => {
  modalVal.value = val
}

// 语言选择
const in_language = ref('auto')
const out_language = ref('English')
const handleChange = () => {
  // 语言变化处理
}

// 语言切换功能
const switchLanguages = () => {
  if (in_language.value === 'auto') {
    message.warning('自动检测语言无法切换')
    return
  }
  const temp = in_language.value
  in_language.value = out_language.value
  out_language.value = temp
}

// 翻译相关状态
const canAsk = ref(true)
const translating = ref(false)
const translateResult = ref('')
const originalText = ref('')

// 文本翻译
const textSendRef = ref(null)
const handleTextTranslate = async (text) => {
  if (!text.trim()) {
    message.warning('请输入要翻译的内容')
    return
  }

  originalText.value = text
  translating.value = true
  canAsk.value = false

  try {
    // 调用翻译API
    const result = await translateText({
      text: text,
      from: in_language.value,
      to: out_language.value,
      model: modalVal.value
    })
    translateResult.value = result.translatedText
  } catch (error) {
    message.error('翻译失败，请重试')
    console.error('Translation error:', error)
  } finally {
    translating.value = false
    canAsk.value = true
  }
}

// 文件翻译相关
const fileUploading = ref(false)

const handleFileUpload = async (data) => {
  const file = data.file
  const allowedTypes = ['.pdf', '.doc', '.docx', '.txt', '.ppt', '.pptx']
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase()

  if (!allowedTypes.includes(fileExtension)) {
    message.error('不支持的文件格式')
    return
  }

  if (file.size > 10 * 1024 * 1024) { // 10MB限制
    message.error('文件大小不能超过10MB')
    return
  }

  fileUploading.value = true

  try {
    // 上传文件到服务器并直接翻译
    const formData = new FormData()
    formData.append('file', file)

    // 这里应该调用实际的文件上传和翻译API
    const result = await translateFileContent({
      file: file,
      from: in_language.value,
      to: out_language.value,
      model: modalVal.value
    })

    translateResult.value = result.translatedContent
    originalText.value = result.originalContent
    message.success('文件翻译完成')
  } catch (error) {
    message.error('文件翻译失败')
    console.error('File translation error:', error)
  } finally {
    fileUploading.value = false
  }
}

// 语音翻译相关
const isRecording = ref(false)

const toggleRecording = () => {
  if (isRecording.value) {
    stopRecording()
  } else {
    startRecording()
  }
}

const startRecording = () => {
  isRecording.value = true
  // 这里应该集成语音识别功能
  // 可以参考现有的 com-mic 组件
  message.info('开始录音...')
}

const stopRecording = () => {
  isRecording.value = false
  // 停止录音并获取识别结果并直接翻译
  message.info('录音结束')
}

// 视频翻译相关
const videoUploading = ref(false)

const handleVideoUpload = async (data) => {
  const file = data.file
  const allowedTypes = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase()

  if (!allowedTypes.includes(fileExtension)) {
    message.error('不支持的视频格式')
    return
  }

  if (file.size > 100 * 1024 * 1024) { // 100MB限制
    message.error('视频文件大小不能超过100MB')
    return
  }

  videoUploading.value = true

  try {
    // 上传视频到服务器并直接翻译
    const formData = new FormData()
    formData.append('video', file)

    // 这里应该调用实际的视频上传和翻译API
    const result = await translateVideoContent({
      file: file,
      from: in_language.value,
      to: out_language.value,
      model: modalVal.value
    })

    translateResult.value = result.translatedSubtitles
    originalText.value = result.originalSubtitles
    message.success('视频翻译完成')
  } catch (error) {
    message.error('视频翻译失败')
    console.error('Video upload error:', error)
  } finally {
    videoUploading.value = false
  }
}

// 结果操作功能
const copyResult = async () => {
  if (!translateResult.value) {
    message.warning('没有翻译结果可复制')
    return
  }

  try {
    await navigator.clipboard.writeText(translateResult.value)
    message.success('复制成功')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = translateResult.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    message.success('复制成功')
  }
}

const downloadResult = () => {
  if (!translateResult.value) {
    message.warning('没有翻译结果可下载')
    return
  }

  const blob = new Blob([translateResult.value], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `翻译结果_${new Date().getTime()}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  message.success('下载成功')
}

// API 调用函数（这些需要根据实际后端API进行实现）
const translateText = async (params) => {
  // 模拟API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        translatedText: `[${params.to}] 这是翻译后的文本内容: ${params.text}`
      })
    }, 2000)
  })
}

const translateFileContent = async (params) => {
  // 模拟API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        originalContent: '原始文件内容...',
        translatedContent: `[${params.to}] 这是翻译后的文件内容...`
      })
    }, 3000)
  })
}

const translateVideoContent = async (params) => {
  // 模拟API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        originalSubtitles: '原始字幕内容...',
        translatedSubtitles: `[${params.to}] 这是翻译后的字幕内容...`
      })
    }, 5000)
  })
}

</script>

<style lang="less" scoped>
.translate_layloutcss {
  max-width: 1265px;
  width: 80%;
  padding: 24px 0px 0px 108px;
  margin: 0 auto;
  position: relative;
  padding-top: 24px;
}

h1 {
  color: #1C2024;
  font-size: 32px;
  text-align: left;
  line-height: 42px;
}

.ai-translate-types {
  display: flex;
  margin-bottom: 24px;
  .ai-translate-type-item {
    display: flex;
    background-color: #fff;
    border-radius: 16px;
    padding: 12px 16px;
    align-items: center;
    margin-right: 16px;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
    .item-icon {
      margin-right: 6px;
      width: 28px;
      height: 28px;
      text-align: center;
      line-height: 28px;
    }

    .item-txt {
      line-height: 28px;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    &.cur {
      .item-txt {
        background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}

.send-box-warp {
  display: flex;
  padding: 20px;
  flex-direction: column;
  gap: 16px;
  align-self: stretch;
  border-radius: 20px;
  background-color: #fff;
  color: #1C2024;
}

.send-box-control {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .model-control {
    display: flex;
    border-radius: 12px;
    background: #F0F0F3;
    gap: 4px;
    position: relative;
    div {
      padding: 8px 16px;
      border-radius: 12px;
      transition: all 0.3s linear;
      cursor: pointer;
      &.cur {
        background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        position: absolute;
        height: 100%;
        left: 0px;
        top: 0px;
        width: 102px;
        z-index: 1;
        transition: all 0.3s ease-in-out;
      }
      &.bmodal {
        width: 102px;
        z-index: 2;
        color: #fff;
      }
      &.xnmodal {
        z-index: 2;
        width: 88px;
        color: #1C2024;
      }
    }
  }
  .rightPanel {
    .cur {
      left: 106px !important;
      width: 88px !important;
    }
    .bmodal {
      color: #1C2024 !important;
    }
    .xnmodal {
      color: #fff !important;
    }
  }
  .detect-control {
    display: flex;
    padding: 8px 16px;
    justify-content: center;
    align-items: center;
    gap: 20px;
    border-radius: 12px;
    background: #F0F0F3;
    .switch-icon {
      cursor: pointer;
      transition: transform 0.3s ease;
      &:hover {
        transform: scale(1.1);
      }
    }
    /deep/.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      height: 21px !important;
      line-height: 21px;
    }
    /deep/ .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border: none !important;
      background: none !important;
    }
    /deep/ .ant-select {
      line-height: 21px;
      outline: none !important;
      width: 96px;
    }
    /deep/ .ant-select-single:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input {
      height: 21px;
    }
    /deep/ .ant-select-single .ant-select-selector .ant-select-selection-item, .ant-select-single .ant-select-selector .ant-select-selection-placeholder{
      line-height: 21px;
    }
    /deep/ .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
      // box-shadow: 0 0 0 2px rgb(221 208 255 / 24%)
      box-shadow: none;
    }
    // /deep/ .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {

    // }
    
  }

}
/deep/ .ant-select-dropdown {
  border-radius: 6px !important;
  overflow: hidden;
}
/deep/ .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: #e6ebff !important;
}

// 文件上传区域样式
.file-upload-area, .video-translate-area {
  .upload-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    border: 2px dashed #D9D9D9;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      border-color: #364AFD;
      background: rgba(54, 74, 253, 0.05);
    }

    .upload-icon {
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
      img {
        width: 100%;
        height: 100%;
      }
    }

    .upload-text {
      text-align: center;
      .upload-title {
        font-size: 16px;
        font-weight: 500;
        color: #1C2024;
        margin-bottom: 8px;
      }
      .upload-desc {
        font-size: 14px;
        color: #8B8D98;
      }
    }

    .upload-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }


}

// 语音翻译区域样式
.voice-translate-area {
  .voice-recorder {
    text-align: center;

    .recorder-controls {
      margin-bottom: 24px;

      .record-btn {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 16px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }

        &.recording {
          animation: pulse 1.5s infinite;
        }

        img {
          width: 32px;
          height: 32px;
        }
      }

      .record-text {
        font-size: 16px;
        color: #1C2024;
      }
    }

    .voice-input {
      background: #F8F9FA;
      border-radius: 12px;
      padding: 16px;
      margin-top: 16px;

      .voice-text {
        font-size: 14px;
        color: #1C2024;
        margin-bottom: 12px;
        text-align: left;
      }


    }
  }
}

// 翻译结果区域样式
.translate-result {
  margin-top: 24px;
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  border: 1px solid #E5E7EB;

  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1C2024;
    }

    .result-actions {
      display: flex;
      gap: 12px;

      .copy-btn, .download-btn {
        padding: 6px 12px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;
      }

      .copy-btn {
        background: #F0F0F3;
        color: #1C2024;
        &:hover {
          background: #E0E0E3;
        }
      }

      .download-btn {
        background: #364AFD;
        color: white;
        &:hover {
          background: #2A3BCC;
        }
      }
    }
  }

  .result-content {
    .original-text, .translated-text {
      margin-bottom: 16px;

      .text-label {
        font-size: 14px;
        font-weight: 500;
        color: #6B7280;
        margin-bottom: 8px;
      }

      .text-content {
        padding: 12px 16px;
        background: #F8F9FA;
        border-radius: 8px;
        font-size: 14px;
        line-height: 1.6;
        color: #1C2024;
        white-space: pre-wrap;
      }
    }

    .translated-text .text-content {
      background: linear-gradient(135deg, rgba(54, 74, 253, 0.05) 0%, rgba(171, 96, 241, 0.05) 100%);
      border: 1px solid rgba(54, 74, 253, 0.1);
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(54, 74, 253, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(54, 74, 253, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(54, 74, 253, 0);
  }
}

</style>
