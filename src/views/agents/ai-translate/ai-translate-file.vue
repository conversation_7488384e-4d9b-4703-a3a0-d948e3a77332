<template>
  <div class="translate_file_content">
    <div class="header">
      AI翻译:支持解读文档及网页内容,提炼核心要点,文章内容搜索,助您高效吸收信息!
    </div>
    <div class="content">
      <div class="filebox">

      </div>
      <div class="translate_panel">
        <div class="translate_header">
          <div class="header_left">
            <div class="cur">
              <span>双语对照</span>
            </div>
            <div>
              <span>译文</span>
            </div>
            <div>
              <span>原文</span>
            </div>
          </div>
          <div class="header_right">
            <div>自动识别术语</div>
            <div>查看全部术语库</div>
            <div class="ai-translate-btn">开始翻译</div>
          </div>
        </div>

        <div class="translate_content">
          <div class="translate_left">
            <div class="translate_con_header">
              <span class="tag">原始文本</span>
              <div class="ai-translate-select original_select">
                <Select
                  ref="select"
                  v-model:value="original_val"
                >
                  <Select.Option value="Chinese">中文</Select.Option>
                  <Select.Option value="dp">英文</Select.Option>
                  <Select.Option value="cs">俄文</Select.Option>
                  <Select.Option value="fd">阿拉伯文</Select.Option>
                </Select>
              </div>
            </div>
            <div class="original_content">
              


            </div>
          </div>

          <div class="translate_right">
            <div class="translate_con_header">
              <span class="tag">目标译文</span>
              <div class="ai-translate-select-warp">
                <span class="label">语种：</span>
                <div class="ai-translate-select">
                  <Select
                    ref="select"
                    v-model:value="original_val"
                  >
                    <Select.Option value="">中文</Select.Option>
                    <Select.Option value="dp">英文</Select.Option>
                    <Select.Option value="cs">俄文</Select.Option>
                    <Select.Option value="fd">阿拉伯文</Select.Option>
                  </Select>
                </div>
                <span class="label">术语库：</span>
                <div class="ai-translate-select">
                  <Select
                    ref="select"
                    v-model:value="original_val"
                  >
                    <Select.Option value="">中文</Select.Option>
                    <Select.Option value="dp">英文</Select.Option>
                    <Select.Option value="cs">俄文</Select.Option>
                    <Select.Option value="fd">阿拉伯文</Select.Option>
                  </Select>
                </div>

              </div>
            </div>
            <div class="original_content">


            </div>
          </div>
        </div>
      </div>
    </div>




  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Select } from 'ant-design-vue'

const original_val = ref('Chinese')





</script>

<style lang="less">
.translate_file_content {
  display: flex;
  flex-direction: column;
  padding: 12px 12px 12px 118px;
  overflow: hidden;
  text-align: left;
  height: 100%;
  .header {
    color: #1C2024;
    font-size: 18px;
    padding: 0px 0px 8px;
    font-weight: 400;
  }
  .content {
    flex: 1;
    display: flex;
    gap: 8px;
    overflow: hidden;
  }
}

.filebox {
  width: 260px;
  border-radius: 8px;
  display: flex;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;

  border: 2px solid #FFF;
  background: rgba(255, 255, 255, 0.80);
  box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
  backdrop-filter: blur(4.300000190734863px);

  
}

.translate_panel {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  

}
.translate_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .header_left, .header_right {
    display: flex;
    gap: 16px;
    div {
      line-height: 24px;
      padding: 4px 16px;
      display: flex;
      padding: 4px 16px;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      border: 1px solid #FCFCFD;
      background: #FFF;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }

      &.cur {
        span {
          background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .ai-translate-btn {
      color: #fff;
      display: flex;
      padding: 8px 16px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      border-radius: 12px;
      background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
    }
  }

  .header_right div {
    color: #1C2024;
    border-radius: 12px;
    line-height: 21px;
    padding: 8px 16px;
    border: none;
  }
  
  .header_left div {
    color: #60646C;
    font-weight: 500;
  }
}

// 翻译主体部分
.translate_content {
  display: flex;
  border-radius: 16px;
  border: 1px solid #FFF;
  background: rgba(255, 255, 255, 0.50);
  gap: 12px;
  flex: 1;  

}

.translate_con_header {
  display: flex;
  justify-content: space-between;
  line-height: 37px;
  padding-bottom: 8px;
  span.tag {
    color: #1C2024;
    font-weight: 700;
    font-size: 16px;
  }
  .ai-translate-select-warp {
    display: flex;
    align-items: center;
    gap: 10px;
    span.label {
      color: #1C2024;
      font-weight: 500;
      font-size: 15px;
    }
  }
}

.translate_left {
  height: 100%;
  flex: 1;
  // background-color: #FCFCFD;
  padding: 12px 0px 12px 12px;
  display: flex;
  flex-direction: column;
  // animation: 0.3s ;

  .original_content {
    background-color: #60646C;
    flex: 1;
  }
}


.translate_right {
  height: 100%;
  flex: 1;
  border-radius: 16px;
  border: 1px solid #FFF;
  background: #FFF;
  flex: 1;
  // background-color: #FCFCFD;
  padding: 12px 0px 12px 12px;
  display: flex;
  flex-direction: column;
  .original_content {
    background-color: #d7d7d7;
    flex: 1;
  }
}

</style>