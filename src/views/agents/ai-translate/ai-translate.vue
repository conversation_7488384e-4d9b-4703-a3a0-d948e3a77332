<template>
<div class="semantic-unified">
    <div class="tag-wrapper">
        <div class="tag">
            <span class="">这是第一个标签的内容这是第一个标签的内容这是第一个标签的内容这是第一个标签的内容这是第一个标签的内容这是第一个标签的内容这是第一个标签的内容</span>
        </div>
        <div class="tag">
            <span class="">和第二个标签的内容自然衔接</span>
        </div>
        <div class="tag" style="background-color: pink;">
            <span class="">和第二个标签的内容自然衔接</span>
        </div>
        <div class="tag">
            <span class="">和第二个标签的内容自然衔接</span>
        </div>
        <div class="tag">
            <span class="">和第二个标签的内容自然衔接</span>
        </div>
        <div class="tag">
            <span class="" style="background-color: blueviolet;">第三个标签继续延续排版效果</span>
        </div>
        <div class="tag">
            <span class="">第三个标签继续延续排版效果</span>
        </div>
        <div class="tag">
            <span class="">第三个标签继续延续排版效果</span>
        </div>
        <div class="tag">
            <span class="">第三个标签继续延续排版效果</span>
        </div>
        <div class="tag">
            <span class="">第五个标签继续延续排版效果</span>
        </div>
    </div>
</div>



</template>

<script setup>




</script>

<style lang="css">
    .semantic-unified {
        width: 600px;
        margin: 20px auto;
        font-family: Arial, sans-serif;
        line-height: 1.5;
    }

    .tag-wrapper {
        /*display: contents; /* 关键：使子元素直接参与父容器布局 */
    }

    .tag {
        display: inline; /* 使标签表现为行内元素 */
        margin: 0;
        padding: 0;
        border: none;
        &:hover {
            background-color: red;
        }
    }

    .tag-content {
        display: inline;
        white-space: normal;
    }




</style>
