<template>
<!-- <div> -->
  <div class="ai-tools">
    <span :class="{'cur': tabIdx == 1}" @click="tabChange(1)"><b>文章内容检索</b></span>
    <span :class="{'cur': tabIdx == 2}" @click="tabChange(2)"><b>生成考试试题</b></span>
    <span :class="{'cur': tabIdx == 3}" @click="tabChange(3)"><b>文章热词提取</b></span>
    <!-- <span><b>相似文章撰写</b></span>
    <span><b>自定义分析</b></span> -->
  </div>
  <template v-if="tabIdx === 1" >
    <div class="ai-content ai-chat-wrapper">
      <div style="display: none;" class="ai-related-title">文章内容检索</div>

      <div class="chat-content">
        <div class="aiChatLoading" v-if="props.fileStatus != 1">
          {{props.fileStatus == 2 ? '解析失败' : '文档解析中...'}}
          <div v-if="props.fileStatus != 2" class="refresh" @click="freshReadFileDetail()">重新获取</div>
        </div>
        <main-right-sse v-else pageName="chat" class="tab1" :robotobj="{...robotobj, askTypeClass: 1}" :messageId="props.searchMessageId" />
      </div>
    </div>
  </template>
  <template v-if="tabIdx === 2" >
    <div class="ai-content ai-chat-wrapper">
      <div style="display: none;" class="ai-related-title">试题生成</div>

      <div class="chat-content">
        <div class="aiChatLoading" v-if="props.fileStatus != 1">
          {{props.fileStatus == 2 ? '解析失败' : '文档解析中...'}}
          <div v-if="props.fileStatus != 2" class="refresh" @click="freshReadFileDetail()">重新获取</div>
        </div>
        <main-right-sse v-else pageName="chat" class="tab2" :robotobj="{...robotobj, askTypeClass: 2}" :messageId="props.testMessageId" />

      </div>
    </div>
  </template>
  <template v-if="tabIdx == 3">
    <div class="wordbox">
      <div class="words-grid" v-if="props.wordText">
        <div 
          v-for="(word, index) in props.wordText.split(',')" 
          :key="index" 
          class="word-card"
          :class="{ 'highlight': index < 3 }"
        >
          <!-- <span class="word-rank">{{ index + 1 }}</span> -->
          <span class="word-text">{{ word }}</span>
          <!-- <span class="word-weight" v-if="showWeight">{{ word.weight.toFixed(1) }}</span> -->
        </div>
      </div>
      <div v-else>
        热词提取中...( <div class="refresh" @click="freshReadFileDetail()">重新获取</div> )
      </div>

    </div>
  </template>



<!-- </div> -->



</template>

<script setup>
import { ref, onMounted, defineProps, watch, defineEmits, nextTick, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import mainRightSse from '@/views/main-right-sse.vue'
import Bus from "@/utils/bus.js";
import { messageDetail } from "@/api/index.js"; // 导入聊天记录、查询机器人助手
import { message } from "ant-design-vue";

const router = useRouter()
const route = useRoute() // 使用 useRoute 获取当前路由信息
const robotId = ref(route.query.robotId)
const askType = ref(route.query.askType)

const robotobj = ref({
  'robotId': robotId.value,
  'askType': askType.value
})

// const messageId = ref(1872) // 参数传递

const props = defineProps({
  fileId: {
    default: ''
  },
  wordText: {
    type: String,
    default: ''
  },
  fileStatus: {
    type: Number,
    default: 0
  },
  searchMessageId: {
    type: Number
  },
  testMessageId: {
    type: Number
  }
})
const emits = defineEmits(['getReadFileDetail'])

watch(() => props.wordText,( val ) => {

})

// 当前哪个tab页显示
const tabIdx = ref(1)

watch([() => props.searchMessageId, tabIdx],([newSearchMessageId, newTabIdx] ) => {
  if(newSearchMessageId && newTabIdx === 1) {
    nextTick(() => {
      chooseHistory()
    })
  }
})

watch([() => props.testMessageId, tabIdx],([newTestMessageId, newTabIdx] ) => {
  if(newTestMessageId && newTabIdx === 2) {
    nextTick(() => {
      chooseHistory()
    })
  }
})


const tabChange = (idx) => {
  if(canAsk.value) {
    tabIdx.value = idx
  } else {
    message.warning("回答生成中，请稍后！")
  }
}


// 获取热词提取内容
const freshReadFileDetail = () => {
  emits('getReadFileDetail')
}

// 判断是否有聊天信息
// const noHistoryTag = ref(true)
const historyList = ref([])
const hisMesLoading = ref(false)
const chooseHistory = () => {
  hisMesLoading.value = true
  let messageId = ref('')
  if(tabIdx.value == 1) {
    messageId.value = props.searchMessageId
  } else {
    messageId.value = props.testMessageId
  }

  messageDetail({
    page: 1,
    pageSize: 100000,
    messageId: messageId.value,
  }).then(res => {
    hisMesLoading.value = false
    if (res.code === 200) {
      let msgListDetailList = res.data.messageDetailVOList.map((item, idx) => {
        let contentText = item.contentText
        /// 如果是试题生成对，返回的 数据进行包装展示：
        if (tabIdx.value ==2) {
          try {
            let contentTextObj = JSON.parse(item.contentText)
            contentText = `单选题【${contentTextObj.singleNum||'--'}】个，多选题【${contentTextObj.multiNum||'--'}】个，判断题【${contentTextObj.judgeNum||'--'}】个，生成试题的要求是：${contentTextObj.text||'--'}`
          } catch (error) {
            console.log('试题生成类型错误...')
          }
        }
        return {
          // ...item,
          file: {
            status: 'success',
            name: item.fileTitle,
            size: item.messageSize,
            successId: item.messageDetailId
          },
          type: item.type === 1 ? 'gpt' : 'ask', // 用户类型  0用户 1系统
          converseType: item.type, //消息类型 0文字 1图片链接 2 文本连接 3语音连接 // 0 普通 1 图片 2 企标 3 文档 
          fileTitle: item.fileTitle, //文件名称
          messageSize: item.messageSize, // 文件大小kb
          content: contentText, // item.contentText, // 问答内容
          commentStatus: item.commentStatus, //点赞状态
          msgDetailId: item.messageDetailId, // 具体编号
          // annotationList: JSON.parse(item.messageFile),
          reasoningText: item.reasoningText,
          webSearchText: item.webSearchText,
          sourceDataText: item.sourceDataText,
          modelName: item.modelName
        }
      })
      // debugger
      console.log('发送消息')
      // if(msgListDetailList.length == 0) {
      //   noHistoryTag.value = true
      // } else {
      //   noHistoryTag.value = false
      // }
      Bus.$emit('historyList', msgListDetailList)
    }
  }).catch(e => {
    hisMesLoading.value = false
  })
}


// 使用fileId去换取messageId。robotobj等信息。

const canAsk = ref(true)
onMounted(() => {
  Bus.$on('canAsk', (val) => {
    canAsk.value = val
  })


})

onUnmounted (() => {
  Bus.$off('canAsk')
})




</script>

<style lang="less" scoped>
/deep/.chat-content {
  position: relative;
  display: flex;
  flex: 1;
  overflow: hidden;
  .send-wrap .send-box .btn-wrap {
    position: absolute; top: 50%; right: 12px; margin-top: -16px; width: fit-content;
  }
  .send-wrap .send-box .input-wrap .input-box {
    margin-bottom: 0px;
  }
  .avatar {
    display: none;
  }

}
.ai-tools {
  display: flex;
  justify-content: flex-start;
  span {
    background-color: #fff;
    border-radius: 6px;
    overflow: hidden;
    margin-right: 8px;
    padding: 3px 8px;
    b{
      padding: 2px 4px;
      color: #60646C;
      font-size: 14px;
      background-color: #fff;
      cursor: pointer;
    }
    &:hover b, &.cur b {
      background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}

.ai-content {
  border-radius: 8px;
  background-color: #fff;
  padding: 12px;
  flex: 2;
  display: flex;
  flex-direction: column;
  border: 1px solid #FFF;
  background: rgba(255, 255, 255, 0.50);
  box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
  backdrop-filter: blur(4.300000190734863px);
}

.ai-chat-wrapper {
  min-height: 50%;
}
//兼容处理chat聊天部分css内容
.ai-chat-wrapper {
  /deep/ .main-wrap-right {
    min-width: 10px;
  }
  /deep/ .main-wrap-right-content {
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 12px;
      background: linear-gradient(
        180deg,
        #f7f8f9 0%,
        #f7f8f9 10%,
        rgba(241, 242, 245, 0) 100%
      );
      z-index: 99;
    }
    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 12px;
      background: linear-gradient(
        to top,
        #f7f8f9 0%,
        #f7f8f9 10%,
        rgba(241, 242, 245, 0) 100%
      );
      z-index: 99;
    }
  }
  /deep/ .converse-wrap .answer-box .content {
    min-width: 250px;
  }
  /deep/ .converse-wrap .converse-box {
    padding: 10px 5px;
    // box-shadow: 0px 0px 1px #333;
  }
  /deep/ .converse-wrap .converse-item {
    padding-bottom: 12px;
  }
  /deep/ .converse-wrap .content {
    padding: 12px;
  }
  /deep/ .converse-wrap .converse-item.ques-box .content {
    border-radius: 8px;
  }
  /deep/ .converse-wrap .converse-item.answer-box .content {
    border-radius: 8px;
    box-shadow: 0px 0px 5px 0px rgba(67, 72, 169, 0.13)
  }
  /deep/ .converse-wrap .answer-box .avatar {
    display: none;
  }
  /deep/ .converse-wrap .stop-generate-btn {
    position: static;
  }
  /deep/ .send-wrap {
    // padding: 0px 8px;
    padding: 0px 0px;
    border-radius: 16px;
    overflow: hidden;
  }
  /deep/ .send-wrap .send-box .input-wrap {
    // padding-right: 54px;
    padding: 6px 54px 6px 6px;
    min-height: 30px;
  }
  /deep/ .converse-wrap .converse-item .model-tag {
    top: -2px;
  }
  /deep/ .converse-wrap .converse-item.answer-box .content .md-wrap {
    padding-top: 5px
  }

}

// 热词提取部分
.wordbox {
  padding: 1rem; background-color: #fff; flex: 1; border-radius: 12px;
}
.section-title {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #333;
  border-left: 4px solid #1890ff;
  padding-left: 0.5rem;
}

.words-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.8rem;
}

.word-card {
  background: #f5f7fa;
  padding: 0.6rem 0.8rem;
  border-radius: 6px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
}

.word-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.word-rank {
  width: 18px;
  height: 18px;
  background: #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
  font-size: 0.8rem;
}

.highlight .word-rank {
  background: #1890ff;
  color: white;
}

.word-text {
  flex: 1;
  font-size: 0.9rem;
}

.word-weight {
  font-size: 0.7rem;
  color: #999;
  margin-left: 0.5rem;
}

.aiChatLoading {
  width: 100%; height: 100%; position: absolute; top: 0px; left: 0px; text-align: center; padding-top: 30px;
}




</style>
