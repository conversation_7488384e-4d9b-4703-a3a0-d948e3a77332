<template>
  <div class="aiRelatedPanel">
    <div class="spinwarp" v-if="detailLoading">
      <Spin class="spinLoading" tip="结果获取中..."/>
    </div>
    <div class="ai-summary-wrapper">
      <div class="ai-related-title" @click="toggleSummary">文章总结<span :class="{ 'expanded': summaryVisible }"><img src="@/assets/image/arrow.svg"/></span></div>
      <div v-if="summaryVisible" class="ai-summary-content">
        <!-- 总结内容将在这里显示 -->
        <!-- <div class="ai-title">总结要点</div> -->
        <div class="ai-summary">
          <vue-Markdown v-if="summaryContent" :source="summaryContent" />
          <div v-else>
            文档解析中...( <div class="refresh" @click="getReadFileDetail(fileId)">重新获取</div> )
          </div>
        </div>
      </div>
    </div>

    <aiReaderChat :wordText="wordText" :fileStatus="fileStatus" :searchMessageId="searchMessageId" :testMessageId="testMessageId" @getReadFileDetail="getReadFileDetail" />

    <!-- <div class="ai-content ai-chat-wrapper">
      <div class="ai-related-title">文章内容检索</div>
    </div> -->

  </div>
</template>

<script setup>
import { ref, defineProps, watch, nextTick, onMounted } from 'vue';
import aiReaderChat from '@/views/agents/ai-reader-chat.vue'
import VueMarkdown from "@/components/markdown/VueMarkdown.js";
import { Spin } from 'ant-design-vue';

import { readFileDetail } from '@/api/index'

import Bus from '@/utils/bus';


const props = defineProps({
  fileId: {
    type: Number,
    default: 0
  },
})

watch(
  () => props.fileId,
  (val) => {
    nextTick(() => {
      // 文件ID变化了，获取该文件的解析
      getReadFileDetail(val)
    })
  }
)

// 总结内容
const summaryContent = ref("")
// 热词提取
const wordText = ref("")
// 文件处理的 状态
const fileStatus = ref(0)
// 文章内容检索的messageId
const searchMessageId = ref()
// 生成考试试题的messageId
const testMessageId = ref()

const detailLoading = ref(false)
const getReadFileDetail = (  ) => {
  detailLoading.value = true
  readFileDetail({ fileId: props.fileId }).then(res => {
    detailLoading.value = false
    if(res.code == 200) {
      summaryContent.value = res.data.summarizeText || ""
      wordText.value = res.data.wordText || ""
      fileStatus.value = res.data.fileStatus || 0
      searchMessageId.value = res.data.searchMessageId
      testMessageId.value = res.data.testMessageId
    }
  })
}


const summaryVisible = ref(true);
const toggleSummary = () => {
  summaryVisible.value = !summaryVisible.value;
};

onMounted(() => {

  // Bus.$on('fileId_fresh', (fileId) => {
  //   // fileId就是props.fileId
  //   getReadFileDetail(fileId)
  // })


})

</script>
<style scoped lang="less">
.aiRelatedPanel {
  display: flex;
  gap: 8px;
  flex-direction: column;
  width: 500px;
  position: relative;
  // max-width: 550px;
  // min-width: 300px;
  
}
/deep/.ai-related-title {
  padding: 8px 6px;
  font-size: 16px;
  border-radius: 5px;
  background: linear-gradient(348deg, #FFF -396.51%, #EBEFFF 69.11%);
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  color: #1C2024;
  span {
    transition: all 0.2s linear;
    transform: rotate(180deg);
    img {
      width: 16px;
      height: 16px;
    }
    &.expanded {
      transform: rotate(0deg);
    }
  }
}
.ai-title {
  font-size: 14px;
  font-weight: bolder;
  line-height: 28px;
  padding-top: 5px;
}
.ai-summary-wrapper {
  max-height: 30%;
  overflow: hidden;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  border: 1px solid #FFF;
  background: rgba(255, 255, 255, 0.50);
  box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
  backdrop-filter: blur(4.300000190734863px);
  .ai-summary-content {
    flex: 1;
    /* max-height: 30%; */
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding-top: 5px;
  }
  .ai-summary {
    flex: 1;
    overflow: auto;
  }
}
/deep/ .refresh { color: #1900ff; display: inline; cursor: pointer; }
.spinwarp {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  background-color: #ffffff50;
  z-index: 9;
}
.spinLoading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}



</style>