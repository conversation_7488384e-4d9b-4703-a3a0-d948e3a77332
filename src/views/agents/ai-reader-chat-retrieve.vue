<template>
    <main-right-sse pageName="chat" :robotobj="{...robotobj, askTypeClass: 1}" :messageId="props.searchMessageId" />


</template>
<script setup>
import { ref, onMounted, defineProps, watch, defineEmits  } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import mainRightSse from '@/views/main-right-sse.vue'
import Bus from "@/utils/bus.js";
import { messageDetail } from "@/api/index.js"; // 导入聊天记录、查询机器人助手

const props = defineProps({
  fileId: {
    default: ''
  },
  fileStatus: {
    type: Number,
    default: 0
  },
  searchMessageId: {
    type: Number
  },
  robotobj: {
    type: Object
  }
})


</script>
<style lang="less">
</style>