<template>
  <div class="agent-container">
    
    <div class="agent-layoutcss">
      <div class="myagent-header">
        <div class="page-title">
          我的智能体
        </div>
        <div class="kinds-list">
          <span @click="filterAgents(0)" :class="{ cur: type === 0 }"><b>全部</b></span>
          <span @click="filterAgents(1)" :class="{ cur: type === 1 }"><b>我创建的</b></span>
          <span @click="filterAgents(2)" :class="{ cur: type === 2 }"><b>我收藏的</b></span>
          <a href="javascript:;" class="goAgentsBtn" @click="skipAgents">智能体中心</a>
        </div>
      </div>
      <Spin v-if="myAgentLoading" tip="我的智能体加载中..." style="transform: translate(-50%, -50%); position: absolute; top: 50%; left: 50%;" />
      <template v-else>
        <div class="agentEmpty" v-if="myAgentList.length === 0">
          暂无相关智能体，去智能体中心看看吧～
        </div>
        <div class="agent-list">
          <div class="agent-item" @click="viewChat(item.robotId, item.askType)" v-for="(item, index) in myAgentList" :key="index">
            <div class="agent-item-top">
              <img :src="'/agentImg' + item.robotImage.substring(item.robotImage.indexOf('/agent-oss'))" />
              <div class="agent-info">
                <div class="agent-name">
                  <span class="agent-name-span">{{ item.robotName }}</span>
                  <p class="agent-tag">
                    <span class="tag" v-for="(tag, index) in item.subscriptList" :key="index">{{ tag }}</span>
                  </p>
                </div>
                <p class="agent-desc" :title="item.robotIntroduce">{{ item.robotIntroduce }}</p>
              </div>
            </div>
            <div class="collectbox">
              <div class="authorSpan">作者：{{ item.robotPower }}</div>
              <div class="collectSpan" @click.stop="toggleCollect(item)">
                <img src="@/assets/image/up.svg" alt="收藏" v-if="item.collectStatus == 1" />
                <img src="@/assets/image/upcur.svg" v-else alt="收藏" />
                <span>收藏</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { message, Modal, Spin } from "ant-design-vue";
import { getMyAgent, collectControl } from "@/api/index.js";
const router = useRouter();

const viewChat = ( robotId, askType ) => {
  if(askType == 8) {
    router.push({ path: '/ai-image' });
  } else {
    router.push({ path: '/chat-hello', query: { robotId: robotId, askType: askType } });
  }
};

/**
 * type 0 - 全部
 * type 1 - 我的创建
 * type 2 - 我的收藏
 */
const type = ref(0)
const filterAgents = (valueChange) => {
  type.value = valueChange;
  getMyAgentList();
}

const toggleCollect = (item) => {
  collectControl({
    "robotId": item.robotId, //智能体id
    "status": item.collectStatus == 0 ? 1 : 0, //0收藏 1取消收藏
  }).then((res) => {
    if(res.code == 200) {
      message.success('操作成功');
      item.collectStatus = item.collectStatus == 1 ? 0 : 1;
    }
  }).catch((err) => {
    console.error(err);
  });
}

const skipAgents = (item) => {
  router.push({ path: '/agent-page' });
}

const myAgentLoading = ref(false) // 加载状态
const myAgentList = ref([])
const getMyAgentList = () => {
  myAgentLoading.value = true;
  getMyAgent({type: type.value}).then((res) => {
    myAgentLoading.value = false;
    if(res.code == 200) {
      myAgentList.value = res.data;
    } else {
      myAgentList.value = []; // 清空列表
      // message.error(res.msg);
    }
  }).catch((err) => {
    myAgentLoading.value = false;
    myAgentList.value = []; // 清空列表
    console.error(err);
  });
}

onMounted(() => {
  getMyAgentList()
})


</script>
<style lang="less" scoped>
.agent-container {
  margin-left: 108px;
  height: 100%;
  overflow-y: auto;
  position: relative;
  .agent-layoutcss {
    max-width: 1305px;
    width: 71.27%;
    margin: 24px auto 0px;
  }
  .myagent-header {
    .page-title {
      font-size: 28px;
      // line-height: 42px;
      text-align: left;
      margin-bottom: 16px;
      font-weight: 500;
    }

    .kinds-list {
      display: flex;
      // justify-content: space-between;
      margin-right: 30px;
      align-items: flex-start;
      margin-bottom: 32px;
      position: relative;
      .scale-box {
        display: flex; overflow-x: auto;
      }
      .more {
        margin-left: 16px;
      }
      .goAgentsBtn {
        position: absolute;
        padding: 5px 20px;
        right: 0px;
        top: -1px;
        font-size: 14px;
        font-weight: 500;
        &::after {
          right: 15px;
        }
        &:hover{
          &::after {
            animation: move 1s linear infinite;
            border-color: #364afd;
          }
        }
      }
    }
    span {
      padding: 4px 16px;
      background-color: #fff;
      border-radius: 8px;
      border: 1px solid #fcfcfd;
      margin-right: 16px;
      cursor: pointer;
      font-weight: 500;
      flex-shrink: 0;
      &.cur b, &:hover b {
        background: linear-gradient(
          69deg,
          #3dbbd7 2.55%,
          #364afd 32.25%,
          #b76ef1 120.42%
        );
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      &:last-child {
        margin-right: 0px;
      }
    }
  }
  .title-line {
    padding: 20px 0px;
    font-size: 20px;
    line-height: 26px;
    color: #000;
    text-align: left;
    display: flex;
    align-items: center;
    span.moretag {
      width: 10px;
      height: 10px;
      display: inline-block;
      border-top: 1px solid #60646c;
      border-right: 1px solid #60646c;
      transform: rotate(45deg);
    }
  }
  .hot-recommend {
    display: flex;
    justify-content: space-between;
    .hot-item {
      width: calc( 20% - 8px );
      border-radius: 16px;
      background-color: #fff;
      padding: 8px;
      position: relative;
      cursor: pointer;
      transition: all 0.1s linear;
      box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.05);
      span.rank-num{
        position: absolute;
        top: 0px;
        left: 0px;
        border-radius: 16px 0px 16px 0px;
        width: 32px;
        height: 32px;
        color: #fff;
        line-height: 32px;
        font-weight: 500;
      }
      .helper-name {
        font-size: 16px; color: #000; font-weight: bolder; margin-top: 9px;
      }
      .helper-img {
        display: block;
        margin: 0 auto;
        width: 68px;
        height: 68px;
        border-radius: 100%;
        overflow: hidden;
      }
      .helper-tag{
        color: #60646C; font-size: 14px; margin-top: 1px;
      }
      .helper-use {
        font-size: 12px; color: #60646C; margin-top: 2px;
      }
      .use-btn {
        display: inline-block;
        background: linear-gradient(89deg, #0FD3FF -3.47%, #364AFD 22.92%, #4C4EFB 59.8%, #B457FF 97.6%);
        border-radius: 16px; padding: 1px; overflow: hidden;
        span.warpper {
          background: #fff; display: inline-block; border-radius: 16px;
        }
        span.inner {
          font-size: 14px; background: linear-gradient(89deg, #0FD3FF -3.47%, #364AFD 22.92%, #4C4EFB 59.8%, #B457FF 97.6%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          line-height: 18px;
          margin: 5px 12px;
          display: inline-block;
        }
        margin: 8px 0px;
      }
      &:first-child .rank-num {
        background-color: #E4453D;
      }
      &:nth-child(2) .rank-num {
        background-color: #F1913C;
      }
      &:nth-child(3) .rank-num {
        background-color: #F6C042;
      }
      &:nth-child(4) .rank-num {
        background-color: #EEF0FC;
        color: #60646C;
      }
      &:nth-child(5) .rank-num {
        background-color: #EEF0FC;
        color: #60646C;
      }
      &:hover {
        // transform: scale(0.99);
        background-color: #ffffff6d;
        .helper-img img {
          transition: all 0.2s linear;
          transform: scale(1.1);
        }
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
        .helper-name {
          color: #2237ef;
        }
        // .use-btn {
        //   transition: all 0.2s linear;
        //   background: linear-gradient(-89deg, #0FD3FF -3.47%, #364AFD 22.92%, #4C4EFB 59.8%, #B457FF 97.6%);
        //   span.inner {
        //     transition: all 0.1s linear;
        //     background: linear-gradient(289deg, #0FD3FF -3.47%, #364AFD 22.92%, #4C4EFB 59.8%, #B457FF 97.6%);
        //     background-clip: text;
        //     -webkit-background-clip: text;
        //     -webkit-text-fill-color: transparent;
        //   }
        //   box-shadow: 0 0 0 2px #00000014;
        // }
      }
    }

  }
  .agentEmpty {
    width: 100%; text-align: center; margin-top: 30px; color: #b7b7b7;
  }
  .agent-list {
    text-align: left; display: grid; grid-template-columns: repeat(3, 1fr); /* 定义三列，每列相等宽度 */
    gap: 16px; margin-bottom: 20px;
    .agent-item {
      display: flex; flex-direction: column; padding: 16px; border-radius: 16px; background-color: #fff; transition: all 0.1s linear; cursor: pointer; justify-content: space-between;
      &:hover {
        transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); background-color: #ffffff6d;
        .agent-name-span {
          color: #2237ef;
        }
      }
      &:last-child {
        margin-right: 0;
      }
    }
    .agent-item-top { 
       display: flex;
       img {
        width: 58px; height: 58px;
       }
      
    }
    .agent-info { 
      flex: 1; margin-left: 8px;
      .agent-name {
        display: flex; margin-bottom: 8px;
        .agent-tag {
          span{
            font-size: 10px; color: #fff; border-radius: 4px; padding: 2px 4px;
            background: linear-gradient(90deg, #FF8969 0%, #FF5151 100%); margin-left: 8px;
          }
        }
      }
      .agent-name-span { flex: 1; font-size: 16px; color: #000; font-weight: 500; }

      .agent-desc {
        color: #8B8D98;
        font-size: 12px;
        font-weight: 400;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 限制文本显示的行数为 3 行 */
        text-overflow: ellipsis;
        word-break: break-all;
      }
    }
    .collectbox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 5px;
      line-height: 14px;
      padding-left: 66px;
      
      .authorSpan {
        color: #8B8D98;
        font-size: 12px;
      }
      div.collectSpan {
        display: flex; align-items: center; position: relative; user-select: none; 
        &:hover {
          // div {
            transform: scale(1.1);
            transition: transform 0.1s ease
          // }
        }
        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100%; /* 初始宽度 */
          height: 100%; /* 初始高度 */
          background: radial-gradient(circle at center, rgba(255, 255, 255, 0.6) 50%, transparent);
          opacity: 0;
          transition: opacity 0.2s ease;
          // border-radius: 100%;
        }
        &:active::before {
          opacity: 1;
          animation: glow 0.3s ease-out forwards;
        }
        @keyframes glow {
          0% {
            transform: translate(-50%, -50%) scale(0); /* 从中心开始 */
          }
          100% {
              transform: translate(-50%, -50%) scale(1.5); /* 放大到2倍 */
          }
        }
      }
      img {
        align-self:flex-start;
      }
      span {
        font-size: 12px;
        color: #8B8D98;
        padding-left: 4px;
      }
      
    }
  }
}
@keyframes move {
  0% {
    right: 16px;
  }
  60% {
    right: 12px;
  }
  100% {
    right: 16px;
  }
}

</style>
