<!-- 有聊天的界面 -->
<template>
  <div class="chat-page-container">
    <div class="loading-box" v-if="pageLoading">
      <Spin tip="火速加载中,请稍后..." style=" position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%);">
        <!-- <img src="@/assets/loading.gif" alt="Loading..." /> -->
      </Spin>
    </div>
    <div class="main-panel">
      <div class="pagetitle">
        <span> {{ titleObj[robotId] }} </span>
      </div>
      <div class="warpper">
        <div class="scroll-container">
          <!-- 聊天内容区域 -->
          <div class="converseBox layoutcss">
            <div class="converse-item">
              <!-- <div class="avatar ai-avatar" /> -->
              <div class="avatar" :class="askType === '5' ? 'reader-avatar' : 'ai-avatar'" />
              <div class="content" v-if="askType === '5'">
                <aiReadHello :robotobj="robotobj" />
              </div>
              <div v-else class="content" v-html="helloObj[robotId]">
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <com-send /> -->
      <comSend :model="model" :innet="innet" pageName="hello" :robotobj="robotobj" :canAsk="canAsk" @sendMsg="handleSendMsg" @modelChange="modelChange" @innetChange="innetChange" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router';
import mainRightSse from './main-right-sse'
import comSend from '@/components/com-send.vue'; // 确保导入正确的组件名称
import comMsgTit from '@/components/com-msg-tit.vue'; // 这里缺少了导入组件的名称，应该是 comSend
import aiReadHello from '@/components/hello-com/ai-read-com.vue'
import Bus from "@/utils/bus.js";
import { createMsg, getRobotDetail } from '@/api/index.js';
import { Spin } from 'ant-design-vue';


const router = useRouter()
const route = useRoute() // 使用 useRoute 获取当前路由信息
const robotId = ref(route.query.robotId || 1)
const askType = ref(route.query.askType || 1)

/*
 * robotId = 1 代表AI助理
 * robotId = 2 代表AI阅读
 * 
 * 
 */
 
const canAsk = ref(true); // hello 页面也可以去问答

const robotobj = {
  'robotId': robotId.value,
  'askType': askType.value
}

const titleObj = ref({
  // 1: '和AI助理的会话',
  // 2: '和AI阅读的会话'
})

const helloObj = ref({
  // 1: 'Hi~ 你好,我是灵犀<br>我可以帮你搜索、答疑、写作、请把你的任务交给我吧～',
  // 3: 'Hi~ 你好,我是AI阅读助手<br>我可以帮你阅读、总结、提取关键信息、请把你的任务交给我吧～',
})

// 控制按钮是否开启对象
const controlBtnsObj = ref({
  'fileStatus': 0, // 上传文件按钮 0开启，1关闭
  'reasoningStatus': 0, // 深度思考按钮
  'recommendStatus': 0, // 相似问是否开启
  'webSearchStatus': 0 // 联网搜索
})


const pageLoading = ref(false)
// 获取智能体 欢迎语
const getRobotHello = () => {
  pageLoading.value = true;
  getRobotDetail(robotobj).then(res => {
    pageLoading.value = false;
    if (~~res.code === 200) {
      // console.log(res.data)
      titleObj.value[robotId.value] = '和' + res.data.robotName + '的会话'; // 假设 res.data.welcomeTitle 是标题
      helloObj.value[robotId.value] = res.data.welcomeTitle + '<br>' + res.data.welcomeIntroduce;
      controlBtnsObj.value.reasoningStatus = res.data.reasoningStatus
      controlBtnsObj.value.recommendStatus = res.data.recommendStatus
      controlBtnsObj.value.webSearchStatus = res.data.webSearchStatus
      controlBtnsObj.value.fileStatus = res.data.fileStatus
      Bus.$emit('controlBtnsObjChange',controlBtnsObj.value)
      // if(robotId.value === 3) {
      //   helloObj[robotId.value] = 'Hi~ 你好,我是AI阅读助手<br>我可以帮你阅读、总结、提取关键信息、请把你的任务交给我吧～'
      // } else {
      //   helloObj[robotId.value] = 'Hi~ 你好,我是灵犀<br>我可以帮你搜索、答疑、写作、请把你的任务交给我吧～';
      // }

    }
  });
}

const model = ref(true)
const innet = ref(false)
const modelChange = (val) => {
  model.value = val;
}
const innetChange = (val) => {
  innet.value = val;
}

// 创建会话, 并将会话内容id, text传递给 chat页面
const handleSendMsg = (msg) => {
  console.log('Message sent:', msg);
  pageLoading.value = true
  createMsg(robotobj).then(res => {
    pageLoading.value = false; 
    if (~~res.code === 200) {
      let msgData = {
        model: model.value,
        innet: innet.value,
        msg: msg
      };
      // 将消息数据存储到 localStorage 中，使用 messageId 作为 key
      localStorage.setItem(res.data.messageId, JSON.stringify(msgData))
      // router push 后，使用 messageId 作为 key获取msg值，然后渲染到页面
      
      router.push({ name: 'chat', params: { messageId: res.data.messageId } });
    } else {
      console.error('Failed to create message:', res);
    }
  })
}

onMounted(() => {
  getRobotHello()
})

</script>

<style lang="less" scoped>
.chat-page-container{
  display: flex;
  flex-direction: row;
  height: 100%;
  width: calc(100% - 108px) !important;
  margin-left: 108px;
  // min-width: 1000px;?
  

}
.loading-box {
  position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; background-color: #ffffff65; z-index: 999;
}
.main-panel { 
  flex: 1;
  flex-direction: column; 
  height: 100%;
  padding: 0px 0px 32px;
  min-width: 559px;
  position: relative;
  flex: 1;
  overflow: hidden;
  width: 100%;
  display: flex;
  flex-direction: column;
  .pagetitle {
    line-height: 24px;
    padding: 5px 0px;
    span{
      color: #1C2024;
    }
  }
  .warpper {
    position: relative;
    flex: 1;
    overflow: hidden;
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 12px;
      background: linear-gradient(
        180deg,
        #f1f2f5 0%,
        #f1f2f5 10%,
        rgba(241, 242, 245, 0) 100%
      );
      z-index: 99;
    }
    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 12px;
      background: linear-gradient(
        to top,
        #f1f2f5 0%,
        #f1f2f5 10%,
        rgba(241, 242, 245, 0) 100%
      );
      z-index: 99;
    }
  }
  .scroll-container {
    height: 100%;
    overflow-y: auto;
  }
  .converseBox {
    
    width: 100%;
    position: relative;
    
    // &::before {
    //   content: "";
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   width: 100%;
    //   height: 12px;
    //   background: linear-gradient(
    //     180deg,
    //     #f1f2f5 0%,
    //     #f1f2f5 10%,
    //     rgba(241, 242, 245, 0) 100%
    //   );
    //   z-index: 99;
    // }
    .converse-item {
      display: flex;
      align-items: flex-start;
      padding-bottom: 48px;
      padding-top: 10px;

      
      .content {
        color: #1d2129;
        padding: 16px;
        text-align: left;
        border-radius: 16px;
        line-height: 20px;
        border-radius: 16px;
        border: 1px solid #FFF;
        background: rgba(255, 255, 255, 0.50);
        box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
        backdrop-filter: blur(4.300000190734863px);
        font-size: 14px;
        overflow: hidden;
        flex: 1;
      }
    }
  }

  .avatar {
    margin-top: 4px;
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    flex-shrink: 0;
    background-image: url("@/assets/image/chaticon.svg");
    background-size: contain;
    margin: 0px 12px 0px 0px;

  }
  .reader-avatar {
    background-image: url("@/assets/image/reader-icon.svg");
  }

  .avatar img {}

}
  .send-wrap {
    margin-bottom: 24px;
  }
  


</style>