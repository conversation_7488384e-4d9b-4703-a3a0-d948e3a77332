<template>
  <div class="login-wrap">
    <div class="login-icon">
      <span />灵犀
    </div>
    <div class="main-wrap">
      <div class="desc-panel" style="display: none;">
        <div class="title">
          欢迎使用灵犀
        </div>
        <div class="title-eng">
          Welcome to<br />
          Global Content Asset<br />Management Platform
        </div>
      </div>
      <div class="bg">
        <div class="login-box">
          <iframe :src="iframeUrl" id="myframe" ref="myframe" frameborder="0" width="100%" height="100%"
            style="max-width:500px;" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, reactive, onBeforeUnmount, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { login, loginSSO } from '@/api'
import { setCookie, getCookie } from '@/utils/common';
// import { encrypt, decrypt } from '@/utils/jsencrypt'
import {
  LoadingOutlined
} from '@ant-design/icons-vue';
const route = useRoute()
const router = useRouter()

const iframeUrl = 'https://platforminner.gwm.cn/'
const handleLogin = (businessToken) => {

  loginSSO({ businessToken }).then(res => {
    console.log(res, 'res')
    if (res.code === 200) { //token验证失败 没有权限
      // const _token = res.data
      // console.log('token', _token)
      localStorage.setItem('userToken', JSON.stringify(res.data.access_token))
      localStorage.setItem('userName', JSON.stringify(res.data.userName||'gw'))
      setCookie('userToken', res.data.access_token, 0.4); // 通过存入 cookie 发验证token给后端
      // router.push('/home')
      const pathName = window.location.pathname || ''
      window.location.replace(window.location.origin + '/')
    } else {
      console.log('验证失败 没有访问权限')
    }
  })
}
onMounted(() => {
  console.log('加载loginViewn')
  window.addEventListener("message", (e) => {
    console.log(e)
    if (e.data.type === 'ssoToken') { // 判断如果4a工作台发来token
      handleLogin(e.data.ssoToken)
    } else { // 如果单点登录发来token
      if (e.data.token) {
        handleLogin(e.data.token)
      }
    }
  })
  //判断是否第三方跳转
  let urlToken = route.query.token, urlSearch = window.location.search
  console.log(urlToken)
  if (urlToken) {
    handleLogin(urlToken)
    return
  } else if (urlSearch) {
  // 判断是否 用 redirectUrl (https://sso.gwm.cn/login?mode=TOKEN&redirect_url=http://127.0.0.1:8080/callback) 跳转 因为hash模式用redirectUrl跳转会造成跳转为 http://localhost:8080/?token=xx#/login 这种格式 所以需要 单独处理
    const _searchToken = urlSearch.split('?token=')[1]
    handleLogin(_searchToken)
    return
  }

  // 判断是否已经登陆 
  const LSToken = getCookie('userToken')
  const token = JSON.parse(localStorage.getItem('userToken'))
  if (LSToken && LSToken.length && token) {
    router.push('/home')
    // this.handleLogin(JSON.parse(LSToken))
    return
  }
})
</script>

<style lang="less" scoped>
.login-wrap {
  width: 100%;
  height: 100%;
  background: url('@/assets/image/ibg.png') center no-repeat;
  background-size: cover;
  overflow: hidden;
  position: relative;
}

.login-icon {
  position: absolute;
  top: 40px;
  left: 40px;
  display: flex;
  align-items: center;
  color: #000;
  font-size: 28px;

  span {
    margin-right: 10px;
    display: inline-block;
    width: 48px;
    height: 48px;
    background: url('@/assets/image/logo.png') center no-repeat;
    background-size: contain;
    border-radius: 5px;
    
  }
}

.main-wrap {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1060px;
  height: 628px;
  margin-left: -530px;
  margin-top: -314px;
}

.desc-panel {
  position: absolute;
  top: 27px;
  left: 0;
  width: 780px;
  height: 574px;
  // background: url('@/assets/image/login_bg_panel.png') center no-repeat;
  font-size: 48px;
  padding: 80px 70px;
  text-align: left;
  color: #fff;

  .title {
    margin-top: 121px;
  }

  .title-eng {
    display: none;
    font-size: 28px;
    margin-top: 46px;
  }
}

.login-box {
  position: absolute;
  top: 0;
  right: 0;
  // transform: translate3d(-50%, -50%, 0);
  width: 573px;
  height: 528px;
  background: #fff; /// rgba(255, 255, 255, 0.2);
  // background-image: linear-gradient(160deg, #c9c2fe40 0%, #fbfbff 25%, #fbfbff 70%, #c9c2fea1 100%);
  backdrop-filter: blur(40px);
  /* Note: backdrop-filter has minimal browser support */
  border-radius: 10px;
  filter: drop-shadow(-2px 0px 5px rgba(4, 4, 4, 0.15)); // drop-shadow(-4px 0px 20px rgba(34, 44, 196, 0.2));
  // padding: 105px 80px;
  padding-top: 30px;
  border: 1px solid #d7d4f9;

  .gw-logo {
    position: absolute;
    top: 30px;
    left: 40px;
    font-size: 14px;
    color: #000;
    display: flex;
    align-items: center;

    span {
      width: 32px;
      height: 22px;
      display: inline-block;
      background: url('@/assets/image/icon_gw.png') center no-repeat;
      background-size: cover;
      margin-right: 10px;
      ;
    }
  }

  .title {
    font-size: 32px;
    color: #000;
  }


}

.bg {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 573px;
  height: 528px;
  // background: url('@/assets/image/login_bg2.png') center no-repeat;
  background-size: cover;
  margin-left: -236px;
  margin-top: -264px;
  border-radius: 40px;
}

.login-item {
  width: 100%;
  font-size: 20px;
  display: flex;
  align-items: center;
  margin-top: 30px;

  // &:nth-child(1) {}

  // &:nth-of-type(1) {
  //   margin-top: 60px;
  // }

  span {
    display: inline-block;
    width: 120px;
    text-align: right;
  }

  input {
    height: 54px;
    line-height: 54px;
    border: 1px solid hsl(0deg 2.4% 67.57% / 40%);
    border-radius: 8px;
    padding: 0 20px;
    color: #000;
    font-size: 14px;
    background: transparent;

    &.error {
      border-color: #ff0000;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

.error-text,
.login-error {
  display: block;
  position: absolute;
  font-size: 14px;
  color: #ff0000;
  text-align: left;
}

.login-btn {
  user-select: none;
  cursor: pointer;
  width: 413px;
  height: 54px;
  line-height: 54px;
  left: 1092px;
  top: 630px;
  background: rgb(45 71 244 / 80%);
  border-radius: 8px;
  font-size: 16px;
  color: #fff;
  margin-top: 40px;

  &:hover {
    background: #2D47F4;
  }
}
</style>