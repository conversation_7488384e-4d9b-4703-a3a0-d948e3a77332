<template>
  <div class="home-page-container">
    <div class="loading-box" v-if="pageLoading">
      <Spin tip="火速加载中,请稍后..." style=" position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%);">
        <!-- <img src="@/assets/loading.gif" alt="Loading..." /> -->
      </Spin>
    </div>
    <div class="home-center">
      <div class="slognbox">
        <h1 class="slogn"><img src="@/assets/image/slogn.svg" alt="Logo" class="logo"></h1>
        <p class="sub-slogn">心有灵犀，办公无间</p>
      </div>
      <comSend :model="model" :innet="innet" pageName="home" :robotobj="robotobj" :canAsk="canAsk" @sendMsg="handleSendMsg" @modelChange="modelChange" @innetChange="innetChange" />
      <!-- <preset-list @handleSendMsg="handleSendMsg" /> -->
      <info-module />
      <div class="btnPanel layoutcss">
        <!-- <a href="javascript:;" class="goAgentsBtn" style="margin-right: 10px;" @click="goScence">开发者门户</a> -->
        <a href="javascript:;" class="goAgentsBtn" @click="goAgent">更多智能体</a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'; 
import { useRoute, useRouter } from 'vue-router';
import comSend from '@/components/com-send.vue';
// import PresetList from '@/components/preset-list.vue';
import infoModule from '@/components/info-module.vue';

import { createMsg } from '@/api/index.js';
// import ChatHelloPage from './chat-hello-page.vue';
import Bus from "@/utils/bus.js";
import { Spin } from 'ant-design-vue';



const router = useRouter();  // 使用 useRouter 来获取路由实例

const canAsk = ref(true); // 首页都可以去问答

const pageLoading = ref(false); // 页面加载状态

const robotobj = {
  'robotId': 1
}

const model = ref(true)
const innet = ref(false)
const modelChange = (val) => {
  model.value = val;
}
const innetChange = (val) => {
  innet.value = val;
}

// 跳转到智能体页面
const goAgent = () => {
  router.push({ path: '/agent-page' });
}
// 跳转案例分享页面---AI门户
const goScence = () => {
  window.open('http://intelligence.test.paas.gwm.cn/index', '_blank')
}

// 创建会话, 并将会话内容id, text传递给 chat页面
const handleSendMsg = (msg) => {
  console.log('Message sent:', msg);
  pageLoading.value = true; // 开始加载
  createMsg(robotobj).then(res => {
    pageLoading.value = false
    if (~~res.code === 200) {
      let msgData = {
        model: model.value,
        innet: innet.value,
        msg: msg
      };
      // 将消息数据存储到 localStorage 中，使用 messageId 作为 key
      localStorage.setItem(res.data.messageId, JSON.stringify(msgData))
      // router push 后，使用 messageId 作为 key获取msg值，然后渲染到页面
      
      router.push({ name: 'chat', params: { messageId: res.data.messageId } });
    } else {
      console.error('Failed to create message:', res);
    }
  })
}

// 控制按钮是否开启对象
const controlBtnsObj = ref({
  'fileStatus': 1, // 上传文件按钮 0开启，1关闭
  'reasoningStatus': 0, // 深度思考按钮
  'recommendStatus': 0, // 相似问是否开启
  'webSearchStatus': 0 // 联网搜索
})
onMounted(() => {
  Bus.$emit('controlBtnsObjChange', controlBtnsObj.value);
});


</script>

<style lang="less" scoped>
.home-page-container {
  background: url(@/assets/image/ibg.png) no-repeat center center;
  background-size: cover;
  width: calc(100% ) !important;
  height: 100%;
  // margin-left: 108px;
  padding-left: 108px !important;
  // padding-right: 108px !important;
  position: relative;
  min-height: 580px;
  .send-wrap .send-box {
    width: 55%;
  }
}
.loading-box {
  position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; background-color: #ffffff65; z-index: 999;
}
.home-center {
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 0 10px;
  padding-left: 108px;
  // max-width: 768px;

}
.slognbox {
  display: flex;
  max-width: 736px;
  flex-direction: column;
  width: calc(100% - 34px);
  margin: 0 auto 32px;
  .slogn {
    width: 100%;
    padding-bottom: 8px;
    margin: 0px;
    img {
      width: 100%;
    }
  }
  .sub-slogn {
    color: #8B8D98;
    text-align: center;
    font-family: "GWM Sans";
    font-size: 21px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 5px;
    line-height: 32px;
  }
}
.send-wrap .send-box .input-wrap .input-box {
  height: 161px !important;
}
.btnPanel {
  padding-top: 32px; display: flex; justify-content: end; width: 100%; 
  // max-width: 768px; 
  margin: 0 auto;
}
.scencelink {
  margin-right: 10px; height: 42px; display: flex; align-items: self-end; text-decoration: underline;
}
.goAgentsBtn {
  display: flex;
  padding: 10px 28px 10px 16px;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
  color: #1C2024;
  background: linear-gradient(69deg, rgba(61, 187, 215, 0.10) 2.55%, rgba(54, 74, 253, 0.10) 32.25%, rgba(183, 110, 241, 0.10) 120.42%);
  font-size: 16px;
  position: relative;
  border: 1px solid transparent;
  line-height: 20px;
  &::after {
    content: '';
    width: 6px;
    height: 6px;
    border-top: 1px solid #1C2024;
    border-right: 1px solid #1C2024;
    transform: rotate(45deg) translateY(-50%);
    // transition: all 0.3s linear;
    position: absolute;
    top: 50%;
    right: 20px;
  }
  &:hover{
    &::after {
      animation: move 1s linear infinite;
      border-color: #364afd;
    }
    border: 1px solid rgba(53, 107, 253, 0.15);
    background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}


</style>