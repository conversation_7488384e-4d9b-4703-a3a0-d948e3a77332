<!-- 有聊天的界面 -->
<template>
  <div class="chat-page-container">
    <div class="left-panel" :class="{ 'collapsed': !isLeftPanelExpanded }" v-show="fileList.length > 0"
      :style="{'left': leftpanelX + 'px' }"  
    >
      <!-- <span v-if="!isLeftPanelExpanded" @click="toggleLeftPanel">文件</span> -->
      <com-file :fileList="fileList" :messageId="messageId" @toggleLeftPanel="toggleLeftPanel" v-show="isLeftPanelExpanded" />
    </div>
    <div class="main-panel">
      <com-msg-tit :title="chatTitle" :messageId="messageId" @titleChange="handleTitleChange" />
      <main-right-sse
      pageName="chat"
      @titleChange="handleTitleChange"  ref="mainRightSseRef" :robotobj="robotobj" :messageId="messageId" :hisMesLoading="hisMesLoading" :recommendStatus="controlBtnsObj.recommendStatus" />
    </div>
    <rightPanel :class="isRightPanelExpanded ? 'expanded' : 'collapsed'" :rightPanelObj="rightPanelObj" />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router';
import mainRightSse from './main-right-sse'
import comMsgTit from '@/components/com-msg-tit.vue'; // 这里缺少了导入组件的名称，应该是 comSend
import comFile from '@/components/com-file.vue';
import rightPanel from '@/views/right-panel.vue';
import { messageDetail, getMessageRobot, getFileList } from "@/api/index.js"; // 导入聊天记录、查询机器人助手
import Bus from "@/utils/bus.js";

const route = useRoute()

// 正式 134635 测试 29642
const messageId = ref() // 参数传递
watch(() => route.params.messageId, (newVal) => {
  if (newVal) {
    messageId.value = parseInt(newVal)
    messageRobot()
    // chooseHistory()
  }
})

const chatTitle = ref('')
const handleTitleChange = (newTitle) => {
  chatTitle.value = newTitle.substr(0, 20)
}
// messageId, robotId, robotName
const robotobj = ref({'robotId': 1})
// robotId 通过 messageId 获取 // == todo ==

const historyList = ref([])
const hisMesLoading = ref(false)
const chooseHistory = () => {

  hisMesLoading.value = true

  messageDetail({
    page: 1,
    pageSize: 100000,
    messageId: messageId.value,
  }).then(res => {
    hisMesLoading.value = false
    if (res.code === 200) {
      let msgListDetailList = res.data.messageDetailVOList.map((item, idx) => {
        if(item.contentText !== null && typeof item.contentText === 'object' && item.contentText.type === 'answerText') {
          item.contentText = item.contentText.content
        }
        return {
          // ...item,
          file: {
            status: 'success',
            name: item.fileTitle,
            size: item.messageSize,
            successId: item.messageDetailId
          },
          type: item.type === 1 ? 'gpt' : 'ask', // 用户类型  0用户 1系统
          converseType: item.type, //消息类型 0文字 1图片链接 2 文本连接 3语音连接 // 0 普通 1 图片 2 企标 3 文档 
          fileTitle: item.fileTitle, //文件名称
          messageSize: item.messageSize, // 文件大小kb
          content: item.contentText, // 问答内容
          commentStatus: item.commentStatus, //点赞状态
          msgDetailId: item.messageDetailId, // 具体编号
          // annotationList: JSON.parse(item.messageFile),
          reasoningText: item.reasoningText,
          webSearchText: item.webSearchText,
          sourceDataText: item.sourceDataText,
          modelName: item.modelName
        }
      })
      chatTitle.value = res.data.messageName
      Bus.$emit('historyList', msgListDetailList)
    }
  }).catch(e => {
    hisMesLoading.value = false
  })
}

// 控制按钮是否开启对象
const controlBtnsObj = ref({
  'fileStatus': 0, // 上传文件按钮 0开启，1关闭
  'reasoningStatus': 0, // 深度思考按钮
  'recommendStatus': 0, // 相似问是否开启
  'webSearchStatus': 0 // 联网搜索
})

// 获取根据messageId获取机器人相关信息
const messageRobot = () => {
  messageId.value = parseInt(route.params.messageId)
  getMessageRobot({
    messageId: messageId.value
  }).then(res => {
    if(res.code == 200) {
      robotobj.value.robotId = res.data.robotId
      robotobj.value.askType = res.data.askType
      controlBtnsObj.value.reasoningStatus = res.data.reasoningStatus
      controlBtnsObj.value.recommendStatus = res.data.recommendStatus
      controlBtnsObj.value.webSearchStatus = res.data.webSearchStatus
      controlBtnsObj.value.fileStatus = res.data.fileStatus
      Bus.$emit('controlBtnsObjChange',controlBtnsObj.value)
      chooseHistory()
    } 
  }).catch(e => {
    console.error(e)
  })
}

// 获取文件列表
const fileList = ref([])
const getFileListFn = () => {
  getFileList({ messageId: route.params.messageId }).then(res => {
    fileList.value = res.data;
    Bus.$emit('filenum', fileList.value.length)
  })
}

const isLeftPanelExpanded = ref(false)
const toggleLeftPanel = (status) => {
  if(status !== undefined) {
    isLeftPanelExpanded.value = status
  } else {
    isLeftPanelExpanded.value = !isLeftPanelExpanded.value
  }
}
watch(isLeftPanelExpanded, (newValue, oldValue) => {
  // console.log('面板状态变化:', oldValue, '→', newValue)
  // 在这里执行你的逻辑
  // if(newValue) {
    leftCountFn()
  // }
})



const isRightPanelExpanded = ref(false)
let rightPanelObj = ref({})

// 左侧panel位于X的位置，调整屏幕计算
const leftpanelX = ref(0)
const leftCountFn = () => {
  // if(isLeftPanelExpanded.value) {
    const left = document.getElementsByClassName('send-box')[0].getBoundingClientRect().left
    const leftPanelWidth = document.getElementsByClassName('left-panel')[0].offsetWidth
    // console.log(left, 'left')
    leftpanelX.value = (left - 220 - 108 - 30)
    if(leftpanelX.value < 0 && isLeftPanelExpanded.value) {
      leftpanelX.value = 0
      document.getElementsByClassName('chat-page-container')[0].style.paddingLeft = '220px'
    } else {
      document.getElementsByClassName('chat-page-container')[0].style.paddingLeft = '0px'
    }
}
function throttle(func, limit) {
    let inThrottle;
    return function() {
        if (!inThrottle) {
            func();
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

const resizeListener = throttle(() => {
  leftCountFn();
}, 200);


onMounted(() => {
  // 使用节流技术
  window.addEventListener('resize', resizeListener); // 每200毫秒执行一次

  messageRobot()
  // chooseHistory()
  getFileListFn()

  // dom添加后，计算leftpanelX的值
  nextTick(() => {
    leftCountFn ()
  })

  Bus.$on('leftPanelChange', (status) => {
    toggleLeftPanel(status)
  })
  Bus.$on('fileListChange', (statusString) => {
    if(statusString.startsWith('id')) {
      let fileId = statusString.substr(2)*1 // 去掉前缀'id'
      fileList.value = fileList.value.filter(file => file.messageFileId !== fileId) // 根据fileId过滤文件列表
      Bus.$emit('filenum', fileList.value.length)

    } else {
      getFileListFn()
      isLeftPanelExpanded.value = true
    }
  })
  Bus.$on('expand', (item) => {
    isRightPanelExpanded.value = true
    rightPanelObj.value = item
  })
  Bus.$on('collapsed', () => {
    isRightPanelExpanded.value = false
    rightPanelObj.value = {}
  })
  Bus.$on('titleChange', (title) => {
    handleTitleChange(title)
  })
})

onUnmounted(() => {
  Bus.$off('leftPanelChange')
  Bus.$off('fileListChange')
  Bus.$off('expand')
  Bus.$off('collapsed')
  Bus.$off('titleChange')
  window.removeEventListener('resize', resizeListener)
})
</script>

<style lang="less" scoped>
.chat-page-container{
  display: flex;
  flex-direction: row;
  height: 100%;
  width: calc(100% - 108px) !important;
  margin-left: 108px;
  position: relative;

}
.left-panel {
  right: 0px;
  bottom: 0px;
  z-index: 100;
  width: 220px;
  text-align: left;
  margin: 0px 0px 32px 20px;
  height: calc(100% - 70px);
  overflow: hidden;
  align-self: end;
  position: absolute;
  transition: all 0.2s linear;
  &.collapsed {
    position: absolute;
    width: 220px;
    height: 0px;
    // left: 0px;
    // top: 34px;
  }
}
.main-panel { 
  flex: 1;
  display: flex;
  flex-direction: column; 
  height: 100%;
  max-width: 100%;
  /deep/.send-wrap {
    padding-bottom: 32px;
  }
}

</style>