<template>
  <div class="login-wrap">
    <div class="login-icon">
      <span />灵犀
    </div>
    <div class="main-wrap">
      <div class="desc-panel" style="display: none;">
        <div class="title">
          欢迎使用灵犀-长城办公AI
        </div>
        <div class="title-eng">
          Welcome to<br />
          Global Content Asset<br />Management Platform
        </div>
      </div>
      <div class="bg">
        <div class="login-box">
          <div class="gw-logo">
            <span />长城统一身份认证
          </div>
          <div class="title">账号登陆</div>
          <div class="login-item" style="margin-top:50px;">
            <a-input placeholder="请输入输入工号/手机号/邮箱" v-model:value="userName" @focus="inpFocusUserName"
              @pressEnter="handleLogin" :class="{ 'error': userNameError }" />
          </div>
          <desc class="error-text" v-if="userNameError">请输入用户名</desc>
          <div class="login-item">
            <a-input placeholder="请输入密码" v-model:value="password" @focus="inpFocusPassword" type="password"
              @pressEnter="handleLogin" :class="{ 'error': passwordError }" />
          </div>
          <desc class="error-text" v-if="passwordError">请输入密码</desc>
          <div class="login-error">
            {{ errorTxt }}
          </div>
          <div class="login-btn" @click="handleLogin">
            <LoadingOutlined style="margin-right:10px;" v-if="loginThoutle" />登录
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, reactive, onBeforeUnmount, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { login, loginSSO } from '@/api'
import { setCookie, getCookie } from '@/utils/common';
import {
  LoadingOutlined
} from '@ant-design/icons-vue';
const route = useRoute()
const router = useRouter()
const userName = ref(``)
const password = ref(``)
const errorTxt = ref(``)
const userNameError = ref(false)
const passwordError = ref(false)
const inpFocusUserName = () => {
  errorTxt.value = ''
  userNameError.value = false
}
const inpFocusPassword = () => {
  errorTxt.value = ''
  passwordError.value = false
}

let loginThoutle = ref(false)
const handleLogin = (token) => {
  if (loginThoutle.value) return // 节流请求
  if (!userName.value.length) {
    // errorTxt.value = '请输入用户名'
    userNameError.value = true
  }
  if (!password.value.length) {
    // errorTxt.value = '请输入密码'
    passwordError.value = true
  }
  if (userNameError.value || passwordError.value) return
  loginThoutle.value = true
  login({
    loginName: userName.value,
    password: password.value
  }).then(res => {
    console.log(res, 'res')
    if (~~res.ret !== 200) { //token验证失败 没有权限
      loginThoutle.value = false
      console.log('验证失败 没有访问权限')
      errorTxt.value = res.msg
      // userNameError.value = true
      // passwordError.value = true
      return
    }
    setCookie('userToken', res.data.userToken, 1); // 通过存入 cookie 发验证token给后端
    // localStorage.setItem('userToken', JSON.stringify(res.data.userToken))
    localStorage.setItem('userName', JSON.stringify(res.data.userName))
    localStorage.setItem('userToken', JSON.stringify(res.data.userToken))
    router.push('/home')
  })
}
onMounted(() => {
  // 判断是否已经登陆
  console.log('加载loginViewn')
  const LSToken = getCookie('userToken')
  const token = JSON.parse(localStorage.getItem('userToken'))
  if (LSToken && LSToken.length && token) {
    router.push('/home')
    // this.handleLogin(JSON.parse(LSToken))
    return
  }
})
</script>

<style lang="less" scoped>
.login-wrap {
  width: 100%;
  height: 100%;
  background: url('@/assets/image/ibg.png') center no-repeat;
  background-size: cover;
  overflow: hidden;
  position: relative;
}

.login-icon {
  position: absolute;
  top: 40px;
  left: 40px;
  display: flex;
  align-items: center;
  color: #000;
  font-size: 28px;

  span {
    margin-right: 10px;
    display: inline-block;
    width: 48px;
    height: 48px;
    background: url('@/assets/image/logo.png') center no-repeat;
    background-size: contain;
  }
}

.main-wrap {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1060px;
  height: 628px;
  margin-left: -530px;
  margin-top: -314px;
}

.desc-panel {
  position: absolute;
  top: 27px;
  left: 0;
  width: 780px;
  height: 574px;
  // background: url('@/assets/image/login_bg_panel.png') center no-repeat;
  font-size: 48px;
  padding: 80px 70px;
  text-align: left;
  color: #fff;

  .title {
    margin-top: 121px;
  }

  .title-eng {
    display: none;
    font-size: 28px;
    margin-top: 46px;
  }
}

.login-box {
  position: absolute;
  top: 0;
  right: 0;
  // transform: translate3d(-50%, -50%, 0);
  width: 573px;
  height: 528px;
  background: #fbfbff; /// rgba(255, 255, 255, 0.2);
  background-image: linear-gradient(160deg, #c9c2fe40 0%, #fbfbff 25%, #fbfbff 70%, #c9c2fea1 100%);
  backdrop-filter: blur(40px);
  /* Note: backdrop-filter has minimal browser support */

  border-radius: 10px;
  filter: drop-shadow(-2px 0px 5px rgba(4, 4, 4, 0.15)); // drop-shadow(-4px 0px 20px rgba(34, 44, 196, 0.2));
  padding: 105px 80px;
  border: 1px solid #d7d4f9;

  .gw-logo {
    position: absolute;
    top: 30px;
    left: 40px;
    font-size: 14px;
    color: #000;
    display: flex;
    align-items: center;

    span {
      width: 32px;
      height: 22px;
      display: inline-block;
      background: url('@/assets/image/icon_gw.png') center no-repeat;
      background-size: cover;
      margin-right: 10px;
      ;
    }
  }

  .title {
    font-size: 32px;
    color: #000;
  }


}

.bg {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 573px;
  height: 528px;
  // background: url('@/assets/image/login_bg2.png') center no-repeat;
  background-size: cover;
  margin-left: -236px;
  margin-top: -264px;
  border-radius: 40px;
}

.login-item {
  width: 100%;
  font-size: 20px;
  display: flex;
  align-items: center;
  margin-top: 30px;

  // &:nth-child(1) {}

  // &:nth-of-type(1) {
  //   margin-top: 60px;
  // }

  span {
    display: inline-block;
    width: 120px;
    text-align: right;
  }

  input {
    height: 54px;
    line-height: 54px;
    border: 1px solid hsl(0deg 2.4% 67.57% / 40%);
    border-radius: 8px;
    padding: 0 20px;
    color: #000;
    font-size: 14px;
    background: transparent;

    &.error {
      border-color: #ff0000;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

.error-text,
.login-error {
  display: block;
  position: absolute;
  font-size: 14px;
  color: #ff0000;
  text-align: left;
}

.login-btn {
  user-select: none;
  cursor: pointer;
  width: 413px;
  height: 54px;
  line-height: 54px;
  left: 1092px;
  top: 630px;
  background: rgb(45 71 244 / 80%);
  border-radius: 8px;
  font-size: 16px;
  color: #fff;
  margin-top: 40px;

  &:hover {
    background: #2D47F4;
  }
}
</style>