<template>
  <div class="agent-container">
    <div class="agent-layoutcss">
      <div class="agent-header">
        <img src="@/assets/image/agent-header.png" />
        <div class="agent-search">
          <input
            class="agent-search-input"
            type="text"
            placeholder="搜索智能体，回车搜索"
            @keyup.enter="handleSearch"
            v-model="searchQuery"
          />
          <img @click="handleSearch" src="@/assets/image/icon_his_search.svg" class="search-icon" />
        </div>
      </div>
      <div class="agent-kinds">
        <div class="kinds-list">
          <div class="scale-box">
            <a :class="{'cur': agentList.length !== 0 }" @click="loadAgentList()"><b>全部</b></a>
            <a v-for="(item,idx) in classArr" :href="`#${item.classifyId}`" :key="idx"><b>{{ item.classifyName }}</b></a>
          </div>
        </div>
        <!-- <Tooltip title="敬请期待" placement="top" > -->
          <!-- class="disabledlink" style="cursor: not-allowed;" -->
          <a @click="viewWaitFn()" ><b>开发者门户</b></a>
        <!-- </Tooltip> -->
      </div>
      <Spin v-if="agentLoading" tip="智能体加载中..." style="transform: translate(-50%, -50%); position: absolute; top: 50%; left: 50%;" />
      <template v-else>
        <template v-if="hotListArr.length > 0">
          <p class="title-line" :id="hotListArr.classifyId">热门推荐<span class="moretag"></span></p>
          <div class="hot-recommend">
            <div class="hot-item" @click="viewChat(item.robotId, item.askType)" v-for="(item, index) in hotListArr[0] && hotListArr[0].agentCenterSonVOList" :key="index">
              <span class="rank-num">{{ index + 1  }}</span>
              <span class="helper-img">
                <img :src="'/agentImg' + item.robotImage.substring(item.robotImage.indexOf('/agent-oss'))" alt="热门推荐" />
              </span>
              <p class="helper-name">{{ item.robotName }}</p>
              <p class="helper-tag">
                {{ item.labelList? item.labelList.join(' / '): ''  }}
              </p>
              <p class="helper-use">使用次数：{{ item.useNum }}</p>
              <a class="use-btn">
                <span class="warpper"><span class="inner">立即使用</span></span>
              </a>
            </div>
          </div>
        </template>
        <div v-if="agentArrList.length > 0" v-for="agentArr in agentArrList" :key="agentArr.classifyId" :id="agentArr.classifyId">
          <template v-if="agentArr.agentCenterSonVOList.length > 0">
            <p class="title-line">{{ agentArr.classifyName }}<span class="moretag"></span></p>
            <div class="agent-list">
              <div class="agent-item" @click="viewChat(item.robotId, item.askType)" v-for="(item, i) in agentArr.agentCenterSonVOList" :key="i">
                <div class="agent-item-top">
                  <img :src="'/agentImg' + item.robotImage.substring(item.robotImage.indexOf('/agent-oss'))"  :alt="agentArr.classifyName" />
                  <div class="agent-info">
                    <div class="agent-name">
                      <span class="agent-name-span">{{ item.robotName }}</span>
                      <p class="agent-tag">
                        <span class="tag" v-for="(tag, index) in item.subscriptList" :key="index">{{ tag }}</span>
                      </p>
                    </div>
                    <p class="agent-desc" :title="item.robotIntroduce">{{ item.robotIntroduce }}</p>
                  </div>
                </div>
                <div class="collectbox">
                  <div class="authorSpan">作者：{{ item.robotPower }}</div>
                  <div class="collectSpan" @click.stop="toggleCollect(item)">
                    <img src="@/assets/image/up.svg" alt="收藏" v-if="item.collectStatus == 1" />
                    <img src="@/assets/image/upcur.svg" v-else alt="收藏" />
                    <span>收藏</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
        <div v-if="searchResultList.length > 0">
          <p class="title-line">搜索结果<span class="moretag"></span></p>
          <div class="agent-list">
            <div class="agent-item" @click="viewChat(item.robotId, item.askType)" v-for="(item, index) in searchResultList" :key="index">
              <div class="agent-item-top">
                <img :src="'/agentImg' + item.robotImage.substring(item.robotImage.indexOf('/agent-oss'))"  />
                <div class="agent-info">
                  <div class="agent-name">
                    <span class="agent-name-span">{{ item.robotName }}</span>
                    <p class="agent-tag">
                      <span class="tag" v-for="(tag, index) in item.subscriptList" :key="index">{{ tag }}</span>
                    </p>
                  </div>
                  <p class="agent-desc" :title="item.robotIntroduce">{{ item.robotIntroduce }}</p>
                </div>
              </div>
              <div class="collectbox">
                <div class="authorSpan">作者：{{ item.robotPower }}</div>
                <div class="collectSpan" @click.stop="toggleCollect(item)">
                  <img src="@/assets/image/up.svg" alt="收藏" v-if="item.collectStatus == 1" />
                  <img src="@/assets/image/upcur.svg" v-else alt="收藏" />
                  <span>收藏</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="empty-box" v-if="searchResultList.length === 0 && !loading && hotListArr.length == 0 && agentArrList.length == 0">
          <!-- <img src="@/assets/image/empty.png" alt="空" /> -->
          <p>暂无相关智能体，去【全部】试试其他智能体吧～</p>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { message, Modal, Spin, Tooltip } from "ant-design-vue";
import { loadAgent, searchAgent, collectControl } from "@/api/index.js";

const router = useRouter();

const viewChat = (robotId, askType) => {
  if(askType == 8) {
    router.push({ path: '/ai-image' });
  } else {
    router.push({ path: '/chat-hello', query: { robotId: robotId, askType: askType } });
  }
};

// 跳转AI门户
const viewWaitFn = () => {
  // message.warn('功能暂未开放, 敬请期待！');
  window.open('http://intelligence.test.paas.gwm.cn/index', '_blank')
}

// 收藏、取消收藏
const toggleCollect = (item) => {
  collectControl({
    "robotId": item.robotId, //智能体id
    "status": item.collectStatus == 0 ? 1 : 0, //0收藏 1取消收藏
  }).then((res) => {
    if(res.code == 200) {
      message.success('操作成功');
      item.collectStatus = item.collectStatus == 1 ? 0 : 1;
    }
  }).catch((err) => {
    console.error(err);
  });
}

const classArr = computed(() => {
  return agentList.value.filter((item, index) => item.agentCenterSonVOList && item.agentCenterSonVOList.length !== 0);
});

const hotListArr = computed(() => {
  return agentList.value.filter((item, index)  => item.classifyId == 1);
});

const agentArrList = computed(() => {
  return agentList.value.filter((item, index) => index != 0)
});

const agentLoading = ref(false);

const searchResultList = ref([]);
const searchQuery = ref('');

// 根据关键字——>搜索智能体列表
const handleSearch = () => {
  if (!searchQuery.value) {
    loadAgentList()
    return;
  }
  agentLoading.value = true;
  searchAgent({ word: searchQuery.value }).then(res => {
    agentLoading.value = false;
    if (res.code === 200) {
      agentList.value = [];
      searchResultList.value = res.data;
    }
  }).catch(error => {
    agentLoading.value = false;
    console.error(error);
  });
};

const agentList = ref([]);
// 加载智能体列表
const loadAgentList = () => {
  agentLoading.value = true;
  searchResultList.value = [];
  searchQuery.value = '';
  loadAgent({}).then(res => {
    agentLoading.value = false;
    if (res.code === 200) {
      agentList.value = res.data;

    }
  }).catch(error => {
    agentLoading.value = false;
    console.error(error);
  });
}

onMounted(() => {
  loadAgentList()
});


</script>
<style lang="less">
.ant-tooltip-content {
  border-radius: 5px; overflow: hidden;
}
</style>
<style lang="less" scoped>
.agent-container {
  margin-left: 108px;
  height: 100%;
  overflow-y: auto;
  position: relative;
  .agent-layoutcss {
    max-width: 1305px;
    width: 71.27%;
    margin: 24px auto 0px;
    padding-bottom: 20px;
  }
  .agent-header {
    img {
      width: 100%;
    }
    color: white;
    border-radius: 32px;
    border: 1.5px solid #fff;
    box-shadow: 0px 8px 30px 0px rgba(0, 45, 134, 0.05);
    overflow: hidden;
    position: relative;
    user-select: none;
    .agent-search {
      position: absolute;
      top: 20px;
      right: 25px;
      border-radius: 8px;
      border: 1px solid #e0e1e6;
      background: #fff;
      box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.05);
      backdrop-filter: blur(4.300000190734863px);
      padding: 8px 16px;
      width: 220px;
      display: flex;
      input {
        border: none;
        outline: none;
        flex: 1;
        height: 18px;
        line-height: 18px;
        color: #000;
      }
      .search-icon {
        width: 18px;
        height: 18px;
        cursor: pointer;
      }
    }
  }
  .agent-kinds {
    margin-top: 28px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .kinds-list {
      display: flex;
      // justify-content: space-between;
      margin-right: 30px;
      align-items: flex-start;
      .scale-box {
        display: flex; overflow-x: auto;
      }
      .more {
        margin-left: 16px;
      }
    }
    a {
      padding: 4px 16px;
      background-color: #fff;
      border-radius: 8px;
      border: 1px solid #fcfcfd;
      margin-right: 16px;
      cursor: pointer;
      font-weight: 500;
      flex-shrink: 0;
      color: #60646C;
      &:not(.disabledlink).cur b, &:not(.disabledlink):hover b {
        background: linear-gradient(
          69deg,
          #3dbbd7 2.55%,
          #364afd 32.25%,
          #b76ef1 120.42%
        );
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      &:last-child {
        margin-right: 0px;
      }
    }
  }
  .title-line {
    padding: 20px 0px;
    font-size: 18px;
    line-height: 26px;
    color: #000;
    text-align: left;
    display: flex;
    align-items: center;
    font-weight: 500;
    span.moretag {
      width: 10px;
      height: 10px;
      display: inline-block;
      border-top: 1px solid #60646c;
      border-right: 1px solid #60646c;
      transform: rotate(45deg);
    }
  }
  .hot-recommend {
    display: flex;
    justify-content: space-between;
    .hot-item {
      width: calc( 20% - 8px );
      border-radius: 16px;
      background-color: #fff;
      padding: 8px;
      position: relative;
      cursor: pointer;
      transition: all 0.1s linear;
      box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.05);
      span.rank-num{
        position: absolute;
        top: 0px;
        left: 0px;
        border-radius: 16px 0px 16px 0px;
        width: 30px;
        height: 30px;
        color: #fff;
        line-height: 30px;
        font-weight: 500;
      }
      .helper-name {
        font-size: 16px; color: #000; font-weight: bolder; margin-top: 9px;
      }
      .helper-img {
        display: block;
        margin: 0 auto;
        width: 68px;
        height: 68px;
        border-radius: 100%;
        overflow: hidden;
      }
      .helper-tag{
        color: #60646C; font-size: 14px; margin-top: 1px;
      }
      .helper-use {
        font-size: 12px; color: #60646C; margin-top: 2px;
      }
      .use-btn {
        display: inline-block;
        background: linear-gradient(89deg, #0FD3FF -3.47%, #364AFD 22.92%, #4C4EFB 59.8%, #B457FF 97.6%);
        border-radius: 16px; padding: 1px; overflow: hidden;
        span.warpper {
          background: #fff; display: inline-block; border-radius: 16px;
        }
        span.inner {
          font-size: 14px; background: linear-gradient(89deg, #0FD3FF -3.47%, #364AFD 22.92%, #4C4EFB 59.8%, #B457FF 97.6%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          line-height: 18px;
          margin: 5px 12px;
          display: inline-block;
        }
        margin: 8px 0px;
      }
      &:first-child .rank-num {
        background-color: #E4453D;
      }
      &:nth-child(2) .rank-num {
        background-color: #F1913C;
      }
      &:nth-child(3) .rank-num {
        background-color: #F6C042;
      }
      &:nth-child(4) .rank-num {
        background-color: #EEF0FC;
        color: #60646C;
      }
      &:nth-child(5) .rank-num {
        background-color: #EEF0FC;
        color: #60646C;
      }
      &:hover {
        // transform: scale(0.99);
        background-color: #ffffff70;
        .helper-img img {
          transition: all 0.2s linear;
          transform: scale(1.1);
        }
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
        .helper-name {
          color: #2237ef;
        }
        .use-btn {
          transform: scale(0.97);
          transition: all 0.2s linear;
          .warpper {
            background-color: #f6f7f8;
          }
        }
        // .use-btn {
        //   transition: all 0.2s linear;
        //   background: linear-gradient(-89deg, #0FD3FF -3.47%, #364AFD 22.92%, #4C4EFB 59.8%, #B457FF 97.6%);
        //   span.inner {
        //     transition: all 0.1s linear;
        //     background: linear-gradient(289deg, #0FD3FF -3.47%, #364AFD 22.92%, #4C4EFB 59.8%, #B457FF 97.6%);
        //     background-clip: text;
        //     -webkit-background-clip: text;
        //     -webkit-text-fill-color: transparent;
        //   }
        //   box-shadow: 0 0 0 2px #00000014;
        // }
      }
    }

  }
  .agent-list {
    text-align: left; display: grid; grid-template-columns: repeat(3, 1fr); /* 定义三列，每列相等宽度 */
    gap: 16px; 
    .agent-item {
      display: flex; flex-direction: column; padding: 16px; border-radius: 16px; background-color: #fff; transition: all 0.1s linear; cursor: pointer; justify-content: space-between;
      &:hover {
        transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); background-color: #ffffff2f;
        .agent-name-span {
          color: #2237ef;
        }
      }
      &:last-child {
        margin-right: 0;
      }
    }
    .agent-item-top { 
       display: flex;
       img {
        width: 58px; height: 58px;
       }
      
    }
    .agent-info { 
      flex: 1; margin-left: 8px;
      .agent-name {
        display: flex; margin-bottom: 8px;
        .agent-tag {
          span{
            font-size: 10px; color: #fff; border-radius: 4px; padding: 2px 4px;
            background: linear-gradient(90deg, #FF8969 0%, #FF5151 100%); margin-left: 8px;
          }
        }
      }
      .agent-name-span { flex: 1; font-size: 16px; color: #000; font-weight: 500; overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1; /* 限制文本显示的行数为 1行 */
        text-overflow: ellipsis; }

      .agent-desc {
        color: #8B8D98;
        font-size: 12px;
        font-weight: 400;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 限制文本显示的行数为 3 行 */
        text-overflow: ellipsis;
        word-break: break-all;
      }
    }
    .collectbox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 5px;
      line-height: 14px;
      padding-left: 66px;
      
      .authorSpan {
        color: #8B8D98;
        font-size: 12px;
      }
      div.collectSpan {
        display: flex; align-items: center; position: relative; user-select: none; 
        &:hover {
          // div {
            transform: scale(1.1);
            transition: transform 0.1s ease
          // }
        }
        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100%; /* 初始宽度 */
          height: 100%; /* 初始高度 */
          background: radial-gradient(circle at center, rgba(255, 255, 255, 0.6) 50%, transparent);
          opacity: 0;
          transition: opacity 0.2s ease;
          // border-radius: 100%;
        }
        &:active::before {
          opacity: 1;
          animation: glow 0.3s ease-out forwards;
        }
        @keyframes glow {
          0% {
            transform: translate(-50%, -50%) scale(0); /* 从中心开始 */
          }
          100% {
              transform: translate(-50%, -50%) scale(1.5); /* 放大到2倍 */
          }
        }
      }
      img {
        align-self:flex-start;
      }
      span {
        font-size: 12px;
        color: #8B8D98;
        padding-left: 4px;
      }
      
    }
  }
  .empty-box {
    padding-top: 30px;
  }
}
</style>
