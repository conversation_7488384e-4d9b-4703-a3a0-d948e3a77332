<template>
  <div class="main-layout">
    <sidebar class="sidebarwap" :silderbarTag="silderbarTag" :silderbarBtnVisiable="silderbarBtnVisiable" @sidebarToggleFn="sidebarToggleFn"/>
    <!-- <leftPanel /> Assuming leftPanel is a component that should be included here -->
    <router-view :class="{'sidebar_in': silderbarTag}" />
  </div>
</template>

<script setup>
import sidebar from '@/components/sidebar-left.vue';
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const router = useRouter()
const route = useRoute() // 使用 useRoute 获取当前路由信息
console.log('route.path', route.path) // 输出当前路由路径
const robotId = ref(route.query.robotId)
const askType = ref(route.query.askType)

const silderbarTag = ref()
const silderbarBtnVisiable = ref()

// 监听整个路由对象的变化
watch(route, (newRoute, oldRoute) => {
  // AI阅读 // 添加上AI翻译的内容
  if(newRoute.query.askType == 5 && newRoute.path == '/ai-reader') {
    silderbarTag.value = true
    silderbarBtnVisiable.value = true
  } else {
    silderbarTag.value = false
    silderbarBtnVisiable.value = false
  }
  // console.log('路由发生变化:', {
  //   askType: newRoute.query.askType,
  //   from: oldRoute.path,
  //   to: newRoute.path
  // })
}, { immediate: true })

// if(askType.value == 5 && route.path == '/ai-reader') {

//   silderbarTag.value = false
// } else {
//   silderbarTag.value = true
// }

const sidebarToggleFn = (val) => {
  silderbarTag.value = val
}


</script>
<style lang="less" scoped>

.main-layout {
  width: 100%;
  height: 100%;
  min-height: 665px;
  position: relative;
  .sidebarwap {
    position: absolute;
    left: 24px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1000;
  }
  .main-panel {
    width: 100%;
    height: 100%;
    padding-left: 108px;
    position: relative;
    max-width: 100%;
  }

  .sidebar_in {
    padding-left: 45px;
  }

}

</style>