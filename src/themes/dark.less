/* dark-theme.css */

#app.dark-theme {
  --primary-color: #fff;
  --primary-color-op08: rgba(255, 255, 255, 0.808);
  --text-color: #fff;
  --bg-color: #001745;
  --text-color-op08: rgba(255, 255, 255, 0.808);
  --primary-purple: #5416FF;
  --text-color-2: rgba(181, 191, 214, 1);
  --text-color-black: #001745;

  // background-color: var(--main-color);
  .main-wrap {
    background-color: var(--bg-color);
    background-image: url('@/assets/image/dark_bg.svg');

    * {
      &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.5);
      }
    }
  }

  .left-title {
    color: var(--text-color);

    span.anticon {
      color: var(--text-color);
    }

    .info-icon {
      background: url('@/assets/image/icon_ques.png');
    }

  }

  .helper_center_enter {
    background: linear-gradient(135deg, #316bff9e 0%, #c441f4b8 100%);
  }

  .main-wrap-left {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.03) 100%);

    .helper-title .title {
      color: var(--text-color-op08);

      span.fast_tag {
        background-image: url('@/assets/image/history_icon.png');
      }
    }

    .helper-title {
      .control-box {
        .add {
          background-image: url('@/assets/image/history_add_w.svg');
        }

        .arrow {
          background-image: url('@/assets/image/history_down_w.svg');
        }

        .arrow {
          background-image: url('@/assets/image/history_down_w.svg');
        }

        .search {
          background-image: url('@/assets/image/history_search_w.svg');
        }

        // .handle-icon {
        //   background-image: url('@/assets/image/icon_approval_w.svg');
        // }
      }

      .helper-active {
        color: var(--text-color);
      }
    }



    .history-item {
      &.active {
        background: rgba(54, 86, 255, 0.2);
      }

      .title-box {
        color: var(--text-color)
      }

      .handle-box i:hover {
        background-color: rgb(190 190 190 / 20%);
      }

      .list-content {
        .edit-input {
          border-color: rgb(190 190 190 / 70%);
          color: var(--text-color);
        }
      }


    }
  }

  .comment-box {
    .handle-icon:not(.active) {
      background-image: url('@/assets/image/icon_approval_w.svg');

      &.deny {
        background-image: url('@/assets/image/icon_deny_w.svg');
      }
    }

    .devide {
      background-color: rgba(181, 191, 214, 1);
      ;
    }
  }

  .filebox .filecontent {
    background: linear-gradient(180deg, #4F32C3 0%, rgba(79, 50, 195, 0.3) 100%);

    .contit {
      color: var(--text-color);
    }

    .statetag {
      border-bottom-color: var(--text-color);
    }
  }

  .main-wrap-right {
    background: linear-gradient(165.98deg, rgba(50, 55, 166, 0.2) 5.6%, rgba(255, 255, 255, 0) 143.97%),
      linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);

  }

  .hello_panel .hello-panel-wrapper {
    // background: #c6d1ed87;
    background: url('@/assets/image/hellowpanel_dark_bg.png') no-repeat;
    background-position: center;
    background-size: cover;
  }

  .main-wrap-right-content {
    border-top: 0;
  }

  .pdf-tool-box .anticon {
    color: var(--text-color)
  }

  .doc-total-wrap {
    &.showDoc {
      background: linear-gradient(165.98deg, rgba(50, 55, 166, 0.2) 5.6%, rgba(255, 255, 255, 0) 143.97%), linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    }

    .close-btn {
      background: var(--primary-color);

      .anticon {
        color: black;
      }
    }
  }

  .converse-wrap {
    .converse-item {
      &.ques-box {
        .content {
          background-color: rgba(255, 255, 255, 0.1);
          color: var(--text-color);

          .icon-magicward {
            background-image: url("@/assets/image/icon_magic_ward_dark.svg");
          }
        }
      }

      &.answer-box {
        .content {
          border: 0.5px solid #ffffff20;
          background: linear-gradient(180deg, rgba(79, 50, 195, 0.6) 0%, rgba(79, 50, 195, 0.18) 100%),
            linear-gradient(132.42deg, rgba(255, 255, 255, 0.2) 10.81%, rgba(255, 255, 255, 0) 75.78%);

          .model-tag {
            color: rgba(255, 255, 255, 40%);
          }
        }


        .handle-box {
          border-color: rgba(255, 255, 255, 0.2);
          color: var(--text-color-2)
        }

        .stop-generate i.regenerate {
          background-image: url('@/assets/image/icon_regenerate_w.svg');
        }

        .stop-generate i.copy {
          background-image: url('@/assets/image/icon_copy_w.svg');
        }

        .stop-generate.is-lastest::after {
          background-color: rgba(181, 191, 214, 1);
        }
      }
    }

    .annotation-box {
      .annotation-box {}

      .doc-link {
        color: var(--text-color);
      }
    }
  }

  .smart-search-wrap .title {
    color: #3656ff;

    &:hover {
      color: var(--primary-purple);
    }
  }

  .smart-search-wrap .annotation-box {
    color: #3656ff;
  }

  .helpermain {
    background: linear-gradient(165.98deg, rgba(50, 55, 166, 0.2) 5.6%, rgba(255, 255, 255, 0) 143.97%), linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);

    .filterbox {
      color: var(--text-color);
    }

    .helper_tit {
      color: var(--text-color);
    }

    .descbox {
      color: var(--primary-color-op08);
    }

    .helperCard {
      background: linear-gradient(0deg, rgba(102, 172, 255, 0.1), rgba(102, 172, 255, 0.1)),
        linear-gradient(0deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));

      h1 {
        color: var(--text-color)
      }

    }

    .addhelper {
      // background: var(--primary-color);
      // color: var(--text-color-black);
    }

    .filters span {
      // background: var(--primary-color);
      // color: var(--text-color-black);
      background: rgba(240, 244, 255, 0.1);
      color: #fff;

      &.active {
        color: #fff;
        background-color: #5416FF;
      }

      &.disabled {
        opacity: 1;
      }
    }

    .ant-tabs-tab {
      // color: var(--text-color)
      color: rgba(201, 205, 212, 1);
    }

    .ant-tabs-ink-bar {
      // background-color: var(--primary-color);
    }
  }

  .pdf-page-box .loading-page {
    color: var(--text-color);
  }

  .login-wrap {
    background-image: url('@/assets/image/bg.png');
    background-color: var(--bg-color);

    .main-wrap {
      background: none;
    }

    .login-icon {
      color: var(--text-color);
    }
  }

  .send-wrap {
    .shortcut {
      background: var(--primary-color);

      &:hover {
        background: var(--primary-purple);
      }
    }

    .shortpath-box {
      position: relative;
      top: 5px;

      &.active {
        height: 145px;
      }

    }

  }

  .main-wrap-left {
    .history-more a {
      color: var(--text-color);
      opacity: .5;
    }
  }

  .theme-switch-box {
    color: var(--text-color-2);
  }

  .ant-input:hover {
    border-color: var(--primary-color);
  }

  .user-box span.avatar {
    // background: var(--primary-color);
  }

  .main-wrap-left .helper-title .title span.fast_tag {
    // 助手icon

  }
}