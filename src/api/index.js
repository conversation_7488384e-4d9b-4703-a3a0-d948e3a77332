import request from '@/utils/request'
// import { getCookie } from '@/utils/common'
// const controller = new AbortController();
// export function axiosController () { // 用于取消请求
//   return controller 
// }

// 术语问答接口
export function termList(data) {
  return request({
    url: '/gbotApi/client/terminology/search',
    method: 'post',
    data
  })
}

// 流程查询接口
export function processList(data) {
  return request({
    url: '/gbotApi/client/agent/process',
    method: 'post',
    data
  })
}


// 获取相似问接口
export function recommendMsg(data) {
  return request({
    url: '/gbotApi/client/agent/recommend',
    method: 'post',
    data
  })
}

//登录
export function login(data) {
  return request({
    url: '/gbotApi/user/login',
    method: 'post',
    data
  })
}

// 首页获取领域上新、动态相关
export function getHomeSuggest(data) {
  return request({
    url: '/gbotApi/client/center/suggest',
    method: 'post',
    data
  })
}

//退出
export function logout(data) {
  return request({
    url: '/gbot<PERSON>pi/auth/logout',
    method: 'post',
    data
  })
}

// 获取智能体 欢迎语
export function getRobotDetail(data) {
  return request({
    url: '/gbotApi/client/center/robotDetail',
    method: 'post',
    data
  })
}

// 用户新建消息框
export function createMsg(data) {
  return request({
    url: '/gbotApi/client/agent/createMessage',
    method: 'post',
    data
  })
}
// 用户发送文本消息
export function askText(data) {
  return request({
    url: '/gbotApi/client/agent/askText',
    method: 'post',
    data,
    signal: data.controller.signal,//controller.signal, // 用于取消请求
    headers: {
      'Content-Type':"application/json; charset=utf-8",
      'noError':true
    }
  })
}

// 以文生图
// export function askImg(data) {
//   return request({
//     url: '/gbotApi/ask/img',
//     method: 'post',
//     signal: data.controller.signal,//controller.signal, // 用于取消请求
//     data
//   })
// }

// 消息记录点赞/倒赞
export function feedbackMessage(data) {
  return request({
    url: '/gbotApi/client/message/feedback',
    method: 'post',
    data
  })
}
// 文本语音播报
export function aloudText(data) {
  return request({
    url: '/gbotApi/client/video/aloud',
    method: 'post',
    responseType: "blob", // 后端返回音频流，转成blob格式页面添加播放
    data,
    headers: {
      'noError':true
    }
  })
}

//企标文档语音播报
export function commonAloud(data) {
  return request({
    url: '/gbotApi/client/video/aloud',
    method: 'post',
    responseType: "blob", // 后端返回音频流，转成blob格式页面添加播放
    data: Object.assign(data,{noMsgTips: true}),
    headers: {
      'noError':true
    }
  })
}

// 用户修改消息标题
export function updateMessageName(data) {
  return request({
    url: '/gbotApi/client/message/updateName',
    method: 'post',
    data
  })
}
// 消息记录删除
export function deleteMessage(data) {
  return request({
    url: '/gbotApi/client/message/delete',
    method: 'post',
    data
  })
}
// 用户查看消息列表
export function messageList(data) {
  return request({
    url: '/gbotApi/client/message/list',
    method: 'post',
    data
  })
}
// 用户查看消息详情
export function messageDetail(data) {
  return request({
    url: '/gbotApi/client/message/detail',
    method: 'post',
    data
  })
}
// 根据messageId 获取消息详情
export function getMessageRobot(data) {
  return request({
    url: '/gbotApi/client/center/messageRobot',
    method: 'post',
    data
  })
}
// 智能体中心 加载
export function loadAgent(data) {
  return request({
    url: '/gbotApi/client/center/loading',
    method: 'post',
    data
  })
}

// 搜索智能体
export function searchAgent(data) {
  return request({
    url: '/gbotApi/client/center/search',
    method: 'post',
    data
  })
}

// 我的智能体
export function getMyAgent(data) {
  return request({
    url: '/gbotApi/client/center/myCollect',
    method: 'post',
    data
  })
}

//收藏、取消智能体
export function collectControl(data) {
  return request({
    url: '/gbotApi/client/center/collect',
    method: 'post',
    data
  })
}

// 获取文件列表
export function getFileList(data) {
  return request({
    url: '/gbotApi/client/file/list',
    method: 'post',
    data
  })
}

// 用户批量上传文件 （form-data）
export function filesUp(data) {
  return request({
    url: '/gbotApi/client/file/up',
    method: 'post',
    headers: { 
      // 'Content-Type': 'multipart/form-data',
      // 'Content-Type': 'application/json'
    },
    data
  })
}
// 用户删除某个文件
export function deleteFile(data) {
  return request({
    url: '/gbotApi/client/file/delete',
    method: 'post',
    data
  })
}



// 请求文档的某一页
// export function requesetSingleDoc(data) {
//   return request({
//     url: `/gbotApi/page/detail`,
//     // responseType:'blob',
//     method: 'post',
//     data
//   })
// }

// 请求文档的某一页 流格式
export function requesetDocFileBlob(data) {
  return request({
    url: `/gbotApi/client/file/detailFile`,
    responseType:'blob',
    method: 'post',
    data
  })
}

// AI阅读上传文件===需要先调文件列表接口，后端会自动创建会话
export function readFileUp(data) {
  return request({
    url: '/gbotApi/client/read/up',
    method: 'post',
    headers: { 
      // 'Content-Type': 'multipart/form-data',
      // 'Content-Type': 'application/json'
    },
    data
  })
}

// AI阅读 根据机器人ID查询关联的文件列表
export function readFileList(data) {
  return request({
    url: '/gbotApi/client/read/fileList',
    method: 'post',
    data
  })
}

// AI阅读 文件删除接口联调
export function readDeleteFile(data) {
  return request({
    url: '/gbotApi/client/read/deleteFile',
    method: 'post',
    data
  })
}

// AI阅读
export function readFileDetail(data) {
  return request({
    url: '/gbotApi/client/read/fileDetail',
    method: 'post',
    data
  })
}

// AI阅读欢迎页、欢迎列表

export function readHelloLoading(data) {
  return request({
    url: '/gbotApi/client/read/loading',
    method: 'post',
    data
  })
}

// 图片上传
export function imageUp(data) {
  return request({
    url: '/gbotApi/client/image/up',
    method: 'post',
    headers: { 
      // 'Content-Type': 'multipart/form-data',
      // 'Content-Type': 'application/json'
    },
    data
  })
}

// 识别图片  /image/identify
export function imageIdentify(data) {
  return request({
    url: '/gbotApi/client/image/identify',
    method: 'post',
    headers: { 
      // 'Content-Type': 'multipart/form-data',
      // 'Content-Type': 'application/json'
    },
    data
  })
}

// 根据imageId查询图片详情
export function imageDetail(data) {
  return request({
    url: '/gbotApi/client/image/detail',
    method: 'post',
    data
  })
}


// //  文章检索接口
// export function readFileDetail(data) {
//   return request({
//     url: '/gbotApi/client/read/fileDetail',
//     method: 'post',
//     data
//   })
// }




/**
 * 获取用户是否有企标助手权限
 * "qibiaoStatus": 1 //0代表有 1代表没有权限
 */
export function helperLimits(data) {
  return request({
    url: '/gbotApi/home/<USER>',
    method: 'post',
    data
  })
}



/**
 * 普通文件上传助手
 * createFileMsg, fileMsgList, updateFileMessageName, deleteFileMessage, fileDetail , 
 */
// 新建对话
export function createFileMsg(data) {
  return request({
    url: '/gbotApi/create/ordinaryMessage',
    method: 'post',
    data
  })
}

// 用户查看 文档 消息列表
export function fileMsgList(data) {
  return request({
    url: '/gbotApi/robotOrdinary/list',
    method: 'post',
    data
  })
}

// 具体会话上传的文件列表
export function ordinaryMessage(data) {
  return request({
    url: '/gbotApi/list/ordinaryMessage',
    method: 'post',
    data
  })
}

// 用户批量上传文件 （form-data）
export function ordfilesUp(data) {
  return request({
    url: '/gbotApi/ordinary/fileUp',
    method: 'post',
    headers: { 
      // 'Content-Type': 'multipart/form-data',
      // 'Content-Type': 'application/json'
    },
    data
  })
}
// 用户删除某个文件
export function fileDelete(data) {
  return request({
    url: '/gbotApi/ordinary/fileDelete',
    method: 'post',
    data
  })
}

export function loginSSO(data) {
  return request({
    url: '/gbotApi/auth/check',
    method: 'post',
    data
  })
}

// 语言完成后发送 完整音频
export function wavUp(data) {
  return request({
    url: '/gbotApi/wav/up',
    method: 'post',
    headers: {},
    data
  })
}