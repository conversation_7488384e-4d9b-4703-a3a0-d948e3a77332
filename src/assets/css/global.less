@import '~ant-design-vue/dist/antd.less'; // 引入官方提供的 less 样式入口文件
// @import '@/themes/custom-theme.less'; // 用于覆盖上面定义的变量

// 全局变量
body,
html {
  width: 100%;
  height: 100%;
  background-color: #F1F2F5;

  // overflow: hidden;
  #app .main-layout * {
    &::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }
  
    &::-webkit-scrollbar-thumb {
      border-radius: 5px;
      -webkit-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.5);
      background-color: #0000002c;
    }
  
    &::-webkit-scrollbar-track {
      // -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      -webkit-box-shadow: inset 0 0 5px transparent;
      border-radius: 5px;
    }
  }

}

p {
  margin-bottom: 0px;
}

pre {
  // background:#000;
  // overflow-x: auto;
  color: #000;
  background: #FAFAFA;
  overflow-x: auto;
  padding: 20px;
  position: relative;
  z-index: 99;
  line-height: initial;

  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px #fff;
    background-color: #fff;
  }

  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
  }

  code {
    overflow-x: auto;

    // padding:0 20px;
    &::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 5px;
      -webkit-box-shadow: inset 0 0 5px #fff;
      background-color: #fff;
    }

    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 5px;
    }
  }

  ol,
  ul {
    list-style: auto;
    padding-left: 20px;
    ;
  }
}

.ant-popover-placement-bottom {
  .popover-confirm {
    padding: 10px;

    p {
      font-size: 18px;
      ;
      cursor: pointer;

      &.red {
        color: #FF0000;
        margin-bottom: 10px;
        ;
      }
    }
  }


}

.ant-popover-placement-leftBottom {
  .ant-popover-title {
    font-size: 14px;
    font-weight: bold;
    text-align: center;
  }
}

.introduce-com {
  .ant-modal-content {
    border-radius: 15px;
    overflow: hidden;
  }

  .ant-modal-title {
    font-size: 23px;
    font-weight: bold;
    color: #5416ff;
    text-align: center;
  }

  .ant-modal-header {
    padding: 20px 24px;
  }
}
.layoutcss {
  max-width: 832px;
  margin: 0px auto;
  padding: 0px 32px;
}
.goAgentsBtn {
  display: flex;
  padding: 10px 28px 10px 16px;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
  color: #1C2024;
  background: linear-gradient(69deg, rgba(61, 187, 215, 0.10) 2.55%, rgba(54, 74, 253, 0.10) 32.25%, rgba(183, 110, 241, 0.10) 120.42%);
  font-size: 16px;
  position: relative;
  border: 1px solid transparent;
  line-height: 20px;
  &::after {
    content: '';
    width: 6px;
    height: 6px;
    border-top: 1px solid #1C2024;
    border-right: 1px solid #1C2024;
    transform: rotate(45deg) translateY(-50%);
    // transition: all 0.3s linear;
    position: absolute;
    top: 50%;
    right: 20px;
  }
  &:hover{
    &::after {
      animation: move 1s linear infinite;
      border-color: #364afd;
    }
    border: 1px solid rgba(53, 107, 253, 0.15);
    background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.ant-btn {
  border-radius: 5px;
  overflow: hidden;
}
.ant-dropdown {
  border-radius: 5px;
  overflow: hidden;

}
.title-modal {
  .ant-dropdown {
    .ant-dropdown-content {
      border-radius: 8px !important;
      overflow: hidden !important;
    }
  }
  .ant-modal-content {
    border-radius: 15px !important;
    overflow: hidden !important;
  }
  .ant-modal-header {
    border-bottom: none;
  }
  .ant-modal-body {
    padding: 0px 24px 12px;
  }
  .ant-modal-footer {
    border: none;
  }
  .ant-input {
    border: none;
    background-color: #eee;
    border-radius: 5px; padding: 6px 11px;
  }
  .ant-input:hover {
    border-color: #d9d9d9;
    box-shadow: none;
  }
}

.ai-translate-select {
  .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    height: 37px !important;
    line-height: 37px;
    padding: 8px 0px;
  }
  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border: none !important;
    background: none !important;
  }
  .ant-select {
    line-height: 21px;
    outline: none !important;
    width: 96px;
  }
  .ant-select-single:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-search-input {
    height: 21px;
  }
  .ant-select-single .ant-select-selector .ant-select-selection-item, .ant-select-single .ant-select-selector .ant-select-selection-placeholder{
    line-height: 21px;
    text-align: center;
  }
  .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
    // box-shadow: 0 0 0 2px rgb(221 208 255 / 24%)
    box-shadow: none;
  }
  padding: 0px 16px;
  border-radius: 12px;
  background: #F0F0F3;
}


@keyframes move {
  0% {
    right: 20px;
  }
  60% {
    right: 16px;
  }
  100% {
    right: 20px;
  }
}
