<template>
  <div class="filePanel">
    <span class="closeBtn" @click="closefn"></span>
    <div class="historyTitle">历史文件列表</div>
    <div class="historyPanel">
      <div class="historyList"> 
        <div v-if="fileList.length === 0" class="emptyList">--文件列表为空--</div>
        <div class="historyItem" :class="{ cur: item.messageFileId == messageFileId }" @click="fileChecked(item)" v-for="(item, idx) in fileList" :key="idx">
          <span><img src="@/assets/image/pdfTag.svg" class="historyIcon" /></span>
          <span :title="item.fileName">{{ item.fileName }}</span>
          <Popconfirm
            title="确定删除此文件嘛?"
            ok-text="确定"
            cancel-text="取消"
            @confirm="deleteFileFn('1', item)"
            @cancel="deleteFileFn('0', item)"
          >
            <span @click.stop><img src="@/assets/image/delbtn.svg" class="historyDelete" /></span>
          </Popconfirm>
        </div>
      </div>
    </div>
    <Upload
      name="file"
      :show-upload-list="false"
      :customRequest="customRequest"
      ref="uploadRef"
      style="width: 100% !important;"
    >
      <!-- shortcut file 不要修改 -->
      <div class="fileUpBtn">
        <div class="loading-file" v-if="fileupLoading" >
          <LoadingOutlined/>
        </div>
        <span>上传文档</span>
        <span>PDF/Word/TXT</span>
      </div>
    </Upload>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, reactive, watch, onMounted, computed } from 'vue'
import { message, Popconfirm, Upload } from 'ant-design-vue';
import { deleteFile } from '@/api';
import {
  LoadingOutlined
} from '@ant-design/icons-vue';

import Bus from '@/utils/bus.js';

const props = defineProps({
  messageId: Number,
  fileList: Array
})

const emits = defineEmits(['toggleLeftPanel'])

const fileList = ref([])
watch(() => props.fileList, (newVal) => {
  fileList.value = newVal;
  const len = fileList.value.length
  if(len > 0) {
    messageFileId.value ? '' :  messageFileId.value = fileList.value[0].messageFileId
    Bus.$emit('messageFileId',messageFileId.value)
  }
})

const messageFileId = ref('')
// 文件列表点击事件
const fileChecked = (item) => {
  // 实现文件检查逻辑
  messageFileId.value = item.messageFileId;
  Bus.$emit('messageFileId',messageFileId.value)

}

const deleteFileFn = (tag, item) => {
  if(tag === '1') {
    // 调用删除文件的API
    deleteFile({ messageId: item.messageId, fileId: item.messageFileId }).then(res => {
      if(res.code == 200) {
        Bus.$emit('fileListChange', 'id'+item.messageFileId)
        message.success({
          key: 'delete',
          content: '删除成功',
        })
      } else {
        console.log('删除失败', res)
      }
    }).catch(error => {
      
    });
  } else {
    message.warning('取消操作')
  }
}

const customRequest = (data) => {
  Bus.$emit("upfile", data)
}


const closefn = () => {
  // 实现关闭文件面板的逻辑
  emits('toggleLeftPanel')
}

const fileupLoading = ref(false)

onMounted(() => {
  Bus.$on("fileupLoading", (val) => {
    fileupLoading.value = val
  })

})

</script>

<style lang="less" scoped>
.filePanel {
  max-width: 100%;
  border-radius: 12px;
  border: 2px solid #FFF;
  background: rgba(255, 255, 255, 0.60);
  box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
  backdrop-filter: blur(4.300000190734863px);
  display: flex;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  height: 100%;
  overflow: hidden;
  position: relative;
  .closeBtn {
    width: 20px; height: 20px; background-image: url(@/assets/image/left_panel_close.svg);
    position: absolute;
    right: 12px;
    top: 12px;
    &:hover {
      opacity: 0.6;
      cursor: pointer;
    }


  }
}
.fileUpBtn {
  padding: 12px 16px;
  border-radius: 8px;
  background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
  display: flex;
  flex-direction: column;
  // width: 100%;
  width: 192px;
  align-items: center;
  justify-content: space-around;
  cursor: pointer;
  position: relative;
  color: #fff;
  .loading-file {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.5); // 半透明背景
    border-radius: 8px; // 与按钮的圆角保持一致
    z-index: 10; // 确保加载动画在按钮内容之上
  }
  span:first-child {
    color: white;
    font-size: 14px;
    font-weight: bolder;
    line-height: 14px;
    padding-bottom: 3px;
  }
  span:last-child {
    color: white;
    font-size: 12px;
    line-height: 12px;
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: #CDCED6;
}
.historyTitle {
  font-size: 14px;
  color: #1C2024;
  font-weight: bolder;
}
.historyPanel {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1;
  overflow: hidden;
  gap: 12px;
  .emptyList { text-align: center; color: #999; font-size: 14px; padding: 20px 0; }
  .historyList {
    overflow-y: auto;
    .historyItem {
      padding: 8px 5px;
      border-radius: 8px;
      background-color: #fff;
      display: flex;
      margin-bottom: 4px;
      align-items: center;
      cursor: pointer;

      span:first-child {
        padding: 3px 5px;
        margin-right: 8px;
        img {
          display: block;
        }
      }

      span:nth-child(2) {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      
      span:last-child{
        width: 24px;
        height: 24px;
        border-radius: 8px;
        display: block;
        visibility: hidden;
        img { display: block; }
      }
      &:hover{
        background-color: #F0F0F3;
        span:last-child {
          visibility: visible;
        }
        span:last-child :hover {
          background-color: #E0E1E6;
          border-radius: 8px;
        }
      }
      .historyDelete {
        align-self: flex-end;
      }
    }
    .historyItem.cur {
      background: linear-gradient(69deg, rgba(61, 187, 215, 0.20) 2.55%, rgba(54, 74, 253, 0.20) 32.25%, rgba(183, 110, 241, 0.20) 120.42%);
      span:last-child :hover {
        background-color: #fff;
        border-radius: 8px;
      }
      span:nth-child(2) {
        opacity: var(--13, 1);
        background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}
</style>