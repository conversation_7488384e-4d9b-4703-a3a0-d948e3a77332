<!-- 点赞、点踩按钮组件 -->
<template>
  <div class="comment-box">
    <!-- {{ commentObj }} -->
    <div class="comment-box-cover" v-if="(commentObj.commentStatus || props.isGenerating) || props.noContent" />
    <popover trigger="click" :getPopupContainer="trigger => trigger.parentNode.parentNode.parentNode.parentNode || document.body" :forceRender="true" placement="leftBottom" v-model:visible="popVisibleAgree">
      <i class="handle-icon approval" :class="{ 'active': commentObj.commentStatus === 1 }"></i>
      <template #title>
        你的反馈将帮助我们持续进步
      </template>
      <template #content>
        <!-- <Checkbox-group v-model:value="agreeChecked" :options="agreeList" /> -->
        <div v-for="(item, idx) in agreeList" :key="idx">
          <Checkbox v-model:checked="item.checked">{{ item.text }}</Checkbox>
        </div>
        <br />
        <a-textarea v-model:value="agreeInp" :maxlength="2000" show-count :autoSize="{ minRows: 2, maxRows: 6 }" />
        <div style="text-align: center; margin-top:10px;">
          <a-button size="small" @click="handleApproval('agree')" :loading="btnLoading">提交</a-button>
        </div>
      </template>
    </popover>
    <!-- <span class="devide" /> -->
    <popover trigger="click" :getPopupContainer="trigger => trigger.parentNode.parentNode.parentNode.parentNode || document.body" placement="leftBottom" v-model:visible="popVisibledisAgree">
      <i class="handle-icon deny" :class="{ 'active': commentObj.commentStatus === 2 }"></i>
      <template #title>
        你的反馈将帮助我们持续进步
      </template>
      <template #content>
        <!-- <Checkbox-group v-model:value="agreeChecked" :options="agreeList" /> -->
        <div v-for="(item, idx) in disagreeList" :key="idx">
          <Checkbox v-model:checked="item.checked">{{ item.text }}</Checkbox>
        </div>
        <br />
        <a-textarea v-model:value="disagreeInp" :maxlength="2000" show-count :autoSize="{ minRows: 2, maxRows: 6 }" />
        <div style="text-align: center;margin-top:10px;">
          <a-button size="small" @click="handleApproval('disagree')" :loading="btnLoading">提交</a-button>
        </div>
      </template>
    </popover>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, reactive, watch, onMounted, computed, nextTick, onUnmounted } from 'vue'
import { feedbackMessage } from '@/api/index.js'
import { popover, Checkbox, message } from 'ant-design-vue';

import Bus from '@/utils/bus';

const props = defineProps({
  commentObj: Object,
  messageId: Number,
  isGenerating: Boolean,
  noContent: Boolean,
  robotobj: Object
})

let popVisibleAgree = ref(false)
let popVisibledisAgree = ref(false)
let btnLoading = ref(false)
let agreeInp = ref('')
let disagreeInp = ref('')

let agreeList = reactive([{
  checked: false,
  text: '回答准确且专业'
}, {
  checked: false,
  text: '回答清晰易于理解'
}, {
  checked: false,
  text: '回答响应速度快准确且专业'
}
])
let disagreeList = reactive([{
  checked: false,
  text: '内容回复不全'
}, {
  checked: false,
  text: '内容存在错误'
}, {
  checked: false,
  text: '内容定位不准'
}
])

// let status = ref(0)
const handleApproval = (type) => {
  btnLoading.value = true
  let commentContent = []
  if (type === 'agree') {
    commentContent = agreeList.map(item => {
      if (item.checked) {
        return item.text
      }
    }).filter(item => item).concat([agreeInp.value]).toString()
    console.log(commentContent)
    // status.value = 1 // 正赞
  } else {
    commentContent = disagreeList.map(item => {
      if (item.checked) {
        return item.text
      }
    }).filter(item => item).concat([disagreeInp.value]).toString()
    console.log(commentContent)
    // status.value = 2 // 倒赞
  }
  if (!commentContent.length) {
    message.warn('请添加反馈内容')
    btnLoading.value = false
    return
  }
  console.log(props.robotobj,props.commentObj.msgDetailId)
  feedbackMessage({
    "messageId": props.messageId, // 消息编号
    "messageDetailId": props.commentObj.msgDetailId,// 消息详情编号
    commentContent,// 用户评论内容
    status: type === 'agree' ? 1 : 2 // status.value // 0代表取消点赞或倒赞 1代表点赞 2 代表倒赞
  }).then(res => {
    console.log(res)
    btnLoading.value = false
    if (res.code === 200) {
      popVisibleAgree.value = false
      popVisibledisAgree.value = false
      // status.value = type === 'agree' ? 1 : 2
      Bus.$emit('comment', {
        commentStatus: type === 'agree' ? 1 : 2,
        id: props.commentObj.msgDetailId
      })
      message.success('反馈提交成功！')
    } else {
      message.error('反馈提交失败...')
    }
  })
}

// const commentSubmit = (type) => {
//   if (type === 'agree') {
//     console.log(agreeList)
//   } else {
//     console.log(disagreeList)
//   }
// }
// watch(
// () => props.commentObj.commentStatus,
// (val) => {
//   console.log(123, val)
//   status.value = val
// },
// { immediate: true }
// )
onMounted(() => {
  Bus.$on('resetComment', (id) => {
    console.log(id)
    if (id === props.commentObj.msgDetailId) {
      // status.value = 0
      agreeList.forEach(item => item.checked = false)
      disagreeList.forEach(item => item.checked = false)
      agreeInp.value = ""
      disagreeInp.value = ""
    }
  })

})
onUnmounted(() => {
  Bus.$off('resetComment')
})
</script>

<style lang="less" scoped>
.comment-box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 52px;

  .comment-box-cover {
    position: absolute;
    inset: 0 0 0 0;
    z-index: 9;
  }
}

.devide {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1px;
  height: 16px;
  background-color: #86909C;
  // transform: translate(-50%, -50%);
  margin-top: -8px;
  opacity: .6;
}

.handle-icon {
  cursor: pointer;
  display: inline-block;
  width: 18px;
  height: 18px;
  background: url('@/assets/image/icon_approval.svg') center no-repeat;
  background-size: auto;
  // opacity: 0.6;

  &:hover {
    opacity: .8;
  }
}

.deny {
  background-image: url('@/assets/image/icon_deny.svg');
}

.approval.active {
  background-image: url('@/assets/image/icon_approval_active.svg');
}

.deny.active {
  background-image: url('@/assets/image/icon_deny_active.svg');
}
</style>