<template>
 <!-- -->
  <div class="mask" v-if="tipShowTag">
    <div class="bgmask" @click="$emit('closeTips')"></div>
    <div class="zbox">
      <div class="textbox" v-html="props.tipMsg">
      </div>
    </div>
  </div>
</template>

<script setup>
import { watch } from "vue";
const props = defineProps({
  tipMsg: String,
  tipShowTag: Boolean,
});

</script>

<style lang="less" scoped>
.mask {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 1001;
  .bgmask {
    background-color: #00000085;
    width: 100%;
    height: 100%;
  }
  .zbox {
    // width: 480px;
    // height: 230px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(to bottom, #fbfbfbeb, #ddd1ffeb);
    border-radius: 5px;
    display: flex;
    justify-content: center;
    padding: 24px;
    .textbox {
      display: inline-block;
      text-align: left;
      font-size: 16px;
      margin: 8px 16px;
      color: #2a2929; 
      // #5416ff
    }

  }
}
</style>
