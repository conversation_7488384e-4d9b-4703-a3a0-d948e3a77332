<template>
  <div class="theme-switch-box" style="display: none;">
    沉浸模式：<Switch size="small" v-model:checked="isDark" @change="switchTheme"/>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, reactive, onBeforeUnmount, onUnmounted, nextTick, computed, watch } from 'vue'
import { Switch } from 'ant-design-vue';
let isDark = ref(false)
const switchTheme = (val) => {
  document.getElementById('app').setAttribute('class', isDark.value ? 'dark-theme' : '');  
  localStorage.setItem('darkTheme', isDark.value ? 'dark' : '');
}
onMounted(() => {
  const storageDark = localStorage.getItem('darkTheme');
  isDark.value = storageDark === 'dark' || false
  // if(storageDark === 'dark') {
  //   document.getElementById('app').setAttribute('class', 'dark-theme');  
  // } else {
  //   document.getElementById('app').setAttribute('class', '');  
  // }
})
</script>

<style lang="less" scoped>
.theme-switch-box {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  justify-content: center;
  align-items: center;
  padding:0 20px;
  color:#00000052;
  position:absolute;
  bottom:20px;
  left:0;
}
</style>