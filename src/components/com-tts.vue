<template>
  <div class="play-siri">
    <div class="siri-gif-box" v-if="isPlaying && !ttsLoading" />
  </div>
  <div :class="{'play-btn': true}" v-if="ttsLoading">
    <loading-outlined style="margin-right: 20px" />音频生成中...
  </div>
  <template v-else>
    <div :class="{'play-btn': true}" @click="handlePlay()" v-if="!isPlaying">
      <i class="handle-icon start" />开始播放
    </div>
    <div :class="{'play-btn': true, 'playing': isPlaying}" @click="handlePause()" v-else>
      <i class="handle-icon start pause" />
      停止播放
    </div>
  </template>
</template>

<script setup>
import {
  ref,
  defineProps,
  defineEmits,
  reactive,
  watch,
  onMounted,
  computed,
  nextTick,
  onBeforeUnmount,
  onUnmounted,
} from "vue";
import { LoadingOutlined } from "@ant-design/icons-vue";

import Bus from "@/utils/bus";
import { aloudText, commonAloud } from "@/api";
import { message } from "ant-design-vue";

const props = defineProps({
  ttsObj: Object,
  messageId: Number,
  ttsList: Array,
  idx: Number
});

const emits = defineEmits(["ttsList"]);

// 播放内容语音
const isPlaying = ref(false);
const ttsLoading = ref(false);
let audio = null;
const handlePlay = () => {
  ttsLoading.value = true;
  isPlaying.value = true;
  console.log("ttslist", props.ttsList);
  // 如果已经生成过
  let ttsObj = props.ttsList.find(
    (item, idx) => item.msgDetailId === props.ttsObj.msgDetailId
  );
  console.log(ttsObj);
  Bus.$emit("currentPlayingIdx", props.idx);
  if (ttsObj) {
    playBlob(ttsObj.blob);
  } else {
    // 判断是通用问答 还是企标问答
    let loudObj = {
      argument: {},
      url: aloudText,
    };
    //if (props.ttsObj.converseType === 4 || props.ttsObj.converseType === 3 ) { // 企标、文档助手
    loudObj = {
      argument: {
        text: props.ttsObj.content, // 消息具体内容
      },
      url: commonAloud,
    };
    // } else {
    //   loudObj = {
    //     argument: {
    //       "messageId": props.messageId, // 消息编号
    //       recordDetailId: props.ttsObj.msgDetailId // 消息详情编号
    //     },
    //     url: aloudText
    //   }
    // }
    loudObj["url"](loudObj.argument)
      .then((res) => {
        let blob = window.URL.createObjectURL(res);
        playBlob(blob);
        // 存入tts 列表
        emits("ttsList", {
          msgDetailId: props.ttsObj.msgDetailId,
          blob,
        });
      })
      .catch((err) => {
        ttsLoading.value = false;
        isPlaying.value = false;
        Bus.$emit("currentPlayingIdx", "");
      });
  }
};

const playBlob = (blob) => {
  console.log(blob);
  ttsLoading.value = false;
  audio = new Audio(blob);
  audio.play();
  audio.onended = () => {
    console.log("播放结束事件");
    isPlaying.value = false;
    Bus.$emit("currentPlayingIdx", "");
  };
  audio.onerror = (err) => {
    message.error("播放出错");
    isPlaying.value = false;
    Bus.$emit("currentPlayingIdx", "");
  };
};

const handlePause = () => {
  isPlaying.value = false;
  Bus.$emit("currentPlayingIdx", "");
  audio.pause();
};

onUnmounted(() => {
  if (audio) {
    handlePause();
  }
  audio = null;
});
</script>

<style lang="less" scoped>
.play-siri {
  height: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  .siri-gif-box {
    width: 100%;
    height: 100%;
    background: url("@/assets/image/siri_play.gif") center no-repeat;
    background-position: center;
    mix-blend-mode: screen;
  }
}

.play-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
  opacity: 1;
  margin-right: 16px;
  user-select: none;
  white-space: nowrap;

  &:hover,
  &:hover i {
    opacity: 0.8;
  }

  i {
    background-image: url("@/assets/image/icon_play.svg");
    margin-right: 4px;
    background-position-y: 1px;
  }

  i.pause {
    background-image: url("@/assets/image/icon_pause.svg");
  }

  &.playing {
    animation: neon-pulse 0.5s ease-in-out infinite alternate;
    color: #1900ff90;
    text-shadow: 
      0 0 5px #6f7ffc,
      0 0 10px #6f7ffc,
      0 0 20px #6f7ffc;
  }
}
@keyframes neon-pulse {
  from {
    opacity: 0.9;
    text-shadow: 
      0 0 2px #6f7ffc,
      0 0 5px #6f7ffc;
  }
  to {
    opacity: 0.7;
    text-shadow: 
      0 0 15px #6f7ffc,
      0 0 15px #6f7ffc;
  }
}

.handle-icon {
  cursor: pointer;
  display: inline-block;
  width: 18px;
  height: 18px;
  background: url("@/assets/image/icon_approval.svg") center no-repeat;
  background-size: auto;
  opacity: 1;

  &:hover {
    opacity: 0.8;
  }
}
</style>
