<template>
  <div
    :class="{
      'converse-wrap': true,
    }"
    id="converse-box"
  >
    <div class="converse-box" ref="converseDom">
      <!-- <div class="loadingH" v-if="hisMesLoading">
        <spin tip="Loading"></spin>
      </div> -->

      <a
        href="javascript:;"
        class="go-down-btn"
        @click="goDown()"
        v-if="props.goDownSeeTag"
      >
        <!-- 回到底部 -->
      </a>
      <div class="scroll-box" id="scroll-box" style="position: relative">
        <!-- <div class="filetest" style="width: 260px; height: 100%; background-color: red; position: absolute; left: -200px; top: 0px;"></div> -->
        <div v-if="msgList.length == 0">
          <div
            class="converse-item answer-box nolist-style"
          >
          暂无问答记录，请问个问题吧～
          </div>
        </div>
        <div v-for="(item, idx) in msgList" :key="idx">
          <div
            class="converse-item"
            :class="{
              'answer-box': item.type === 'gpt',
              'ques-box': item.type === 'ask',
            }"
          >
            <template v-if="item.type === 'ask'">
              <!-- 问题内容 -->

              <!-- <Avatar shape="square" size="large" class="avatar">{{
              userName
            }}</Avatar> -->
              <div class="content">
                
                  {{ item.content }}
              </div>
              <!-- <div class="avatar" /> -->
            </template>
            <template v-else>
              <!-- gpt返回答案 -->
              <div class="avatar" />
              <div class="content">
                <div
                  class="loading"
                  v-if="
                    (!item.content && !item.reasoningText && msgLoading) &&
                    idx === msgList.length - 1
                  "
                >
                  <loading-outlined
                    style="margin-right: 10px;"
                  />正在生成内容...
                </div>
                <template v-else>
                  <!-- <template v-if="!item.isSmartSearch"> -- 去掉慧搜功能-->
                  <net-result :item="item" :idx="idx" />
                  <think-com :item="item" :idx="idx" />
                  <!-- 流程助手 - 展示流程助手组件 -->
                  <process-list :processlist="item.content" v-if="props.robotobj.askType == 6" />
                  <!-- 如果是术语助手 - 展示术语组件，否则显示 -->
                  <term-list :termlist="item.content" v-else-if="props.robotobj.robotId == 9" />
                  <!-- 制造云设备故障问答 -->
                  <!-- 点击问答列表返回的txt 和 列表返回数据 都为props.robotobj.askType=7；展示格式做区分； -->
                  <answer-list :answerlist="item" @answerClick="answerClick" v-else-if="props.robotobj.askType == 7 && (item.content !== null && typeof item.content === 'object')" />
                  <vue-markdown
                    v-else
                    :postrender="postrenderHandler"
                    :source="
                      item.content
                        ? item.content.replace(
                            /http:\/\/oss-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn\/gbot\/images/gi,
                            '/imgGbot'
                          )
                          .replace(/https:\/\/gwkb.gwm.cn\/oss/gi, '/videoIt')
                        : '很抱歉，我无法帮到您 ~~'
                    "
                    :breaks="true"
                    :html="true"
                    class="md-wrap"
                    @click="handleMd(item)"
                    :class="`md-${idx}`"
                  />
                  <!-- {{ props.robotobj.askType == 7 && (item.content !== null && typeof item.content === 'object') }} -->
                  <div
                    class="source-data-box"
                    v-if="item.sourceDataText && item.sourceDataText.content"
                  > 
                    <template v-if="props.robotobj.askType == 5">
                      <page-source-data :item="item || ''" />
                    </template>
                    <template v-else>
                      <source-data :item="item || ''" :idx="idx" />
                    </template>
                  </div>

                  <div class="annotation-box">
                    <span
                      class="annotation-item"
                      v-for="(ele, idx) in item.annotationList"
                      :key="idx"
                    >
                      <Tooltip placement="bottom">
                        <template #title>
                          <span>{{ ele.fileName }}</span>
                        </template>
                        <file-pdf-outlined class="doc-icon" />
                      </Tooltip>
                      <template v-for="(chunk, chunkIdx) in ele.chunks"
                        ><span
                          class="doc-link"
                          @click="handleAnnotation(ele, area, i, chunkIdx)"
                          v-for="(area, i) in chunk['area']"
                          :key="i"
                          >{{ i + chunkIdx + 1 }}</span
                        >
                      </template>
                    </span>
                  </div>
                  <!-- </template> -->
                  <!-- <com-smart-search v-else :smartData="item.content" /> 慧搜功能 -->
                  <div class="model-tag" v-if="item.content && props.robotobj.askType == 7">由agent平台生成</div>
                  <div class="model-tag" v-else-if="item.content && props.robotobj.robotId === 1">
                    由 {{ item.modelName==true ? 'deepseek-r1': (item.modelName==false ? 'qwen2-5-72b': item.modelName) }} 生成
                  </div>
                  <div class="model-tag" v-else-if="item.content && props.robotobj.robotId !== 1">
                    由 {{ item.modelName==true ? 'agent平台': (item.modelName==false ? 'agent平台': item.modelName) }} 生成
                  </div>
                  <!-- 3长城术语搜索、6流程查询助手 -->
                  <div class="model-tag" v-if="props.robotobj.askType == 3 || props.robotobj.askType == 6">
                    由 ai 查询
                  </div>
                </template>
                <!-- && ((idx !== msgList.length - 1 && item.content.length>0) || idx === msgList.length - 1) 
              用于判断，返回内容为空 并且 不是最后一条回答时，handle-box不显示。
              是最后一条时，显示，handle-box内部去控制 （复制、点赞等）不显示
              原因：点赞，倒赞 需要返回的数据ID，因为返回内容为空，所以无法进行操作  ->隐藏掉
              -->
                <div
                  class="handle-box"
                  :class="{'right-align': (props.robotobj.askType === 3 || props.robotobj.askType === 6)}"
                  v-if="
                   
                    ((idx !== msgList.length - 1 && item.content.length > 0) ||
                      idx === msgList.length - 1) && !isGenerating

                  "
                >
                  <!-- 术语助手、流程查询助手 中不显示重新生成/复制 -->
                  <div
                    class="stop-generate"
                    v-if="props.robotobj.askType !== 3 && props.robotobj.askType !== 6"
                  >
                    <!-- 最后一个问答才显示 重新生成btn -->
                    <!-- 播放中不显示重新生成按钮 -->
                    <template
                      v-if="
                        idx === msgList.length - 1 && playingIdx * 1 !== idx && !isGenerating && props.robotobj.askType != 5 && props.robotobj.askType != 7
                      "
                    >
                      <div @click="reGenerate(item, idx)">
                        <i
                          class="handle-icon generate-icon regenerate"
                        />重新生成
                      </div>
                    </template>
                    <div
                      @click="handleCopy(item.content, item.converseType, idx)"
                      v-if="
                        item.content &&
                        item.content.length > 0 &&
                        !item.isSmartSearch
                      "
                      class="copy-btn"
                      :class="`copy-${idx}`"
                    >
                      <i class="handle-icon generate-icon copy" />复制
                    </div>
                  </div>
                  <!-- 除了 术语助手、流程查询助手 都显示播报功能 -->
                  <!-- {{ item.converseType !== 1 }} {{ playingIdx * 1 === idx }} {{ playingEnd }}{{ !isGenerating }} -->
                  <keep-alive :key="props.messageId" v-if="props.robotobj.askType !== 3 && props.robotobj.askType !== 6 && props.robotobj.askType != 5">
                    <com-tts
                      :ttsObj="item"
                      :idx="idx"
                      v-if="
                        (playingIdx * 1 === idx || playingEnd) &&
                        !isGenerating &&
                        item.content.length &&
                        item.content.indexOf('![图片描述]') < 0
                      "
                      :messageId="props.messageId"
                      @ttsList="ttsListfn"
                      :ttsList="ttsList"
                    />
                  </keep-alive>
                  <com-comment
                    v-if="
                      item.content &&
                      item.content.length > 0 &&
                      props.robotobj.askType != 5
                    "
                    :commentObj="item"
                    :messageId="props.messageId"
                    :isGenerating="isGenerating"
                    :noContent="
                      !item.content.length ||
                      item.content === '很抱歉，我无法帮到您 ~~'
                    "
                    :robotobj="props.robotobj"
                  />
                </div>
              </div>
            </template>
          </div>
          <!-- 术语搜索没有停止生成 -->
          <div
            @click="stopGenerate(item.converseType === 1 ? 'img' : 'text')"
            v-if="
              idx === msgList.length - 1 &&
              playingIdx * 1 !== idx &&
              isGenerating &&
              (props.robotobj.askType !== 3 && props.robotobj.askType !== 6)
            "
            class="stop-generate-btn"
          >
          </div>
        </div>

        <!-- <div v-else style="color:#fff;text-align:center;" v-show="">暂无内容。。。</div> -->
        <!-- <div class="pending-box" v-if="showPending">
          当前问答人数较多,请耐心等待...
        </div> -->
        <!-- 猜你想问模块 -->
        <guess-que
          :recommendList="props.recommendList"
          :recommendListLoading="recommendListLoading"
          @sendMsg="sendMsg"
        ></guess-que>
      </div>
      <Image :src="previewImgSrc" style="width: 0px; height: 0px" zoom="2" />
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  defineProps,
  defineEmits,
  reactive,
  watch,
  onMounted,
  computed,
  nextTick,
} from "vue";
import { LoadingOutlined, FilePdfOutlined } from "@ant-design/icons-vue";
import VueMarkdown from "@/components/markdown/VueMarkdown.js";
import katex from "katex";
import "katex/dist/katex.min.css";
import "@/assets/css/dark.css";
import { message, Avatar, Image, Spin, Tooltip } from "ant-design-vue";
import { feedbackMessage, aloudText } from "@/api";

// import comFile from "@/components/com-file";
import comTts from "@/components/com-tts";
import comComment from "@/components/com-comment";
// import comFileSelf from "@/components/com-file-self";
// import comSmartSearch from "@/components/com-smart-search";

import guessQue from "@/components/msgcontent/guess-que"; // 添加这一行以引入 guess-que 组件
import thinkCom from "@/components/msgcontent/think-com.vue"; // 思考线组件
import netResult from "@/components/msgcontent/net-result.vue"; // 网络搜索结果组件
import sourceData from "@/components/msgcontent/source-data.vue"; // 来源数据组件
import pageSourceData from "@/components/msgcontent/page-source-data.vue"; // 来源数据组件

import termList from "@/components/msgcontent/term-list.vue";
import processList from "@/components/msgcontent/process-list.vue";
import answerList from "@/components/msgcontent/answer-list.vue";

// import peaAsk from "@/components/helper_diff/peaAsk.vue";

import ClipboardJS from "clipboard";
import Bus from "@/utils/bus";
// console.log(ClipboardJS)
const nameArr = JSON.parse(localStorage.getItem("userName")).split("");
const name = nameArr.slice(1, nameArr.length).join("");
const userName = ref(name);
const props = defineProps({
  msgList: Array,
  msgLoading: Boolean, /// send发送消息后，消息是否加载中
  messageId: Number,
  // showPending: Boolean,
  // showPullMore: Boolean,
  robotobj: Object,
  GLOBAL_DRAWTAG: String,
  // GLOBAL_SEARCHTAG: String,
  recommendList: Array,
  recommendListLoading: Boolean,
  canAsk: Boolean,
  goDownSeeTag: Boolean,
});
const emits = defineEmits([
  "reGenerate",
  "stopGenerate",
  "sendMsg",
  "scrollBtmInterval",
  "answerClick"
]);
watch(
  () => props.msgLoading,
  (val) => {
    isGenerating.value = val;
  }
);

// const goDownSeeTag = ref(false); // 是否需要滚动到底部查看标签
// watch(
//   () => props.goDownSeeTag,
//   (val) => {
//     debugger
//     goDownSeeTag.value = val;
//   }
// )

// const hisMesLoading = ref(false);
// Bus.$on("hisMesLoading", (val) => {
//   hisMesLoading.value = val;
// });

const isGenerating = ref(false);
const stopGenerate = (type) => {
  isGenerating.value = true;
  emits("stopGenerate", type);
};

const reGenerate = (item, idx) => {
  isGenerating.value = true;
  // 重置点赞状态
  item.commentStatus = 0;
  console.log(item);
  Bus.$emit("resetComment", item.msgDetailId);
  emits("reGenerate", {
    item,
    idx,
    msgDetailId: item.msgDetailId * 1,
    // status=0 代表第一次生成  1代表重新生成
  });
};

const handleCopy2 = (content, type, idx) => {
  let clipboard = new ClipboardJS(`.copy-${idx}`, {
    target: function (trigger) {
      const dom =
        type === 1
          ? document.querySelector(`.md-${idx}`).children[0]
          : document.querySelector(`.md-${idx}`);
      console.log(dom);
      // console.log(document.querySelector(`.md-${idx}`).children[0])
      return dom;
    },
  })
    .on("success", () => {
      message.success("复制成功！");
      clipboard.destroy();
    })
    .on("error", () => {
      message.error("复制失败。");
      clipboard.destroy();
    });
};
// img 标签提取src属性正则
// let reg = /(\/|http)[^>]+\.(jpg|jpeg|png|gif)/g;
const handleCopy = (text, type) => {
  // if (type === 1) {
  //   // 如果复制的是图片
  //   console.log(text.match(reg));
  //   text = text.match(reg)[0];
  // }
  text = text.replace(/^\n/, "");
  const save = function (e) {
    e.clipboardData.setData("text/plain", text);
    e.preventDefault(); // 阻止默认行为
  };
  document.addEventListener("copy", save, { once: true }); // 添加一个copy事件
  document.execCommand("copy"); // 执行copy方法
  message.success("复制成功");
};

let ttsList = []; //已经生成过的 语音list
const playingIdx = ref(""); // 正在播放的id
const playingEnd = ref(true); // 是否播放完毕
const ttsListfn = (item) => {
  ttsList.push(item);
};

let preViewDom = null;
const previewImgSrc = ref("");
let reg = /(\/|http)[^>]+\.(jpg|jpeg|png|gif)/g;
const handleMd = (item) => {
  if (item.content.startsWith("<img")) {
    console.log("打开 图片");
    previewImgSrc.value = item.content.match(reg)[0];
    setTimeout(() => {
      preViewDom.click();
    }, 0);
  }
};

// 点击 标注
const handleAnnotation = (ele, area, i, chunkIdx) => {
  // expandShow.value = true
  Bus.$emit("annotationList", {
    ele,
    area,
    // i,
    // chunkIdx,
  });
};

// 已由接口返回，此处注释
// const modelMap = {
//   1: 'Stable-Diffusion-XL-1.0', // 文生图
//   // 0: 'ChatGPT3.5', // 普通问答qwen2-72b
//   0: 'Qwen2-72B', // 普通问答
//   4: 'StableBeluga2', // 旗标问答
//   3: 'StableBeluga2' // 文档问答
// }
// const getModelName = (item) => {
//   if (item.isSmartSearch) return 'AI自研算法'
//   return modelMap[item.converseType]
// }

// const thinkShow = (item) => {
//   item.thinkShow = !item.thinkShow
// }
const sendMsg = (item) => {
  emits("sendMsg", item);
};

const answerClick = (item) => {
  emits("answerClick", item);
};


const fixLeftRightPairs = (formula) => {
  const leftCount = (formula.match(/\\left/g) || []).length;
  const rightCount = (formula.match(/\\right/g) || []).length;

  if (leftCount > rightCount) {
    formula += Array(leftCount - rightCount + 1).join("\\right.");
  } else if (rightCount > leftCount) {
    formula = Array(rightCount - leftCount + 1).join("\\left.") + formula;
  }

  return formula;
};

const postrenderHandler = (html) => {
  const preprocessFormula = (formula) => {
    // 移除多余的换行符和空格
    formula = formula.replace(/<br\s*\/?>/gi, "");
    return formula.trim();
  };

  html = html.replace(
    /<table class="table">/g,
    '<table border="1" cellpadding="5" cellspacing="0">'
  );

  // 匹配 LaTeX 公式（支持多行公式）
  return html.replace(/\[([\s\S]*?)\]/g, (match, p1) => {
    const processedFormula = preprocessFormula(p1);

    // 检查是否是有效的 LaTeX 公式
    const isLatexFormula = /\\[a-zA-Z]+|\^|\_|{}/.test(processedFormula);

    if (isLatexFormula) {
      const fixedFormula = fixLeftRightPairs(processedFormula);
      // 使用 KaTeX 渲染公式
      return katex.renderToString(fixedFormula, {
        throwOnError: false,
        displayMode: true,
        output: "html",
      });
    } else {
      // 如果不是公式，直接返回原始内容
      return match;
    }
  });
};

const goDown = () => {
  // console.log('down...')
  let chatWrap = document.getElementById("converse-box");
  chatWrap.scrollTop = 999999999;
  emits("scrollBtmInterval");
};

onMounted(() => {
  // document.getElementById('#mdWrap')
  preViewDom = document.getElementsByClassName("ant-image")[0];
  Bus.$on("currentPlayingIdx", (idx) => {
    playingIdx.value = idx;
    playingEnd.value = idx.length === 0;
  });
});
</script>

<style lang="less" scoped>
.pLine {
  display: flex;
  justify-content: space-between;
  span:last-child {
    flex: 1;
  }
}
.converse-wrap {
  // max-width: 1300px;
  // height: calc(100% - 110px);
  height: 100%;
  width: 100%;
  overflow-y: auto;
  // position: absolute;
  // left: 50%;
  // top: 100%;
  // width: 95%;
  // transform: translateX(-50%);
  transition: all 0.5s ease-in-out;
  display: flex;
  flex-direction: row;


  .loadingH {
    // padding-top: 10px;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9;

    .ant-spin-spinning {
      background: rgba(255, 255, 255, 0.8);
      padding: 10px;
      border-radius: 10px;
    }
  }

  .converse-box {
    // margin-bottom: 29px;
    max-width: 832px;
    margin: 0px auto;
    padding: 10px 32px;
    width: 100%;
    // height: 100%; // todo calc(100% - 200px);
    // overflow: hidden;
    // overflow-y: scroll;
    // padding-right: 2%;
    .go-down-btn {
      position: absolute;
      bottom: 0px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #ffffffaf;
      padding: 15px 0px 10px 0px;
      border-radius: 50%;
      z-index: 999;
      background-image: url("@/assets/image/go_down.svg");
      background-repeat: no-repeat;
      background-position: 50% 50%;
      background-size: 25px 25px;
      width: 35px;
      height: 35px;
      animation: heart 0.8s ease-in-out 0.8s infinite alternate;
      border: 1px solid #eee;
    }
    
  }
  @keyframes heart {
    from {
      background-position: 50% 45%;
    }
    to {
      background-position: 50% 65%;
    }
  }

  .pull-more-box {
    position: absolute;
    top: 0px;
    left: 50%;
    text-align: center;
    color: #ccc;
    text-align: center;
    transform: translate(-50%, 0px);
  }

  .converse-item {
    display: flex;
    align-items: flex-start;
    padding-bottom: 48px;

    &.ques-box {
      justify-content: end;

      .content {
        border-radius: 16px;
        background: #3133ff;
        backdrop-filter: blur(4.300000190734863px);
        color: #fff;
        &.is-smart {
          padding-left: 40px;

          .icon-magicward {
            width: 20px;
            height: 20px;
            background: url("@/assets/image/icon_magic_ward.svg") center
              no-repeat;
            background-size: contain;
            position: absolute;
            top: 13px;
            left: 13px;
          }
        }
      }
    }

    &.answer-box {
      justify-content: start;

      .content {
        border-radius: 16px;
        border: 1px solid #fff;
        background: rgba(255, 255, 255, 0.5);
        box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
        backdrop-filter: blur(4.300000190734863px);
        table {
          max-width: 100%;
        }
        .md-wrap {
          max-width: 100%;
          overflow-x: auto;
        }
      }
    }
    // .avatar {
    //   display: none;
    // }

    .model-tag {
      padding: 0px 6px;
      // height: 14px;
      background: #d9d9e0;
      color: white;
      font-size: 12px;
      position: absolute;
      right: 32px;
      top: 1px;
      line-height: normal;
      transform: scale(0.9);
      transform-origin: right top;
    }
    
  }
  .stop-generate-btn {
    position: absolute;
    bottom: 0px;
    left: 52px;
    background: url("@/assets/image/stopbtn.svg") center no-repeat;
    width: 88px;
    height: 34px;
    z-index: 9;
    cursor: pointer;
  }

  .avatar {
    margin-top: 4px;
    background: url("@/assets/image/askicon.svg") center no-repeat;
    background-size: contain;
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    margin: 0px 0px 0px 12px;
    flex-shrink: 0;
  }

  .avatar img {
  }

  .content {
    // flex: 1;
    font-size: 14px;
    color: var(--text-5, #1d2129);
    padding: 16px;
    text-align: left;
    border-radius: 16px;
    line-height: 20px;
    position: relative;
    // max-width: 911px;
    overflow-x: hidden;
    background: rgba(54, 86, 255, 0.05);
    // border: 1px solid rgba(0, 0, 0, 0.05);
    // box-shadow: 0 2px 10px #6a8a8a4a;

    input {
      font-size: 20px;
      padding: 0px 10px;
    }

    .edit {
      font-size: 14px;
      color: #b5bfd6;
      height: 18px;
      position: absolute;
      top: 50%;
      margin-top: -9px;
      right: -60px;
      background: url("@/assets/image/icon_edit.svg") center no-repeat;
      display: flex;
      align-items: center;
      padding-left: 54px;
      cursor: pointer;
      user-select: none;

      &:hover {
        opacity: 0.8;
      }

      &.cancel {
        background: none;
        padding-left: 0px;
        right: -40px;
      }
    }

    /deep/.md-wrap {
      // margin-bottom: 20px;

      img {
        display: block;
        // max-height: 300px;
        max-height: 512px;
        cursor: pointer;
        margin: 0 auto;
        transition: all 0.3s ease-in-out;
        margin-bottom: 20px;
        margin-top: 5px;

        &:hover {
          box-shadow: 0 0 15px 0 #00000045;
        }
      }

      p {
        margin-bottom: 15px;
        word-wrap: break-word;
        &:last-child {
          margin-bottom: 0px;
        }
      }
    }
  }

  .annotation-box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;

    .annotation-item {
      margin-right: 15px;
      margin-bottom: 10px;

      .doc-link {
        text-decoration: underline;
        margin-left: 10px;
        color: #5416ff;
        cursor: pointer;

        &:hover {
          transform: scale(1.5);
          opacity: 0.8;
        }
      }
    }

    .anticon {
      color: #f16464;
      // margin-right: 5px;
    }
  }

  .answer-box {
    .content {
      border: none;
      padding-top: 19px;
      min-width: 440px;
      // flex: 1;
    }
  }

  .handle-icon {
    cursor: pointer;
    display: inline-block;
    width: 18px;
    height: 18px;
    background: url("@/assets/image/icon_approval.svg") center no-repeat;
    background-size: auto;
    opacity: 1;

    &:hover {
      opacity: 0.8;
    }
  }

  .pending-box {
    // width: 90%;
    height: 57px;
    line-height: 57px;
    background: #171d2d;
    border: 1px solid #28284a;
    border-radius: 10px;
    color: #fe7b71;
    font-size: 18px;
    text-align: left;
    padding: 0 20px;
    position: absolute;
    bottom: -20px;
    left: 0;
  }

  .loading {
    // position: absolute;
    // top: 15px;
  }

  .answer-box {
    position: relative;
    padding-bottom: 48px;

    .avatar {
      background-image: url("@/assets/image/chaticon.svg");
      background-size: contain;
      margin: 0px 12px 0px 0px;
    }

    .time {
      color: #929cb9;
      font-size: 14px;
      position: absolute;
      bottom: 13px;
      right: 32px;
    }

    .handle-box {
      //height: 56px;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      color: #8b8d98;
      padding: 10px 0px 0px 0px;
      margin-top: 10px;
      &.right-align {
        justify-content: end;
      }
    }

    .play-siri {
      height: 100%;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      .siri-gif-box {
        width: 100%;
        height: 100%;
        background: url("@/assets/image/siri_play.gif") center no-repeat;
        background-position: center;
        mix-blend-mode: screen;
      }
    }

    .play-btn {
      display: flex;
      align-items: center;
      cursor: pointer;
      opacity: 1;
      margin-right: 16px;
      user-select: none;
      white-space: nowrap;

      &:hover,
      &:hover i {
        opacity: 0.8;
      }

      i {
        background-image: url("@/assets/image/icon_play.svg");
        margin-right: 4px;
        background-position-y: 1px;
      }

      i.pause {
        background-image: url("@/assets/image/icon_pause.svg");
      }
    }

    .stop-generate {
      // width: 220px;
      // display: flex;
      flex-wrap: nowrap;
      align-content: center;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      position: relative;
      display: flex;

      div {
        opacity: 1;
        display: flex;
        align-items: center;
        // display: inline-block;
        user-select: none;
        // flex: 1;
        // width: 90px;
        padding-right: 16px;

        &:hover {
          opacity: 0.8;

          i {
            opacity: 0.8;
          }
        }
      }

      div:nth-child(1) {
        // border-right: 1px solid #B5BFD6;
        // padding-right: 22px;
      }

      i {
        margin-right: 4px;
        background-image: url("@/assets/image/icon_stop.png");
        user-select: none;
      }

      i.stop {
        background-position-y: 1px;
      }

      i.copy {
        background-image: url("@/assets/image/icon_copy.svg");
      }

      i.regenerate {
        background-image: url("@/assets/image/icon_regenerate.svg");
      }
    }
  }
  /deep/ video {
    max-width: 100% !important;
    width: 100% !important;
  }
}
.nolist-style {
  color: #bbbbbb;
  text-align: center;
  margin: 0 auto;
  justify-content: center !important;
}


@media (max-width: 1300px) {
  .converse-wrap {
    // width: 800px;
  }

  .converse-box {
    // padding-right: 30px;
  }
}
</style>
