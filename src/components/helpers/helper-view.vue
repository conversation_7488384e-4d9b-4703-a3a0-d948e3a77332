<template>
  <div class="helpermain">
    <div class="headerbox">
      <div class="helper_tit">智能体中心</div>
      <div class="header-right">
        <div class="inputbox" style="display: none;">
          <input placeholder="助手或名称" />
          <span class="search"
            ><img src="@/assets/image/history_search.svg" />
          </span>
        </div>
        <popover placement="bottom">
          <template #content>
            <span style="cursor: pointer">敬请期待</span>
          </template>
          <a class="addhelper"
            ><plus-outlined style="font-size: 15px" /> 创建智能体</a
          >
        </popover>
      </div>
    </div>
    <div class="helperwarp">
      <a-tabs v-model:activeKey="tabIdx">
        <a-tab-pane key="1" tab="智能体广场">
          <div class="filterbox">
            <span class="filtertit">业务类型</span>
            <div class="filters">
              <span class="active">全部</span>
              <span class="disabled">通用聊天</span>
              <span class="disabled">文档问答</span>
              <span class="disabled">文生图</span>
            </div>
          </div>
          <div class="emptybox">
            <span>敬请期待...</span>
          </div>
          <div class="helperbox" style="display: none;">
            <div class="helperCard">
              <h1>
                通用助手
                <span class="text">通用聊天</span>
              </h1>
              <div class="descbox">
                可以进行日常交流、文案生成、信息抽取、逻辑推理和代码编写等任务。
              </div>
              <div class="cardbom">
                <span class="">系统</span>
                <div class="controlbox" style="display: none">
                  <span class="cardRemove"> 移除 </span>
                  <span class="invite">邀请</span>
                </div>
              </div>
            </div>
            <div class="helperCard">
              <h1>
                文档助手
                <span class="document">文档问答</span>
              </h1>
              <div class="descbox">
                便捷、高效的文档问答助手，只需上传PDF文档，即可通过自然语言对话获取答案。让知识触手可及！
              </div>
              <div class="cardbom">
                <span class="">系统</span>
                <div class="controlbox" style="display: none">
                  <span class="cardRemove">移除</span>
                  <span class="invite">邀请</span>
                </div>
              </div>
            </div>
            <div class="helperCard">
              <h1>
                企标助手
                <span class="document">文档问答</span>
              </h1>
              <div class="descbox">
                简化对汽车制造过程中公司企业标准文件的查阅，提高资料查询效率。
              </div>
              <div class="cardbom">
                <span class="">系统</span>
                <div class="controlbox" style="display: none">
                  <span class="cardRemove">移除</span>
                  <span class="invite">邀请</span>
                </div>
              </div>
            </div>
            <div class="helperCard">
              <h1>
                识人才助手
                <span class="text">通用聊天</span>
              </h1>
              <div class="descbox">
                基于员工信息数据，高效实现员工信息查询、员工工作能力对比、多条件匹配合适人选等
              </div>
              <div class="cardbom">
                <span class="">系统</span>
                <div class="controlbox" style="display: none">
                  <span class="cardRemove">移除</span>
                  <span class="invite">邀请</span>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="我的智能体">
          <div class="filterbox">
            <span class="filtertit">业务类型</span>
            <div class="filters">
              <span class="active">全部</span>
              <span class="disabled">通用聊天</span>
              <span class="disabled">文档问答</span>
              <span class="disabled">文生图</span>
            </div>
          </div>
          <div class="emptybox">
            <span>敬请期待...</span>
          </div>
          <div class="helperbox" style="display: none;">
            <div class="helperCard">
              <h1>
                通用助手
                <span class="text">通用聊天</span>
              </h1>
              <div class="descbox">
                可以进行日常交流、文案生成、信息抽取、逻辑推理和代码编写等任务。 
              </div>
              <div class="cardbom">
                <span class="">系统</span>
                <div class="controlbox" style="display: none">
                  <span class="cardRemove"> 移除 </span>
                  <span class="invite">邀请</span>
                </div>
              </div>
            </div>
            <div class="helperCard">
              <h1>
                文档助手
                <span class="document">文档问答</span>
              </h1>
              <div class="descbox">
                便捷、高效的文档问答助手，只需上传PDF文档，即可通过自然语言对话获取答案。让知识触手可及！
              </div>
              <div class="cardbom">
                <span class="">系统</span>
                <div class="controlbox" style="display: none">
                  <span class="cardRemove">移除</span>
                  <span class="invite">邀请</span>
                </div>
              </div>
            </div>
            <div class="helperCard" v-if="qibiaoStatus === 0">
              <h1>
                企标助手
                <span class="document">文档问答</span>
              </h1>
              <div class="descbox">
                简化对汽车制造过程中公司企业标准文件的查阅，提高资料查询效率。
              </div>
              <div class="cardbom">
                <span class="">系统</span>
                <div class="controlbox" style="display: none">
                  <span class="cardRemove"> 移除 </span>
                  <span class="invite">邀请</span>
                </div>
              </div>
            </div>
            <div class="helperCard" v-if="personStatus === 0">
              <h1>
                识人才助手
                <span class="text">通用聊天</span>
              </h1>
              <div class="descbox">
                基于员工信息数据，高效实现员工信息查询、员工工作能力对比、多条件匹配合适人选等
              </div>
              <div class="cardbom">
                <span class="">系统</span>
                <div class="controlbox" style="display: none">
                  <span class="cardRemove">移除</span>
                  <span class="invite">邀请</span>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup>
import { popover } from "ant-design-vue";
import { ref, onMounted } from "vue";
import { PlusOutlined } from "@ant-design/icons-vue";
// import { helperLimits } from '@/api'
const tabIdx = ref("1");
// 判断企标助手是否有权限
const qibiaoStatus = ref(1) // 企标助手: 0代表有 1代表没有权限
const personStatus = ref(1) // 识人才助手： 0代表有 1代码没有权限
// todo 此处添加 识人才 权限
// const getHelperLimits = () => {
//   helperLimits().then(res => {
//     if (res.ret == 200) {
//       qibiaoStatus.value = res.data.qibiaoStatus
//       personStatus.value = res.data.personStatus
//     }
//   })
// }
onMounted(() => {
  // getHelperLimits()
})
</script>

<style lang="less" scoped>
.emptybox {
  color: #666;
}
input {
  outline: none;
}

/deep/.main-wrap {
  min-height: 695px;
}

.helpermain {
  // min-width: 1000px;
  flex: 1;
  overflow: hidden;
  backdrop-filter: blur(35px);
  /* Note: backdrop-filter has minimal browser support */
  border-radius: 10px;
  margin-left: 12px;
  // border: 1px solid;
  border-image-source: linear-gradient(
    133.05deg,
    rgba(255, 255, 255, 0.2) 10.57%,
    rgba(255, 255, 255, 0) 74.15%
  );
  background-color: #fdfeff80;
  border: 1px solid;
  border-bottom: 0;
  border-image-source: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  padding: 40px;
  min-height: 542px;
  display: flex;
  flex-direction: column;
}

.headerbox {
  display: flex;
  justify-content: space-between;
  padding-top: 40px;
  align-items: center;

  .helper_tit {
    color: var(--unnamed, #171a1f);
    font-size: 20px;
    line-height: 28px;
  }

  .header-right {
    display: flex;
    align-items: center;

    .inputbox {
      border-radius: 8px;
      border: 1px solid var(--fill-3, #e5e6eb);
      background: #fff;
      padding: 8px 40px 8px 12px;
      position: relative;
      margin-right: 30px;

      input {
        width: 278px;
        padding-left: 10px;
        border: none;
        background: none;
      }

      .search {
        width: 20px;
        height: 20px;
        position: absolute;
        top: 50%;
        margin-top: -10px;
        right: 12px;
        cursor: pointer;
      }
    }

    .addhelper {
      padding: 0 17px;
      color: #fff;
      font-size: 16px;
      border-radius: 8px;
      line-height: 38px;
      background: #5416FF;
      cursor: not-allowed;
    }
  }
}

.helperwarp {
  padding-top: 10px;
  overflow-y: hidden;
  flex: 1;

  /deep/.ant-tabs {
    color: var(--unnamed, #3f4650);
    font-size: 16px;
    line-height: 42px;
    height: 100%;

    .ant-tabs-tab:hover {
      color: #2f50c1;
    }

    .ant-tabs-nav {
      margin: 0px;
    }
  }

  /deep/.ant-tabs-ink-bar {
    // background: #2351f3;
  }

  /deep/.ant-tabs-tab {
    padding: 0px;
  }

  /deep/.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    // color: #5416FF;
  }

  /deep/ .ant-tabs-content {
    height: 100%;
  }

  /deep/ .ant-tabs-tabpane {
    height: 100%;
  }

  .filterbox {
    color: #181818;
    font-size: 16px;
    display: flex;
    align-items: center;
    line-height: 28px;
    padding: 25px 0px;

    .filtertit {
      padding-right: 48px;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        right: 24px;
        height: 13px;
        top: 50%;
        margin-top: -7.5px;
        width: 1px;
        background-color: #dfe6f1;
      }
    }

    .filters {
      align-items: center;
      justify-content: flex-start;

      span {
        line-height: 22px;
        font-size: 14px;
        text-align: center;
        color: #3f4650;
        padding: 5px 16px;
        background-color: #f0f4ff;
        border-radius: 16px;
        margin-right: 16px;
        cursor: pointer;
        transition: all 0.2s ease-in-out;

        &.active {
          color: #fff;
          background-color: #5416FF;
        }

        &:hover {
          font-weight: bolder;
        }

        &.disabled {
          cursor: not-allowed;
          opacity: 0.7;
        }

        &.disabled:hover {
          font-weight: inherit;
        }
      }
    }
  }

  .helperbox {
    display: flex;
    flex-wrap: wrap;
    height: calc(100% - 100px);
    overflow-y: auto;
    align-content: flex-start;
    padding: 5px;

    .helperCard {
      width: 32%;

      &:not(:nth-child(3n)) {
        margin-right: calc(4% / 2);
      }

      padding: 20px;
      border-radius: 10px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      background-color: rgba(102, 172, 255, 0.1);
      background-image: url("@/assets/image/hcardbg.png");
      background-repeat: no-repeat;
      background-position: right bottom;
      margin-bottom: 20px;
      transition: all 0.2s ease-in-out;

      h1 {
        color: var(--unnamed, #171a1f);
        font-size: 16px;
        line-height: 24px;
        padding: 0px 28px 10px 0px;
        position: relative;
        text-align: left;
        margin: 0px;

        span {
          color: #fff;
          border-radius: 2px;
          font-size: 12px;
          zoom: 0.83;
          padding: 4px;
          line-height: 11px;
          position: absolute;
          right: 0px;
          top: 4px;
          background: rgba(35, 81, 243, 0.5);
          text-align: center;
        }

        span.document {
          background-color: rgba(243, 135, 35, 0.5);
        }

        span.text {
          background-color: rgba(53, 184, 129, 0.5);
        }

        span.video {
          background-color: rgba(201, 35, 243, 0.5);
        }
      }

      .descbox {
        color: #666;
        text-align: justify;
        font-size: 16px;
        line-height: 22px;
        text-align: left;
        height: 68px;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        word-break: break-all;
        margin-bottom: 10px;
      }

      .cardbom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        line-height: 22px;
        color: var(--text-3, #86909c);

        .controlbox {
          color: var(--brand-16, #165dff);

          span {
            padding-left: 18px;
            background-position: left center;
            background-repeat: no-repeat;
            cursor: pointer;
          }

          .cardRemove {
            background-image: url("@/assets/image/remove.png");
            margin-right: 20px;
          }

          .invite {
            background-image: url("@/assets/image/share-one.png");
          }
        }
      }

      &:hover {
        // border: 1px solid rgba(179, 170, 226, 0.80);
        box-shadow: 0px 0px 5px rgb(179 170 226 / 53%);
      }
    }
  }
}
</style>
