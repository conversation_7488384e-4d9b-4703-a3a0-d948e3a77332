<template>
  <Modal v-model:visible="mdVisible" title="灵犀-长城办公AI 版本介绍 " :footer="null" class="introduce-com" width="80%"
    @cancel="cancel">
    <p>大家好，我是长城汽车IDC打造的人工智能助手灵犀-长城办公AI, 目前我采用的模型是基于业界领先的开源模型的基础上训练得来的（尚有多个模型调优对比中）；</p>
    <p></p>
    <p>
      我仍会不断吸取全世界的营养（优化我的模型和提高我的参数量级）和各领域的知识，持续进化，不断提高为各位同学的服务能力。为长城汽车打造自主可控的、安全的、具备通用以及汽车行业领域知识的大模型能力。我的进化能力是很快的哦，欢迎持续关注，感受我的成长。
    </p>
    <p></p>
    <p class="title">版本信息：</p>
    <p>
      <span>V1.3 - </span>
      <span>通用助手底层大模型服务切换为G2M大模型平台的开源模型服务；文档助手新增支持txt、word文档上传及基于文档知识库进行问答。实现新术语替换。增加换肤功能。</span>
      <span class='p_time'>24-07-11</span>
    </p>
    <p>
      <span>V1.2 - </span>
      <span>通用助手底层大模型服务切换为 OpenAI 服务。使用时请注意保护企业和个人隐私数据，遵守国家和公司相关规定，切勿泄露公司的保密信息！</span>
      <span class='p_time'>23-11-09</span>
    </p>
    <p>
      <span>V1.1 - </span>
      <span>新增企标助手用户白名单管理，支持技术中心用户权限管理；新增慧搜功能，支持全文检索、搜索内容高亮显示、参考文档定位预览、文档上下翻页及页面缩放。</span>
      <span class='p_time'>23-10-20</span>
    </p>
    <p>
      <span class='opc0'>V1.1 - </span>
      <span>新增文档助手，支持用户上传pdf文档并基于文档进行问答。</span>
    </p>
    <p>
      <span>V1.0 - </span>
      <span> UI/UE升级；新增企标问答助手，支持基于专业文档知识库进行问答、参考文档的定位预览、问答历史记录搜索；新增助手广场，并预置通用聊天助手和文档问答助手。</span>
      <span class='p_time'>23-08-03</span>
    </p>
    <p>
      <span>V0.2.1 - </span>
      <span>新增AI生图英文版，可输入英文提示词；优化了AI生图中文版；修复部分已知问题；添加接口熔断、限流机制、qps动态配置功能。</span>
      <span>23-06-14</span>
      
    </p>
    <p>
      <span>V0.2 - </span>
      <span>用户登录、问答记录、点赞倒赞、文档问答、语音输入、语音播报、以文生图。</span>
      <span>23-06-01</span>
      
    </p>
    <p>
      <span>V0.1 - </span>
      <span>自然语言的理解和处理能力，能够快速准确地理解用户的提问，并给出相关的答案，能够进行语言生成，为用户提供故事、文章等文本内容。</span>
      <span>23-04-30</span>
      
    </p>
    <p></p>
    <p class="title">注意事项：</p>
    <p>！只提供访问体验，不对返回的结果负责；</p>
    <p>！请勿发送涉政暴恐、黄赌毒、违反法律和公序良俗等言论，查出后记录将转交集团有关部门；</p>
    <p>提问清晰：请尽可能清晰地描述您的问题，以便G-Bot可以更好地理解您的意思并提供准确的答案；</p>
    <p>简明扼要：请尽量使用简单的语言和简洁的句子来表达您的问题，以便我可以更快地理解您的问题并为您提供有用的答案；</p>
    <p>如果您有多个问题，请一个一个地问。这样可以更好地帮助我理解和回答您的问题；</p>
    <p></p>
    <!-- <p>如果您有多个问题，请一个一个地问。这样可以更好地帮助我理解和回答您的问题。</p> -->
    <p class="title">联系方式：</p>
    <p></p>
    <p>产品经理：张涛（GW00266809）</p>
    <p>G-BOT产品体验用户中心（嗨长城群号31845016481）</p>
    <p class="no-dot"><span class="qrcode-box"><img class="" src="@/assets/img/qrcode.png" alt="" /></span></p>
    <p class="no-dot title" @click="clearq">嗨长城扫描二维码加入群聊咨询详细内容</p>
    <!-- <p @click="clearq">联系方式：G-BOT产品体验用户中心（嗨长城群号31845016481）</p> -->
  </Modal>
</template>

<script setup>
import { ref, defineProps, watch, defineEmits, onMounted, reactive, onBeforeUnmount, onUnmounted } from 'vue'
import { Modal } from 'ant-design-vue';
import { clearQuee } from '@/api'
const props = defineProps({
  modalVisible: Boolean
})
const emits = defineEmits(['cancel'])
const mdVisible = ref(false)
const cancel = () => {
  emits('cancel', mdVisible.value)
}

const clearq = () => {
  clearQuee().then(res => {
    console.log(res)
  })
}
let source = null
const testRequest = () => {

  if (window.EventSource) { ///chat/demo/getSseMsg
    // source = new EventSource(`/chatgpt/chat/window/question?${qs.stringify(sendParams)}`, { // 老版本
    source = new EventSource(`/gbot2/ask/text2`);
    // 加入历史问题列表
    //建立连接
    source.onopen = function (event) {
      console.log("建立连接", event);
      // answerStr.value = ''
      // scrollBtnInterval()
    }
    //接收数据
    source.onmessage = function (event) {
      console.log(event)
      // const { data } = event
      const data = JSON.parse(event.data).content
      //判断是否token失效
      // console.log(event.data)
      const _data = JSON.parse(event.data)
      if (_data.code && _data.code === 401) { // token失效时
        console.log('token 失效')

      } else if (_data.code && _data.code === 500) { //问题不合规时
      } else {
        // answerStr.value += data === '[DONE]' ? '' : data
      }
      // console.log(answerStr.value)
      // const _answerTxt = VueMarkdown.render(answerStr.value.length ? answerStr.value : '对不起,我无法帮到你~')
      // let _answerTxt = ''
      // if (answerStr.value.length) { // 如果服务器返回结果为空
      //   _answerTxt = answerStr.value
      // } else {
      //   _answerTxt = '对不起,我无法帮到你~'
      // }
      // const _answerTxt = answerStr.value.length ? answerStr.value : '对不起,我无法帮到你~'
      // msgList.list[_idx - 1].content = answerStr.value.length ? answerStr.value : '对不起,我无法帮到你~'

      //存入历史三条记录 （问题不合规的除外）
      // const historyArr = JSON.stringify(msgList.list.slice(msgList.list.length - 3).filter(item => item.content.indexOf('问题不合规') === -1)) // 获取最近三条
      // console.log('历史数据', historyArr)
      // setCookie('historyMsg', encodeURIComponent(historyArr, "utf-8"), 30); // 讲历史问题问题记录存入cookie 穿给后端实现 上下文问答

      // console.log(answerStr);
      // scrollBtn()
      //滚动到底部
      // const chatBoxHeight = document.getElementById('chat-box').offsetHeight //
      // chatWrap.scrollTop = chatBoxHeight - chatWrap.offsetHeight + 40
      if (data === '[DONE]') {

      }
    }
    //错误监听
    source.onerror = function (event) {
      if (event.readyState === EventSource.CLOSED) {
        // clearInterval(timer)
        console.log("连接关闭");
      } else {

      }
    }
  } else {
    timer = null
    console.log("浏览器不支持SSE");
  }
}
watch(
  () => props.modalVisible,
  (newVal, oldVal) => {
    console.log(newVal)
    mdVisible.value = newVal
  }
)
</script>

<style lang="less" scoped>
.introduce-com {
  p {
    margin: 10px 0;
    display: flex;
    span:nth-child(2) { flex: 1; padding-left: 5px; padding-right: 10px;}
  }

  p.title {
    color: #5416ff;
    font-size: 14px;
    font-weight: bold;
  }
  p.p_time {
    text-align: right;
  }
  .opc0{
    opacity:0;
  }
}

.qrcode-box img {
  width: 200px;
  height: 200px;
  border: 2px solid #5416ff;
  padding: 10px;
  border-radius: 10px;
}
</style>