<template>
  <div class="info_module layoutcss">
    <div class="areabox box" v-if="areaList.length > 0">
      <h1><img src="@/assets/image/area.svg" class="area_icon">领域上新</h1>
      <div class="list-box area-list">
        <div class="item" v-for="(item, index) in areaList" @click="handleItemClick(item)" :key="index">
          <div class="name"><span>{{ item.title }}</span></div>
          <div class="desc">{{ item.introduce }}</div>
        </div>
      </div>
    </div>
    <div class="infobox box" v-if="infoList.length > 0">
      <h1><img src="@/assets/image/trends.svg" class="area_icon">AI动态</h1>
      <div class="list-box info-list">
        <div class="item" v-for="(item, index) in infoList" @click="handleItemClick(item)" :key="index">
          <div class="name"><span>{{ item.title }}</span></div>
          <div class="desc">{{ item.introduce }}</div>
        </div>
      </div>

    </div>

  </div>

</template>

<script setup>
import { ref, onMounted } from 'vue'; 
import { useRouter } from 'vue-router'; 
import { getHomeSuggest } from '@/api/index.js';

const router = useRouter(); 

const handleItemClick = (item) => {
  //0 智能体，1链接，2图片链接 ,3markdown
  if(item.resourceType == 0) {
    router.push({
      path: "/chat-hello",
      query: {
        robotId: item.resourceContent * 1
      }
    });
  } else if (item.resourceType == 1) {
    window.open(item.resourceContent, '_blank');
  } 
};


const areaList = ref([]);
const infoList = ref([]);
onMounted(() => {
  getHomeSuggest({}).then(res => {
    if(res.code === 200) {
      areaList.value = res.data[0]
      infoList.value = res.data[1]
    } else {
      console.error('获取首页建议失败', res.message);
    }
  }).catch(err => {
    console.error('获取首页建议失败', err);
  });
});


</script>

<style scoped lang="less">
.info_module {
  display: flex; gap: 16px; text-align: left; margin-top: 24px;
  .box {
    flex: 1;
    border-radius: 16px;
    border: 1.5px solid #FFF;
    background: linear-gradient(180deg, #E3E7FF 0%, #EAEEFF 100%);
    box-shadow: 0px 4px 24px 0px rgba(129, 136, 255, 0.10);
    backdrop-filter: blur(4.300000190734863px);
    padding: 12px;
    h1 {
      color: #1C2024;
      font-size: 14px;
      font-weight: 700;
      line-height: normal;
      margin-bottom: 12px;
      display: flex; 
      align-items: center;
      .area_icon {
        margin-right: 4px;
      }
    }
  }
  .list-box {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    align-self: stretch;
    .item {
      padding: 0px 16px; width: 100%; font-size: 12px; cursor: pointer; height: 37px;
      border-radius: 8px; background: rgba(255, 255, 255, 0.70); display: flex; align-items: center;
      .name{
        transition: all 0.3 linear; font-size: 12px !important;
      }
      .desc {
        color: #1C2024; line-height: 18px;
        transition: all 0.3 linear;
      }
      &:hover {
        background: rgb(239 243 245);
        .desc {
          color: #000;
        }
      }
    }
  }
  .area-list {
    .name {
      border-radius: 4px;
      border: 1px solid rgba(53, 107, 253, 0.10);
      background: rgba(53, 107, 253, 0.05);
      padding: 0 8px;
      margin-right: 11px;
      span {
        background: linear-gradient(89deg, #36C0D2 0.96%, #3651FF 8.02%, #AB60F1 99.81%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
  .info-list .item .name {
    color: #1C2024; font-weight: 700; margin-right: 11px;
  }


}

</style>