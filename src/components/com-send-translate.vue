<template>
  <!-- ai翻译 -->
  <div
    class="send-wrap"
  >
    <div class="send-box">
      <div class="input-wrap">
        <!-- 试题生成的 模块 -->
        <a-textarea
          class="input-box"
          placeholder="请输入你想翻译的内容吧～"
          :auto-size="props.pageName === 'home' ? { minRows: 5, maxRows: 6} : { minRows: 2, maxRows: 4 }"
          v-model:value="chatMsg"
          @pressEnter="pressEnter"
          @change="inputChange"
          id="sendTextraea"
          ref="inputDom"
        />
      </div>
    </div>
  </div>
<!-- </div> -->
</template>

<script setup>
import {
  ref,
  defineProps,
  defineEmits,
  reactive,
  watch,
  onMounted,
  computed,
  defineExpose,
  onUnmounted,
  nextTick
} from "vue";
// import comMic from "@/components/com-mic";
// import comHistroyDetail from "@/components/com-histroy-detail";
import { useRoute, useRouter } from "vue-router";
import Bus from "@/utils/bus";
import { Upload, popover, Spin, Switch, Select, Tooltip } from "ant-design-vue";
import { message } from "ant-design-vue";
// import VueClickOutside from 'v-click-outside';

const props = defineProps({
  canAsk: Boolean, // 是否可以发送下一条问答
  messageId: Number,
  msgList: Array,
  robotobj: {
    type: Object,
    default: () => ({}),
  },
  pageName: String, // 从哪个页面引入
  hisMesLoading: Boolean // 新增属性，用于控制历史消息加载状态

});

const emits = defineEmits(["sendMsg", "modelChange", "innetChange"]);

const router = new useRouter();

/**
 * 输入框默认值text
 * 通过pageName区分
 */
const inputTextList = ref({
  'home': {
    1: '我可以帮你搜索、答疑、写作、请把你的任务交给我吧～' // id: 输入框文本
  },
  'chat': {
    1: '我可以帮你搜索、答疑、写作、请把你的任务交给我吧～',
    14: 'AI阅读 输入你想问的问题～',
  },  
  'hello': {
    1: '我可以帮你搜索、答疑、写作、请把你的任务交给我吧～',
    14: '请上传文档，开启阅读新体验吧～'
  }
})


const inputDom = ref(null);
const chatMsg = ref(""); // 聊天信息



// 发送按钮
const sendMsg = () => {
  // a阅读的题型生成，时判断参数
  if (props.robotobj.askType==5 && props.robotobj.askTypeClass == 2) {
    if(singleNum.value==0 && multiNum.value==0 && judgeNum.value==0) {
      message.warn("请至少生成一种题型") 
      return
    }
  } else {
    if (!chatMsg.value.trim().length) return;
  }
  if (props.hisMesLoading) {
    message.warn("历史消息加载中，请稍后...");
    return;
  }
  if (!props.canAsk) {
    message.warning("请稍等，正在回答上一个问题");
    return;
  }
  
  setTimeout(() => {
    chatMsg.value = "";
    // console.log(chatMsg.value)
  }, 0);
};

const pressEnter = (e) => {
  e.preventDefault();
  sendMsg();
};


const inputChange = (val) => {
  // 检测输入内容 进行相关逻辑
};



let textareaDom = null;
onMounted(() => {
  // 输入框dom
  textareaDom = document.getElementById("sendTextraea");
  Bus.$on("filenum", (num) => {
    filenum.value = num;
  });

  
});
onUnmounted(() => {
  Bus.$off("upfile");
  Bus.$off("filenum");
});
</script>

<style lang="less">
.ant-tooltip-inner {
  border-radius: 8px !important; overflow: hidden; font-size: 13px; color: #fff;
}
</style>
<style lang="less" scoped>
.send-wrap {
  width: 100%;

  .send-box {
    padding-top: 6px;
    // width: 55%;
    // max-width: 768px;
    margin: 0 auto;
    border-radius: 16px;
    background: linear-gradient(80deg, #36C0D2 0%, #3651FF 8%, #AB60F1 100%);
    padding: 2px;
    box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
    border-radius: 16px;

    .input-wrap {
      position: relative;
      overflow: hidden;
      width: 100%;
      height: 100%;
      min-height: 59px;
      background-color: #fff;
      border-radius: 15px;
      padding: 12px;
      // margin-bottom: 8px;
      /deep/ .input-box {
        width: 100%;
        height: 116px;
        background: #ffffff;
        border-radius: 12px;
        // padding: 14px;
        font-size: 16px;
        line-height: 26px;
        gap: 10px;
        border: none;
        padding: 4px;
        margin-bottom: 10px;
        &::placeholder {
            color: #8B8D98;
        }

        &:focus {
          box-shadow: none;
        }
      }
    }
    .btn-wrap {
      display: flex;
      color: #fff;
      user-select: none;
      width: 100%;
      margin: 0px auto 0px;
      align-items: center;
      justify-content: space-between;
      .btn-left {
        display: flex;
        align-items: center;
      }
      .btn-right {
        display: flex;
        align-items: center;
        span {

        }
      }
      .btn-right span:first-of-type {
          height: 32px; overflow: hidden;
      }

      .control-btn {
        display: flex;
        padding: 3px 8px;
        justify-content: center;
        align-items: center;
        margin-right: 8px;
        border-radius: 22px;
        border: 1px solid rgba(0, 0, 0, 0.03);
        background: #FFF;
        cursor: pointer;
        &:hover {
          opacity: 0.8;
        }
        span {
          color: #666;
          font-size: 14px;
          display: flex;
          align-items: center;
          img { margin-right: 4px; }
        }
        span.cur{
          background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: 500;
        }
        &.cur {
          border-radius: 22px;
          border: 1px solid rgba(53, 107, 253, 0.15);
          background: rgba(53, 107, 253, 0.07);
        }
      }
      .selectbox {
        color: #000;
        margin-right: 10px;
        /deep/ .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          border: 1px solid #eeedff !important;
          border-radius: 16px !important;
        }
        /deep/
          .ant-select-focused:not(.ant-select-disabled).ant-select:not(
            .ant-select-customize-input
          )
          .ant-select-selector {
          box-shadow: none !important;
        }
      }
      /deep/ .ant-select-dropdown {
        border-radius: 10px;
        overflow: hidden;
      }

      .shortcut {
        width: 32px; 
        height: 32px;
        cursor: pointer;
        background-size: contain;
        background-image: url("@/assets/image/sendbox/file.svg");
        &:hover,
        &.active {
          background-image: url("@/assets/image/sendbox/fileCur.svg");
          
        }
      }
      .icon-box {
        // width: 64px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease-in-out;
        margin-left: 10px;
        span.icon {
          display: inline-block;
          width: 32px;
          height: 32px;
          cursor: not-allowed;
          background: url("@/assets/image/sendbox/send.svg") center no-repeat;
          background-size: contain;
          // opacity: .6;
          transition: all 0.3s ease-in-out;
          &.canAsk {
            cursor: pointer;
            background-image: url("@/assets/image/sendbox/sendCur.svg") !important;
          }

          &:hover {
            // opacity: 1;
            transform: scale(0.9);
          }
        }
      }

    }
  }

  .bg-box {
    background: url("@/assets/image/ibg.png") no-repeat;
    border-radius: 8px 8px 0px 0px;
    width: 100%;
    height: 100%;
  }

  .quick-ask .title span {
    background-image: url("@/assets/image/@_big_icon.png");
  }

  .quick-ask.active {
    height: 250px;
  }
}
.controlbox {
  display: flex; 
  justify-content: space-between; 
  padding-bottom: 12px; 
  line-height: 24px; 
  width: 100%;
  z-index: 100;
  .controlleft-btns {
    display: flex; align-items: center;
  }
  .newChatBtn {
    color: #1C2024; font-size: 14px; padding: 6px 24px;border-radius: 16px; border: 1px solid #FFF; background: rgba(255, 255, 255, 0.50); box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13); backdrop-filter: blur(4.300000190734863px); margin-right: 6px; line-height: 24px;
    &:hover {
      background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      border: 1px solid rgba(53, 107, 253, 0.15);
    }
  }
  .hisReaderBtn {
    padding-right: 40px;
    span {
      display: inline; padding-right: 3px;
    }
    svg {
      position: absolute; right: 22px; top: 50%; margin-top: -9px;
    }

    &:hover {
      svg {
        stroke: rgb(67 78 251);
        animation: marginChange 1s linear infinite;
      }
    }
  }
  .lastAiReader {
    padding: 0px 10px;
    color: #1C2024;
    &:hover {
      color: rgb(67 78 251);
      text-decoration: underline;
    }
  }
  .fileListBtn {
    width: 32px;
    height: 32px;
    display: inline-block;
    background-image: url('@/assets/image/sendbox/fileList.svg');
    background-size: 100% 100%;
    cursor: pointer;
    &:hover {
      background-image: url('@/assets/image/sendbox/fileListCur.svg');
    }

  }
  .goAgentsBtn {
    padding: 6px 28px 6px 16px ;
  }
}
.readerSendBox {
  width: 100%;
  border-radius: 16px;
  border: 1px dashed #D9D9D9;
  background: rgba(255, 255, 255, 0.80);
  padding-top: 10px;
  padding-bottom: 10px;
  cursor: pointer;
  position: relative;
  .ant-upload-text {
    span {
      color: #364AFD; padding: 0 3px;
    }
  }
}
.aiReaderloadingbox {
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #ffffffb1;
  left: 0px;
  top: 0px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

/// ai阅读新增 添加的输入框内容
.typeInline {
  display: inline;
  width: 40px; 
  border-radius: 8px;
  text-align: center;
  border: none;
  // border-bottom: 1px solid #ccc;
  background-color: #e9e9e9a3;
}
.typeInlineArea {
  display: inline;
  border-radius: 8px;
  background-color: #e9e9e95e;
  border: none;
}

@media (max-width: 1300px) {
  
}
@keyframes marginChange {
  0% {
    right: 22px;
  }
  50% {
    right: 20px;
  }
  100% {
    right: 18px;
  }
}


</style>
