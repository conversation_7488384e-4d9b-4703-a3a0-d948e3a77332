<template>
  <div class="user-box">
    <!-- <span class="avatar">{{ userName }}</span>GW00322007 -->
    <!-- <div class='calc-box' v-if="showStatic">
      <div class='calc-item online'>在线：<Statistic :value="calcObj.onlineNum"  :valueStyle="{color:'skyblue'}"/></div>
      <div class='calc-item day'>日活：<Statistic :value="calcObj.dayNum"  :valueStyle="{color:'#75d875'}"/></div>
      <div class='calc-item month'>月活：<Statistic :value="calcObj.monthNum"  :valueStyle="{color:'#e79c12'}"/></div>
    </div> -->
    <Tooltip title="一键将页面添加到桌面快捷方式" color="#ffffffbd">
      <div class="downapp" id="install-button">
        <img src="@/assets/img/down_single.svg" alt="添加到桌面" />
        <span style="display: none;">
          添加到桌面
        </span>
      </div>
    </Tooltip>
    <popover placement="bottom" v-if="props && props.adminPower == 0">
      <template #content>
        <span style="cursor: pointer;" @click="goGbotAdmin">跳转后端管理平台</span>
      </template>
      <div class="to_setting" @click="goGbotAdmin"></div>
    </popover>
    <popover placement="bottom">
      <template #content>
        <span style="cursor: pointer;" @click="hanldeLogout">退出登录</span>
      </template>
      <!-- <template #title>
        <span>Title</span>
      </template> -->
      <Avatar class='avatar' shape="circle" size="large">{{ userName }}</Avatar>
    </popover>
  </div>
</template>

<script setup>
import { Avatar, message, Popover, Statistic, Tooltip } from 'ant-design-vue';
import { ref, defineProps, defineEmits, reactive, watch, onMounted, onUnmounted, computed } from 'vue'
import Bus from '@/utils/bus.js';
import { useRoute, useRouter } from 'vue-router'
import { setCookie, delCookie, getCookie } from '@/utils/common'
import { BellOutlined } from "@ant-design/icons-vue";
import { logout, countDay } from '@/api'
const router = useRouter()
const nameArr = JSON.parse(localStorage.getItem('userName')).split('')
const name = nameArr.slice(1, nameArr.length).join('')
const userName = ref(name)

const props = defineProps({
  adminPower: {
    default: 1
  }
})

const goGbotAdmin = ()=>{
  // const nameurl = window.location.origin
  // alert(nameurl, 'nameurl')
  // if(nameurl == 'http://localhost:8080'){
  //   window.open('http://localhost:3000/?token='+getCookie('userToken'),'_blank')
  // } else if(nameurl == 'https://gbot-test.gwm.cn'){
  //   window.open('http://gbotadmin-paas-bdtest.gwmit.cn/?token='+getCookie('userToken'),'_blank')
  // } else if(nameurl == 'https://gbot.gwm.cn'){
  //   window.open('https://gbot-admin.gwm.cn/?token='+getCookie('userToken'),'_blank')
  // }
  // 跳转到 后台管理系统
  window.open(`http://${process.env.VUE_APP_ADMINURL}?token=${getCookie('userToken')}` ,'_blank')
}

const hanldeLogout = () => {
  logout().then(res => {
    if (res.ret === 200) {
      delCookie('userToken')
      localStorage.setItem('userName', '')
      message.success('退出成功，即将返回登录页。')
      setTimeout(() => {
        router.push('/login')
      }, 1000)
    }
  })
}
// 监听doc关闭 显示隐藏 人数统计
// let showStatic = ref(true)
// let calcObj = ref({})
// let staticTimer = null
// // 每分钟更新一次 人数统计 
// const getStatic = () => {
//   countDay().then(res => {
//       console.log(res)
//       if(res.ret === 200) {
//         calcObj.value = res.data
//       }
//     })
// }
// const intervalCount = () => {
//   staticTimer = setInterval(() => {
//     getStatic()
//   }, 60000);
// }
onMounted(() => {
  // getStatic()
  // intervalCount()
  // Bus.$on('toggleStatic', (show) => {
  //   showStatic.value = !show
  // })
  let deferredPrompt;
  window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault(); // 阻止默认的安装提示显示
    deferredPrompt = e; // 保存事件对象以便后续使用

    // 显示自定义的安装按钮或提示
    document.getElementById('install-button').style.display = 'flex';
  });

  document.getElementById('install-button').addEventListener('click', () => {
    if (deferredPrompt) {
      deferredPrompt.prompt(); // 显示安装提示
      deferredPrompt.userChoice.then((choiceResult) => {
        if (choiceResult.outcome === 'accepted') {
          console.log('用户接受了安装提示');
        } else {
          console.log('用户拒绝了安装提示');
        }
        deferredPrompt = null; // 清除保存的事件对象
      });
    }
  });
})
onUnmounted(() => {
  // Bus.$off('toggleStatic')
  // staticTimer = null
})
</script>

<style lang="less">
.ant-tooltip-inner {
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgb(0 0 0 / 38%), 0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
  border-radius: 10px !important;
  color: #000 !important;

}
.ant-tooltip-placement-bottom .ant-tooltip-arrow-content, .ant-tooltip-placement-bottomLeft .ant-tooltip-arrow-content, .ant-tooltip-placement-bottomRight .ant-tooltip-arrow-content {
  box-shadow: -3px -3px 7px rgb(0 0 0 / 34%);
}
</style>
<style lang="less" scoped>
.downapp {
  padding: 0px 5px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  border-radius: 8px;
  // background-color: #fff;
  color: #001aff;
  font-size: 14px;
  border: 1px solid #d9e6ff00;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  span{
    line-height: 16px;
    // border-bottom: 1px solid #001aff;
  }
  img{ width: 22px; height: 22px; margin-bottom: 2px; }
  &:hover {
    // background-color: #e4edff;
    background-color: #2f2f2f17;
    border: 1px solid #e3e3e3;
    // span{ border-bottom: 1px solid #cccccc00; }
  }

}
.to_setting{ width: 31px; height: 31px; background: url('@/assets/img/settingskip.svg') no-repeat; background-size: 31px; margin-right: 10px; }
.user-box {
  position: absolute;
  top: 31px;
  right: 55px;
  z-index: 99;
  font-size: 16px;
  color: #fff;
  display: flex;
  align-items: center;
  cursor: pointer;

  span.avatar {
    width: 30px;
    height: 30px;
    background: #5416ff;
    border-radius: 4px;
    margin-right: 8px;
    line-height: 30px;
    text-align: center;
    // box-shadow: 2px 2px 7px 0px #00000045;
  }

  // /deep/ span:not(.ant-avatar, .ant-avatar-string) {
  //   display: flex;
  //   align-items: center;
  // }
  .calc-box {
    // background:#fff;
    color:#7a7b86;
    // display:block;
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: space-between;
    // border:1px solid #5416ff;
    font-size: 12px;
    padding:5px 10px;
    border-radius:20px;
    min-width: 120px;
    margin-right: 10px;
      cursor:default;
    a {
      font-weight: bold;
      cursor:default;
    }
    .day {
      padding:0 15px;
    }
    .calc-item{
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      align-items: center;
    }
    :deep(.ant-statistic) {
      // font-size:12px;
      .ant-statistic-content-value {
        font-size:12px;
        font-weight:bold;
        display:block;
        position: relative;
        bottom: 1px;
      }
    }
  }

}
</style>