<template>

  <div class="welcome-msg">Hi~你好,我是AI阅读</div>
  <div class="assistant-desc">支持解读文档及网页内容,提炼核心要点,文章内容搜索,助您高效吸收信息!</div>
  <div class="center-intro">
    <div class="center-intro-title">核心功能:</div>
    <div class="center-intro-list">
      <div class="center-intro-item">
        <div class="intro-item-img"><img src="@/assets/image/centerimg1.svg" alt="" /></div>
        <div class="intro-item-bottom">
          <div class="intro-item-title">复杂文档一键总结</div>
          <div class="intro-item-desc">自动总结文章概要</div>
        </div>
      </div>
      <div class="center-intro-item">
        <div class="intro-item-img"><img src="@/assets/image/centerimg2.svg" alt="" /></div>
        <div class="intro-item-bottom">
          <div class="intro-item-title">文章内容检索</div>
          <div class="intro-item-desc">对话式文档内容搜索</div>
        </div>
      </div>
      <div class="center-intro-item">
        <div class="intro-item-img"><img src="@/assets/image/centerimg3.svg" alt="" /></div>
        <div class="intro-item-bottom">
          <div class="intro-item-title">生成考试试题</div>
          <div class="intro-item-desc">多种试题题型一键生成</div>
        </div>
      </div>
      <div class="center-intro-item">
        <div class="intro-item-img"><img src="@/assets/image/centerimg4.svg" alt="" /></div>
        <div class="intro-item-bottom">
          <div class="intro-item-title">文章热词提取</div>
          <div class="intro-item-desc">文章热词自动提取</div>
        </div>
      </div>                                
    </div>
  </div>
  <div class="example-box">
    <div class="example-title">示例文件:</div>
    <div class="example-list">
      <div class="example-item" v-for="(item, idx) in fileList" :key="idx" @click="skipItem(item)">
        <div class="example-item-pdf">
          <img :src="'/agentImg' + item.onePdf.substring(item.onePdf.indexOf('/agent-oss'))" />
        </div>
        <div class="example-item-bottom">
          <div class="example-item-tag"></div>
          <div class="example-item-filename">
            {{ item.fileName }}
          </div>
          <div class="tag-desc">
            {{ item.size }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import * as PDF from 'pdfjs-dist';
import PdfjsWorker from "pdfjs-dist/build/pdf.worker.entry";
import { useRoute, useRouter } from 'vue-router'; 

import { ref, onMounted, defineProps } from 'vue';
import { readHelloLoading } from '@/api/index'
import { nextTick } from 'vue';


const router = useRouter();
const route = useRoute(); 
const props = defineProps({
  'robotobj': Object
})

let url = 'http://oss-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/gbot/deep-seek/2025-05-10/1234741013485870377026.pdf';
let url1 = 'http://oss-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/gbot/deep-seek/2025-05-10/1236869457042843929214.pdf'
// let url = '@/assets/image/pdfexample.pdf';

// 渲染PDF文件
const pdfRerder = (url, pdfPanel) => {
  const loadingTask = PDF.getDocument(url);
  loadingTask.promise.then((pdf) => {
    const numPages = pdf.numPages;
    pdf.getPage(1).then((page) => {
      const containerWidth = pdfPanel.clientWidth;
      const containerHeight = pdfPanel.clientHeight;
      const viewportInit = page.getViewport({ scale: 1 });
      const pageWidth = viewportInit.width;
      const pageHeight = viewportInit.height;
      const scale = Math.min(containerWidth / pageWidth, containerHeight / pageHeight);
      const viewport = page.getViewport({ scale });
      const canvas = document.createElement('canvas');
      canvas.style.borderRadius = '5px';
      const context = canvas.getContext('2d');
      canvas.width = viewport.width;
      canvas.height = viewport.height;
      pdfPanel.appendChild(canvas);
      const renderContext = {
        canvasContext: context,
        viewport
      };
      page.render(renderContext);
    });
  });
}
const skipItem = ( item ) => {
  // router.push({ path: '/ai-reader', query: { robotId: props.robotobj.robotId, askType: props.robotobj.askType } });
  
  localStorage.setItem('aiReaderFileId', item.fileId)
  
  router.push({
    path: "/ai-reader",
    query: {
      robotId: props.robotobj.robotId,
      askType: props.robotobj.askType
    }
  });
}

const fileList = ref([])
// 获取示例文件列表
const getExampleFileList = () => {
  readHelloLoading({robotId: props.robotobj.robotId}).then(res => {
    if(res.code == 200) {
      fileList.value = res.data
      // nextTick(() => {
      //   pdfRenderFn()
      // })
    }
  })
}

// const pdfRenderFn = () => {
//   for (let i = 0; i < fileList.value.length ; i++) {
//     const pdfPanel = document.getElementById('pdfPanel-'+(i+1));
//     pdfRerder(fileList.value[i].onePdf, pdfPanel)
    
//   }
// }

onMounted(() => {
  getExampleFileList()
  

});




</script>
<style lang="less" scoped>
.welcome-msg {
  font-size: 24px; line-height: 32px; color: #1C2024; font-weight: bold; margin-bottom: 5x;
}
.assistant-desc {
  font-size: 14px; color: #8B8D98; margin-bottom: 10px;
}
.center-intro {
  padding: 14px 0px 14px 14px; border-radius: 12px; background-color: #fff; overflow: hidden; margin-bottom: 14px;
  .center-intro-title {
    font-size: 14px; color: #1C2024; font-weight: bold; padding-bottom: 6px;
  }
  .center-intro-list {
    display: flex; gap: 14px;
    .center-intro-item { width: calc(25% - 14px); border-radius: 8px; overflow: hidden; border: 1px solid #F0F0F3; }
    .intro-item-bottom {
      padding: 6px 8px;
      .intro-item-title {
        font-size: 12px; color: #1C2024; font-weight: bold;
      }
      .intro-item-desc {
        font-size: 12px; color: #8B8D98;
      }
    }
    .intro-item-img {
      img { width: 100%; height: 100%; object-fit: cover; }
    }
  }
}
.example-box {
  padding: 14px 0px 14px 14px;
  border-radius: 12px;
  background-color: #fff;
  overflow: hidden;
  .example-title {
    font-size: 14px; color: #1C2024; font-weight: bold; padding-bottom: 6px;
  }
  .example-list {
    display: flex; overflow: hidden;
    .example-item {
      width: calc(22% - 14px); 
      border-radius: 8px;
      overflow: hidden; 
      border: 1px solid #F0F0F3;
      margin-right: 14px;
      background-color: #F6F7F9;
      padding: 5px;
      position: relative;
      cursor: pointer;
      transition: all 0.3 linear;
      &:hover {
        transform: scale(0.98);
        .example-item-filename {
          color: #0031cc;
        }
      }
      &:last-child {
        margin-right: 0;
      }
      .example-item-pdf {
        min-height: 80px;
        height: 131px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        img { max-width: 100%; max-height: 100%; }
      }
      .example-item-bottom {
        position: absolute;
        width: 100%;
        left: 0px;
        bottom: 0px;
        background-color: #fff;
        padding: 5px 5px 0px 25px;
      }
      .example-item-filename {
        white-space: nowrap; /* 保持在一行内 */
        overflow: hidden;    /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 超出部分显示为省略号 */
        font-size: 10px;
        color: #1C2024;
        font-weight: bold;
        line-height: 13px;
      }
      .tag-desc {
        font-size: 10px;
        color: #8B8D98;
        line-height: 13px;
      }
      .example-item-tag {
        width: 25px; height: 25px; background: url('@/assets/image/pdfTag.svg') no-repeat center center; position: absolute; left: 0px; top: 50%; transform: translateY(-50%);
      }
    }
  }
}

</style>

