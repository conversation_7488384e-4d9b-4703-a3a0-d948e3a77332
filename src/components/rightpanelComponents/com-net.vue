<template>
 <div class="net-panel">
  <div class="net-panel-title">搜索结果</div>
  <div class="net-content">
    <ul>
      <li v-for="(item, index) in webobj" :key="index">
        <div class="net-info">
          <div class="net-icon" :style="'background-image: url('+ item.topImage +')'">
          </div>
          <div>
            <span>{{ item.urlName }}</span>
            <span>{{ item.publishDate || '--' }}</span>
          </div>
        </div>
        <a :href="item.url" class="net-link" target="_blank">
          <span class="net-title">{{ item.title }}</span>
          <span class="net-description" v-if="item.content" > {{ item.content }} </span>
        </a>
      </li>
    </ul>
  </div>
 </div>
</template>
<script setup>
import { ref, defineProps, defineEmits, onMounted, reactive, onBeforeUnmount, onUnmounted, defineExpose, watch, toRefs, nextTick } from "vue";

const props = defineProps({
  webobj: {
    type: Array,
    default: () => []
  }
});
const webobj = toRefs(props).webobj;

</script>
<style lang="less" scoped>
.net-panel {
  width: 100%;
  height: 100%;
  padding: 24px 0px;
  display: flex;
  flex-direction: column;
  text-align: left;
  .net-panel-title {
    color: #000;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 100% */
    padding: 0px 24px 16px;
  }
  .net-content {
    flex: 1;
    overflow-y: auto;
    ul{
      list-style: none;
      padding: 0;
      margin: 0;
      li{
        // margin-top: 12px;
        padding: 0px 24px 16px;
        border-bottom: 1px solid #E0E1E6;
        &:hover{
          background-color: #00000009;
          border-radius: 12px;
          .net-title {
            color: #5416FF;
          }
        }
        .net-info {
          display: flex;
          line-height: 24px;
          span:first-child {
            font-size: 14px;
            color: #1C2024;
            margin: 0px 12px;
          }
          padding: 12px 0px;
          .net-icon {
            background-color: #e6e6e6;
            width: 24px;
            height: 24px;
            background-size: contain;
            border-radius: 100%;
            background-position: center center;
          }
        }
        .net-link {
          
          span{
            display: block;
            display: -webkit-box; /* 使用弹性盒子模型 */
            -webkit-box-orient: vertical; /* 垂直排列子元素 */
            -webkit-line-clamp: 2; /* 设置最多显示两行 */
            overflow: hidden; /* 隐藏超出部分 */
            text-overflow: ellipsis; /* 显示省略号 */
          }
        }
        .net-title {
          color: #000;
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          padding-bottom: 8px;
        }
        .net-description {
          color: #8B8D98;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }

      }
    }

  }
}
</style>