<template>
    <div class="pagetitle">
      <Dropdown>
        <span>{{ props.title }}
          <img src="@/assets/image/arrow.svg" class="arrow-down" />
        </span>
        <template #overlay>
          <Menu>
            <MenuItem>
              <a href="javascript:;" @click="handleEdit">修改名称</a>
            </MenuItem>
            <!-- <MenuItem>
              <a href="javascript:;">2nd menu item</a>
            </MenuItem> -->
            <!-- <MenuItem>
              <a href="javascript:;">3rd menu item</a>
            </MenuItem> -->
          </Menu>
        </template>
      </Dropdown>
    </div>
    <Modal v-model:visible="mdVisible" title="修改名称" class="title-modal" :okText="'确定'" :cancelText="'取消'" @cancel="cancel" @ok="handleOk">
      <p><a-input v-model:value="valueTit" maxlength="20" placeholder="输入修改后的名称，最长20字" /></p>

    </Modal>
</template>
<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { Modal, Dropdown, Menu, message } from 'ant-design-vue'
import { MenuItem } from 'ant-design-vue/es/menu'
import { updateMessageName } from '@/api/index.js';


const props = defineProps({
  title: {
    type: String,
    default: '默认标题'
  },
  messageId: {
    type: Number
  }
})

const emit = defineEmits(['titleChange'])

const mdVisible = ref(false)
const valueTit = ref()

const handleEdit = () => {
  if(props.messageId) {
    valueTit.value = props.title; // 设置默认值为当前标题
    mdVisible.value = true
  }
}

const updataLoading = ref(false)
const handleOk = () => {
  // 这里需要添加修改名称的逻辑
  if(updataLoading.value) {
    message.warn('正在修改中，请勿重复操作'); 
    return;
  }
  updataLoading.value = true;
  updateMessageName({ messageId: props.messageId, messageName: valueTit.value }).then((res) => {
    if (res.code === 200) {
      message.success('名称已修改');
      mdVisible.value = false;
      updataLoading.value = false;
      emit('titleChange', valueTit.value);
    } else {
      updataLoading.value = false;
      // message.error('修改失败');
    }
  }).catch((err) => {
    updataLoading.value = false;
    // message.error('修改失败');
  })

}
const cancel = () => {
  mdVisible.value = false
}



</script>
<style lang="less">
.ant-dropdown-menu-title-content {
  text-align: center;
}
</style>

<style lang="less" scoped>
.pagetitle {
  line-height: 24px;
  padding: 5px 0px;
  span{
    color: #1C2024;
    &:hover {
      cursor: pointer;
      background-color: #e4e4e4;
      border-radius: 5px;
      padding: 2px 10px;
    }
    img {
      width: 18px;
      height: 18px;
      vertical-align: middle;
    }
  }
}

</style>