<template>
  <!-- <div style="width: 100%;"> 新版AI阅读为askType 5时，chat参数不显示，hello显示，别的都是home不显示即可-->
  <div class="controlbox layoutcss" v-if="(pageName !== 'home' && robotobj.askType !=5) || (robotobj.askType ==5 && pageName !== 'chat')">
    <div class="controlleft-btns">
      <a 
        href="javascript:;" 
        class="newChatBtn hisReaderBtn" 
        @click="hisReaderFn"
        v-if="
          robotobj.askType == 5
        "
      > 
        <!-- AI阅读情况 -->
        <span>历史阅读</span>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M8.97962 4.14645C9.17488 3.95118 9.49146 3.95118 9.68672 4.14645L13.6867 8.14645C13.882 8.34171 13.882 8.65829 13.6867 8.85355L9.68672 12.8536C9.49146 13.0488 9.17488 13.0488 8.97962 12.8536C8.78436 12.6583 8.78436 12.3417 8.97962 12.1464L12.1261 9H2.6665C2.39036 9 2.1665 8.77614 2.1665 8.5C2.1665 8.22386 2.39036 8 2.6665 8H12.1261L8.97962 4.85355C8.78436 4.65829 8.78436 4.34171 8.97962 4.14645Z" fill="#1C2024"/>
        </svg>
      </a>
      <a 
        href="javascript:;" 
        class="lastAiReader" 
        @click="checkAIReader('old')"
        v-if="
          robotobj.askType == 5
        "
      > 
        <span>旧版AI阅读</span>
      </a>

      <a v-else href="javascript:;" class="newChatBtn" @click="newChat">新对话</a>
      <!-- 文件 -->
      <Tooltip v-if="filenum > 0" title="打开文件列表" color="#00000099" v-model:visible="TPvisible" @mouseenter="handleFirstMouseEnter">
        <a v-if="filenum > 0" href="javascript:;" class="fileListBtn" @click="fileSpanShow"></a>
      </Tooltip>
      <a 
        href="javascript:;" 
        class="lastAiReader" 
        @click="checkAIReader('new')"
        v-if="
          robotobj.robotId == 2
        "
      > 
        <span>新版AI阅读</span>
      </a>

    </div>
    <a href="javascript:;" class="goAgentsBtn" @click="skipAgents">更多智能体</a>
  </div>
  <!-- ai阅读的上传功能 -->
  <div v-if="robotobj.askType == 5 && pageName == 'hello'" class="readerSendBox layoutcss">
    <div class="aiReaderloadingbox" v-if="aiReaderLoading">
      <Spin tip="Uploading..." >
        <!-- <a-alert 
          message="Alert message title"
          description="Further details about the context of this alert."
        ></a-alert> -->
      </Spin>
    </div>
    <Upload
      name="file"
      :show-upload-list="false"
      :customRequest="aiReaderRequest"
      draggable="true"
      :beforeUpload="beforeUpload"
    >
      <p class="ant-upload-drag-icon">
        <img src="@/assets/image/upbox/uptag.svg" />
      </p>
      <p class="ant-upload-text"><span>点击</span>或<span>拖放</span>上传本地文件</p>
      <p class="ant-upload-hint">最大10M，支持 PDF、Word、TXT 文件</p>
    </Upload>


  </div>
  <div v-else
    class="send-wrap layoutcss"
  >
    <div class="send-box">
      <div class="input-wrap">
        <!-- 试题生成的 模块 -->
        <template
        v-if="props.robotobj.askType == 5 && props.robotobj.askTypeClass == 2"
        >
        单选题
        <a-input class="typeInline" placeholder="5" size="small" @input="(event) => handleInput(event, 'singleNum')"  v-model:value="singleNum" />
        个，
        多选题
        <a-input class="typeInline" placeholder="3" size="small" @input="(event) => handleInput(event, 'multiNum')"  v-model:value="multiNum" />
        个，
        判断题
        <a-input class="typeInline" placeholder="2" size="small" @input="(event) => handleInput(event, 'judgeNum')" v-model:value="judgeNum" />
        个。<br>
        生成试题的要求:
        <a-textarea class="typeInlineArea" placeholder="可输入生成试题的要求" v-model:value="que_require" />
        </template>
        <a-textarea
          class="input-box"
          v-else
          :placeholder="                                           
            isRecording
              ? '请对我说你想说的话，我可以识别你说的内容哦～'
              : ((inputTextList[props.pageName] && inputTextList[props.pageName][props.robotobj.robotId]) || inputTextList[props.pageName][1])
              // : '我可以帮你搜索、答疑、写作、请把你的任务交给我吧～'
          "
          :auto-size="props.pageName === 'home' ? { minRows: 3, maxRows: 4} : (props.robotobj.askType == 5 ? { minRows:1, maxRows:2 }: { minRows: 2, maxRows: 4 })"
          v-model:value="chatMsg"
          @pressEnter="pressEnter"
          @change="inputChange"
          id="sendTextraea"
          ref="inputDom"
        />
        <div class="btn-wrap">
          <div class="btn-left">
            <div class="control-btn deepThinkC" :class="{ cur: props.model }" v-if="controlBtnsObj.reasoningStatus === 0">
              <span :class="{ cur: props.model}" @click="deepThinkChange">
                <img width="20" v-if="!props.model" src="@/assets/image/sendbox/think.svg" />
                <img width="20" v-else src="@/assets/image/sendbox/thinkCur.svg" />
                深度思考
              </span>
            </div>

            <div class="control-btn inNetBox" :class="{ cur: props.innet }" v-if="controlBtnsObj.webSearchStatus === 0">
              <span :class="{ cur: props.innet }" @click="innetChange">
                <img width="20" v-if="!props.innet" src="@/assets/image/sendbox/net.svg" />
                <img width="20" v-else src="@/assets/image/sendbox/netCur.svg" />
                联网检索
              </span>
            </div>
          </div>
          <div class="btn-right">
            <Upload
              name="file"
              :show-upload-list="false"
              :customRequest="customRequest"
              ref="uploadRef"
              v-show="controlBtnsObj.fileStatus === 0"
            >
              <!-- shortcut file 不要修改 -->
              <Spin :spinning="fileupLoading" @click="uploadChange">
                <div class="shortcut file"></div>
              </Spin>
            </Upload>
            <div class="icon-box" :class="{ recording: isRecording }">
              <!-- <span class="icon mic-icon" /> -->
              <!-- 语音录入 <com-mic @recordingStatus="recordingStatus" @socketMsg="socketMsg" /> -->
              <!-- 可以发送条件或者 AI阅读的试题生成 -->
              <span
                class="icon send-icon"
                @click="sendMsg"
                :class="{ canAsk: (props.canAsk && chatMsg.length > 0) || (props.robotobj.askType==5 && props.robotobj.askTypeClass == 2) }"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<!-- </div> -->
</template>

<script setup>
import {
  ref,
  defineProps,
  defineEmits,
  reactive,
  watch,
  onMounted,
  computed,
  defineExpose,
  onUnmounted,
  nextTick
} from "vue";
// import comMic from "@/components/com-mic";
// import comHistroyDetail from "@/components/com-histroy-detail";
import { useRoute, useRouter } from "vue-router";
import Bus from "@/utils/bus";
import { Upload, popover, Spin, Switch, Select, Tooltip } from "ant-design-vue";
import { readFileUp, filesUp, createMsg } from "@/api";
import { message } from "ant-design-vue";
import { HistoryOutlined, InboxOutlined } from "@ant-design/icons-vue";
import net_svg from "@/components/svg/net_svg.vue";
// import VueClickOutside from 'v-click-outside';

const props = defineProps({
  canAsk: Boolean, // 是否可以发送下一条问答
  messageId: Number,
  msgList: Array,
  robotobj: {
    type: Object,
    default: () => ({}),
  },
  model: Boolean,
  innet: Boolean,
  pageName: String, // 从哪个页面引入
  hisMesLoading: Boolean // 新增属性，用于控制历史消息加载状态

});

const emits = defineEmits(["sendMsg", "modelChange", "innetChange"]);

const router = new useRouter();

/**
 * 输入框默认值text
 * 通过pageName区分
 */
const inputTextList = ref({
  'home': {
    1: '我可以帮你搜索、答疑、写作、请把你的任务交给我吧～' // id: 输入框文本
  },
  'chat': {
    1: '我可以帮你搜索、答疑、写作、请把你的任务交给我吧～',
    14: 'AI阅读 输入你想问的问题～',
  },  
  'hello': {
    1: '我可以帮你搜索、答疑、写作、请把你的任务交给我吧～',
    14: '请上传文档，开启阅读新体验吧～'
  }
})

const fileList = ref([])

const aiReaderLoading = ref(false)
const aiReaderRequest = ( data ) => {
  console.log(data, 'data')
  aiReaderLoading.value = true;
  const file = data.file;
  const formData = new FormData();
  formData.append("file", file);
  formData.append("robotId", props.robotobj.robotId+'');

  readFileUp(formData)
    .then(res => {
      aiReaderLoading.value = false;
      if(res.code == 200) {
        let fileId = res.data
        message.success({
          key: "fileup",
          content: "文件上传成功",
        });
        // 
        localStorage.setItem('aiReaderFileId', fileId)
      
        router.push({
          path: "/ai-reader",
          query: {
            robotId: props.robotobj.robotId,
            askType: props.robotobj.askType
          }
        });






      }
    })

}

const beforeUpload = file => {
  const isLt10M = file.size / 1024 / 1024 <= 10;
  if (!isLt10M) {
    message.error('上传文件必须小于10M!');
  }
  return isLt10M;
};



// 深度思考按钮相关数据
let deepThink = ''
const deepThinkChange = () => {
  emits("modelChange", !props.model);
  // switch (props.model) {
  //   case "deepseek-r1":
  //     deepThink = 'doubao-lite-32k';
  //     break;
  //   case "doubao-lite-32k":
  //     deepThink = 'deepseek-r1';
  //     break;
  // }
  // emits("modelChange", deepThink); // 通知父组件切换模型类型
};

// 联网按钮相关数据
const innetChange = () => {
  emits("innetChange", !props.innet); // 通知父组件切换模型类型
};

const inputDom = ref(null);
const uploadRef = ref(null);
const chatMsg = ref(""); // 聊天信息

// AI阅读试题生成
const singleNum = ref();
const multiNum = ref();
const judgeNum = ref();
const que_require = ref('')

const handleInput = (e, tag) => {
  // 只允许数字输入（包括退格、删除、方向键等）
  // console.log(e,'eeeeeeee')
  eval(tag).value = e.target.value.replace(/[^0-9]/g, '');
  // param.value = 
}

// 发送按钮
const sendMsg = () => {
  // if (chatMsg.value === "慧画：" || chatMsg.value === "慧搜：") return;
  // a阅读的题型生成，时判断参数
  if (props.robotobj.askType==5 && props.robotobj.askTypeClass == 2) {
    if(singleNum.value==0 && multiNum.value==0 && judgeNum.value==0) {
      message.warn("请至少生成一种题型") 
      return
    }
  } else {
    if (!chatMsg.value.trim().length) return;
  }
  if (props.hisMesLoading) {
    message.warn("历史消息加载中，请稍后...");
    return;
  }
  if (!props.canAsk) {
    message.warning("请稍等，正在回答上一个问题");
    return;
  }
  if (isRecording.value) return;
  if (props.robotobj.askType==5 && props.robotobj.askTypeClass == 2) {
    let queObj = {
      "singleNum": singleNum.value || 5, //单选 
      "multiNum": multiNum.value || 3,//多选
      "judgeNum": judgeNum.value || 2,//判断
      "text": que_require.value
    }
    emits("sendMsg", queObj);
  } else {
    emits("sendMsg", chatMsg.value);
  }
  setTimeout(() => {
    chatMsg.value = "";
    // console.log(chatMsg.value)
  }, 0);
};

const pressEnter = (e) => {
  e.preventDefault();
  sendMsg();
};


const inputChange = (val) => {
  // 检测输入内容 进行相关逻辑
};

const fileSpanShow = () => {
  Bus.$emit("leftPanelChange", true);
};

// 录音
let isRecording = ref(false);
const recordingStatus = (val) => {
  console.log("录音状态", val);
  isRecording.value = val;
  if (val) chatMsg.value = ""; //开始录音情况输入框内容
};
// socket发来消息内容
const socketMsg = (msg) => {
  console.log(msg);
  chatMsg.value += msg;
  textareaDom.scrollTop = 1000000; // 设置文本滚动
};

const filenum = ref(0);
const fileupLoading = ref(false)

watch(
  () => fileupLoading.value,
  (newVal) => {
    Bus.$emit("fileupLoading", fileupLoading.value);
  }
);



// 上传文件
const customRequest = (data) => {
  if(props.pageName !== 'chat') {
    // 非聊天界面直接上传，执行中转操作
    fileControlFn(data)
    return
  }
  
  fileupLoading.value = true;
  const file = data.file;
  const formData = new FormData();
  formData.append("file", file);
  formData.append("messageId", props.messageId);
  console.log(formData,'formData=====')
  // Bus.$emit("preFile", file);

  filesUp(formData)
    .then((res) => {
      fileupLoading.value = false;
      if (res.code === 200) {
        message.success({
          key: "fileup",
          content: "文件上传成功",
        });
        Bus.$emit("fileListChange", "success");
        if (!props.msgList.length && filenum.value == 0) {
          // 还需要当前文件列表为空
          Bus.$emit("titleChange", data.file.name);
        }
        // 给出上传成功的提示！（上边的emit可以去除掉了。newConverseTitle 保留）
      } else {
        
      }
    })
    .catch((e) => {
      fileupLoading.value = false;
    });
};

const uploadChange = (event) => {
  if (fileupLoading.value === true) {
    message.warning({
      key: "fileup",
      content: "有文件正在上传，请稍后再试！",
    });
    event.stopPropagation();
    return;
  }
};


// 文件上传处理函数，首页和欢迎页上传文件时的处理函数
const fileControlFn = (filedata) => {
  createMsg(props.robotobj).then(res => {
    if (res.code === 200) {
      const file = filedata.file; // 用户选择的文件
      const messageId = res.data.messageId;

      // ✅ 1. 打开数据库（确保版本号递增）
      const request = indexedDB.open("FileStorageDB", 4); // 版本号从 1 → 2

      // ✅ 2. 确保 onupgradeneeded 创建 objectStore
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        console.log("Database upgrade needed"); // 调试用
        if (!db.objectStoreNames.contains("files")) {
          db.createObjectStore("files", { keyPath: "id" }); // 创建 objectStore
        }
      };

      // ✅ 3. 在 onsuccess 之后再操作数据库
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Database opened successfully"); // 调试用

        // ✅ 4. 确保 objectStore 名称正确
        const transaction = db.transaction("files", "readwrite");
        const store = transaction.objectStore("files");

        // 存储文件
        store.put({
          id: messageId,
          file: file,
          name: file.name,
          type: file.type,
        });
        transaction.oncomplete = () => {
          console.log("File stored successfully");
          db.close(); // 操作完成后关闭数据库
          localStorage.setItem(messageId+'fs', true)
          router.push({ name: 'chat', params: { messageId: res.data.messageId } });
        };
      };

      // ✅ 5. 错误处理
      request.onerror = (event) => {
        console.error("Database error:", event.target.error);
      };
    } else {
      console.error('Failed to create message:', res);
    }
  })
}

// 新建会话按钮
const newChat = () => {
  if (props.pageName === 'hello') {
    message.warning({
      key: "newChat",
      content: "已是最新对话,请尝试问个问题吧！",
    });
    return;
  }

  router.push({
    path: "/chat-hello",
    query: {
      robotId: props.robotobj.robotId
    }
  });
}

// 历史阅读会话
const hisReaderFn = () => {
  console.log(props.robotobj.robotId, props.robotobj.askType)
  router.push({
    path: '/ai-reader',
    query: {
      robotId: props.robotobj.robotId,
      askType: props.robotobj.askType
    }
  });
}

// 旧版AI阅读
const checkAIReader = ( tag ) => {
  if(tag == 'old') {
    router.push({
      path: '/chat-hello',
      query: {
        robotId: 2,
        askType: 0
      }
    });
  } else {
    router.push({
      path: '/chat-hello',
      query: {
        robotId: 14,
        askType: 5
      }
    });
  }
  setTimeout(() => {
    window.location.reload();
  }, 200)

}



const skipAgents = () => {
  router.push({
    path: "/agent-page"
  });
};

const getDBFileFn = () => {
  // 增加是否已存file的判断。否则直接取报错。
  let messageId = props.messageId
  let fileContain = localStorage.getItem(messageId+'fs')
  // debugger
  // console.log(messageId, 'messageId')
  // console.log(fileContain, 'fileContain')
  if( props.pageName === 'chat' && fileContain) {
    localStorage.removeItem(messageId+'fs')

    const request = indexedDB.open("FileStorageDB", 4);

    request.onsuccess = (event) => {
      const db = event.target.result;
      const transaction = db.transaction("files", "readwrite"); // 需要读写权限
      const store = transaction.objectStore("files");

      // ✅ 1. 查询数据
      const getRequest = store.get(messageId);

      getRequest.onsuccess = (event) => {
        const fileData = event.target.result;
        if (fileData) {
          console.log("找到匹配的文件，messageId:", messageId);
          const file = new File([fileData.file], fileData.name, { type: fileData.type });
          
          // ✅ 2. 传递文件给回调函数
          customRequest({ file });

          // ✅ 3. 立即删除该记录
          const deleteRequest = store.delete(messageId);
          
          deleteRequest.onsuccess = () => {
            console.log("已清理 messageId 对应的数据:", messageId);
          };

          deleteRequest.onerror = (event) => {
            console.error("删除失败:", event.target.error);
          };
        } else {
          console.log("未找到 messageId 对应的文件:", messageId);
        }
      };

      transaction.oncomplete = () => {
        db.close(); // 操作完成后关闭数据库
      };
    };

    request.onerror = (event) => {
      console.error("数据库打开失败:", event.target.error);
    };
  }
}


// 控制 Tooltip 是否显示
const TPvisible = ref(false);

// 标记是否已经触发过第一次鼠标悬停
const hasFirstMouseEnter = ref(false);
// 第一次鼠标悬停时的处理逻辑
const handleFirstMouseEnter = () => {
  if (!hasFirstMouseEnter.value) {
    // 标记已经触发过第一次悬停
    hasFirstMouseEnter.value = true;

    // 设置 TPvisible=false，让 Tooltip 恢复默认行为（后续由鼠标事件控制）
    TPvisible.value = false;
  }
  // 如果已经触发过第一次悬停，则不做任何操作（后续由 Tooltip 自身控制显示）
};

const controlBtnsObj = ref({})
let textareaDom = null;
onMounted(() => {
  // 输入框dom
  textareaDom = document.getElementById("sendTextraea");
  Bus.$on("filenum", (num) => {
    filenum.value = num;
  });
  Bus.$on("upfile", (data) => {
    customRequest(data);
  });

  Bus.$on("controlBtnsObjChange", (data) => {
    if(data) {
      controlBtnsObj.value = data;
    }
  });

  // 默认打开文件的提示打开
  TPvisible.value = true;

  // 首页、欢迎页有待发送文件内容 处理发送
  nextTick(() => {
    getDBFileFn()
  })

});
onUnmounted(() => {
  Bus.$off("upfile");
  Bus.$off("filenum");
});
</script>

<style lang="less">
.ant-tooltip-inner {
  border-radius: 8px !important; overflow: hidden; font-size: 13px; color: #fff;
}
</style>
<style lang="less" scoped>
.send-wrap {
  width: 100%;

  .send-box {
    padding-top: 6px;
    // width: 55%;
    // max-width: 768px;
    margin: 0 auto;
    border-radius: 16px;
    background: linear-gradient(80deg, #36C0D2 0%, #3651FF 8%, #AB60F1 100%);
    padding: 2px;
    box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
    border-radius: 16px;

    .input-wrap {
      position: relative;
      overflow: hidden;
      width: 100%;
      height: 100%;
      min-height: 59px;
      background-color: #fff;
      border-radius: 15px;
      padding: 12px;
      // margin-bottom: 8px;
      /deep/ .input-box {
        width: 100%;
        height: 116px;
        background: #ffffff;
        border-radius: 12px;
        // padding: 14px;
        font-size: 16px;
        line-height: 26px;
        gap: 10px;
        border: none;
        padding: 4px;
        margin-bottom: 10px;
        &::placeholder {
            color: #8B8D98;
        }

        &:focus {
          box-shadow: none;
        }
      }
    }
    .btn-wrap {
      display: flex;
      color: #fff;
      user-select: none;
      width: 100%;
      margin: 0px auto 0px;
      align-items: center;
      justify-content: space-between;
      .btn-left {
        display: flex;
        align-items: center;
      }
      .btn-right {
        display: flex;
        align-items: center;
        span {

        }
      }
      .btn-right span:first-of-type {
          height: 32px; overflow: hidden;
      }

      .control-btn {
        display: flex;
        padding: 3px 8px;
        justify-content: center;
        align-items: center;
        margin-right: 8px;
        border-radius: 22px;
        border: 1px solid rgba(0, 0, 0, 0.03);
        background: #FFF;
        cursor: pointer;
        &:hover {
          opacity: 0.8;
        }
        span {
          color: #666;
          font-size: 14px;
          display: flex;
          align-items: center;
          img { margin-right: 4px; }
        }
        span.cur{
          background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: 500;
        }
        &.cur {
          border-radius: 22px;
          border: 1px solid rgba(53, 107, 253, 0.15);
          background: rgba(53, 107, 253, 0.07);
        }
      }
      .selectbox {
        color: #000;
        margin-right: 10px;
        /deep/ .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          border: 1px solid #eeedff !important;
          border-radius: 16px !important;
        }
        /deep/
          .ant-select-focused:not(.ant-select-disabled).ant-select:not(
            .ant-select-customize-input
          )
          .ant-select-selector {
          box-shadow: none !important;
        }
      }
      /deep/ .ant-select-dropdown {
        border-radius: 10px;
        overflow: hidden;
      }

      .shortcut {
        width: 32px; 
        height: 32px;
        cursor: pointer;
        background-size: contain;
        background-image: url("@/assets/image/sendbox/file.svg");
        &:hover,
        &.active {
          background-image: url("@/assets/image/sendbox/fileCur.svg");
          
        }
      }
      .icon-box {
        // width: 64px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease-in-out;
        margin-left: 10px;
        span.icon {
          display: inline-block;
          width: 32px;
          height: 32px;
          cursor: not-allowed;
          background: url("@/assets/image/sendbox/send.svg") center no-repeat;
          background-size: contain;
          // opacity: .6;
          transition: all 0.3s ease-in-out;
          &.canAsk {
            cursor: pointer;
            background-image: url("@/assets/image/sendbox/sendCur.svg") !important;
          }

          &:hover {
            // opacity: 1;
            transform: scale(0.9);
          }
        }
      }

    }
  }

  .bg-box {
    background: url("@/assets/image/ibg.png") no-repeat;
    border-radius: 8px 8px 0px 0px;
    width: 100%;
    height: 100%;
  }

  .quick-ask .title span {
    background-image: url("@/assets/image/@_big_icon.png");
  }

  .quick-ask.active {
    height: 250px;
  }
}
.controlbox {
  display: flex; 
  justify-content: space-between; 
  padding-bottom: 12px; 
  line-height: 24px; 
  width: 100%;
  z-index: 100;
  .controlleft-btns {
    display: flex; align-items: center;
  }
  .newChatBtn {
    color: #1C2024; font-size: 14px; padding: 6px 24px;border-radius: 16px; border: 1px solid #FFF; background: rgba(255, 255, 255, 0.50); box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13); backdrop-filter: blur(4.300000190734863px); margin-right: 6px; line-height: 24px;
    &:hover {
      background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      border: 1px solid rgba(53, 107, 253, 0.15);
    }
  }
  .hisReaderBtn {
    padding-right: 40px;
    span {
      display: inline; padding-right: 3px;
    }
    svg {
      position: absolute; right: 22px; top: 50%; margin-top: -9px;
    }

    &:hover {
      svg {
        stroke: rgb(67 78 251);
        animation: marginChange 1s linear infinite;
      }
    }
  }
  .lastAiReader {
    padding: 0px 10px;
    color: #1C2024;
    &:hover {
      color: rgb(67 78 251);
      text-decoration: underline;
    }
  }
  .fileListBtn {
    width: 32px;
    height: 32px;
    display: inline-block;
    background-image: url('@/assets/image/sendbox/fileList.svg');
    background-size: 100% 100%;
    cursor: pointer;
    &:hover {
      background-image: url('@/assets/image/sendbox/fileListCur.svg');
    }

  }
  .goAgentsBtn {
    padding: 6px 28px 6px 16px ;
  }
}
.readerSendBox {
  width: 100%;
  border-radius: 16px;
  border: 1px dashed #D9D9D9;
  background: rgba(255, 255, 255, 0.80);
  padding-top: 10px;
  padding-bottom: 10px;
  cursor: pointer;
  position: relative;
  .ant-upload-text {
    span {
      color: #364AFD; padding: 0 3px;
    }
  }
}
.aiReaderloadingbox {
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #ffffffb1;
  left: 0px;
  top: 0px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

/// ai阅读新增 添加的输入框内容
.typeInline {
  display: inline;
  width: 40px; 
  border-radius: 8px;
  text-align: center;
  border: none;
  // border-bottom: 1px solid #ccc;
  background-color: #e9e9e9a3;
}
.typeInlineArea {
  display: inline;
  border-radius: 8px;
  background-color: #e9e9e95e;
  border: none;
}

@media (max-width: 1300px) {
  
}
@keyframes marginChange {
  0% {
    right: 22px;
  }
  50% {
    right: 20px;
  }
  100% {
    right: 18px;
  }
}


</style>
