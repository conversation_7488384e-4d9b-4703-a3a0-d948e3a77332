<template>
  <div>
  <div class="sidebar" :class="{'sidebarhide': props.silderbarTag}">
      <div class="sidebar-group">
        <!-- 首页 -->
        <Tooltip overlayClassName="sidebar-tooltip" title="回到首页" :getPopupContainer="(triggerNode) => triggerNode.parentNode.parentNode" placement="right">
          <div class="sidebar-button home-button" @click="silderSkip('/home')">
            <span></span>
            AI助理
          </div>
        </Tooltip>
      </div>

      <div class="sidebar-group">
        <!-- 智能体中心 -->
        <Tooltip overlayClassName="sidebar-tooltip" title="智能体中心" :getPopupContainer="(triggerNode) => triggerNode.parentNode.parentNode" placement="right">
          <div class="sidebar-button agentcenter-button" :class="{'cur': route.path === '/agent-page'}" @click="silderSkip('/agent-page')">
            <span></span>
            智能体<br>中心
          </div>
        </Tooltip>
        <!-- 我的智能体 -->
        <Tooltip overlayClassName="sidebar-tooltip" title="我的智能体" :getPopupContainer="(triggerNode) => triggerNode.parentNode.parentNode" placement="right">
          <div class="sidebar-button myAgent-button" :class="{'cur': route.path === '/myagent-page'}" @click="silderSkip('/myagent-page')">
            <span></span>
            我的<br>智能体
          </div>
        </Tooltip>
        <!-- 开发者门户 prevent-default  prevent-default-->
        <Tooltip overlayClassName="sidebar-tooltip" title="开发者门户" :getPopupContainer="(triggerNode) => triggerNode.parentNode.parentNode" placement="right">
          <div class="sidebar-button knowledge-button" @click="silderGoLink('menhu')">
            <span></span>
            开发者<br>门户
          </div>
        </Tooltip>
        <!-- 历史会话 -->
        <Tooltip overlayClassName="sidebar-tooltip" title="历史会话" :getPopupContainer="(triggerNode) => triggerNode.parentNode.parentNode" placement="right">
          <div class="sidebar-button history-button" :class="{'cur': route.path === '/history'}" @click="silderSkip('/history')">
            <span></span>
            历史会话
          </div>
        </Tooltip>
      </div>

      <div class="sidebar-group">
        <!-- 设置 -->
        <popover overlayClassName="sidebar-popover" :getPopupContainer="(triggerNode) => triggerNode.parentNode.parentNode" placement="left">
          <template #content>
            <div class="sidebartab prevent-default" title="敬请期待"  @click="showTips('敬请期待！')">语言切换</div>
            <div class="sidebartab" @click="hanldeLogout">退出登录</div>
          </template>
          <div class="sidebar-button setting-button">
            <span></span>
            个人配置
          </div>
        </popover>
        <!-- 用户中心 -->
        <popover overlayClassName="sidebar-popover" :getPopupContainer="(triggerNode) => triggerNode.parentNode.parentNode" placement="left">
          <template #content>
            <div class="sidebartab" id="install-button" @click="installAppToDesk">桌面快捷方式</div>
            <div class="sidebartab prevent-default" title="敬请期待" @click="showTips('敬请期待！')">浏览器插件</div>
          </template>
          <div class="sidebar-button down-button">
            <span></span>
            快捷方式
          </div>
        </popover>
      </div>
      <div class="sidebar-group">
        <!-- 创建智能体  禁用->以下两个容器中添加prevent-default-->
        <Tooltip title="新建智能体" overlayClassName="sidebar-tooltip" :getPopupContainer="(triggerNode) => triggerNode.parentNode.parentNode" placement="right">
          <div class="sidebar-button createAgent-button" @click="silderGoLink('addAgents')">
            <span></span>
            新建
          </div>
        </Tooltip>
      </div>
    
  </div>
  <div class="sidebarToggle" v-if="props.silderbarBtnVisiable" :class="{'sidebarToggleC': !props.silderbarTag}" @click="sidebarToggleFn">
    <img src="@/assets/image/sidebar/sidebar_toggle.svg" />
  </div>
  </div>
</template>  

<script setup>
import { ref, onMounted, onUnmounted, watch, defineProps, defineEmits } from 'vue';
import { Tooltip, popover, message } from "ant-design-vue";
import { setCookie, delCookie, getCookie } from '@/utils/common'
import { useRoute, useRouter } from 'vue-router'
import { logout } from '@/api'

const router = useRouter()
const route = useRoute() // 使用 useRoute 获取当前路由信息
// console.log('route.path', route.path) // 输出当前路由路径
// const robotId = ref(route.query.robotId)
// const askType = ref(route.query.askType)

const props = defineProps({
  silderbarTag: {
    type: Boolean
  },
  silderbarBtnVisiable: {
    type: Boolean
  }
})

// watch(
//   () => props.silderbarTag,
//   (val) => {
//     debugger
//     if(val == 5) {
//       silderbarTag.value = false
//     } else {
//       silderbarTag.value = true
//     }
//   }
// )

const emits = defineEmits(['sidebarToggleFn'])
const sidebarToggleFn = () => {
  emits('sidebarToggleFn', !props.silderbarTag)
}

const showTips = (contentTxt) => {
  message.warning(contentTxt);
};

const hanldeLogout = () => {
  let data = {
    "userToken": getCookie('userToken')
  }

  logout(data).then(res => {
    if (res.code === 200) {
      delCookie('userToken')
      localStorage.setItem('userName', '')
      message.success('退出成功，即将返回登录页。')
      setTimeout(() => {
        router.push('/login')
      }, 1000)
    }
  })
}

const silderSkip = (path) => {
  if(path === '/home') {
    router.push(path)
  } else {
    const route = router.resolve({ path: path });
    window.open(route.href, path); // 同一页面在同一窗口打开
  }
}

// 左侧菜单跳转外链
const silderGoLink = (tag) => {
  if(tag == 'addAgents') {
    window.open('https://agent.gwm.cn/login', '_blank')
  } else if(tag == 'menhu') {
    window.open('http://intelligence.test.paas.gwm.cn/index', '_blank')
  }
}



let deferredPrompt;
let appLoadFlag = false; // 标记应用是否已经加载

const installAppToDesk = () => {
  if (!appLoadFlag) {
    message.warning('应用已经安装，无法再次安装到桌面。'); // 提示用户应用已经加载
    return; // 如果应用已经加载，直接返回，不执行后续代码
  }
  if (deferredPrompt) {
    deferredPrompt.prompt(); // 显示安装提示
    deferredPrompt.userChoice.then((choiceResult) => {
      if (choiceResult.outcome === 'accepted') {
        console.log('用户接受了安装提示');
      } else {
        console.log('用户拒绝了安装提示');
      }
      deferredPrompt = null; // 清除保存的事件对象
    });
  }
}

onMounted(() => {
  window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault(); // 阻止默认的安装提示显示
    deferredPrompt = e; // 保存事件对象以便后续使用
    appLoadFlag = true
  });

  // console.log(props.silderbarTag,'silderbarTag')

});

</script>

<style lang="less">
.sidebar-tooltip{
  left: 85px !important;
  .ant-tooltip-content{
    border-radius: 8px; overflow: hidden;
    .ant-tooltip-arrow { display: none; }
    .ant-tooltip-inner {
      background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%) !important;
      box-shadow: none !important;
      // cursor: pointer;
    }
  }
  &.prevent-default {
    .ant-tooltip-content {
      .ant-tooltip-inner { 
        cursor:not-allowed; color: #fff; background: #B9BBC6 !important;
      }
    }
  }
}
.sidebar-popover {
  left: 85px !important; width: 110px !important;
  .ant-popover-content {
    border-radius: 6px; overflow: hidden;
  }
  .ant-popover-arrow {
    display: none;
  }
  .ant-popover-inner {
    background: linear-gradient(69deg, #3DBBD7 2.55%, #364AFD 32.25%, #B76EF1 120.42%) !important;
    box-shadow: none !important;
  }
  .ant-popover-inner-content {
    padding: 0px;
  }
  .sidebartab {
    text-align: center; color: #fff; padding: 5px 5px; background-color: #ffffff23; cursor: pointer; border-bottom: 1px solid #ffffff70;
    &:last-child {
      border-bottom: none;;
    }
  }
  .prevent-default {
    cursor:not-allowed; background: #B9BBC6; color: #fff;
    
  }
}

</style>
<style lang="less" scoped>
.sidebar {
  display: flex;
  padding: 8px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #FAFBFF;
  box-shadow: 2px 2px 7.7px 1px rgba(146, 159, 202, 0.19), 0px 0px 1px 1px rgba(255, 255, 255, 0.45) inset;
  border-radius: 12px;
  transition: all 0.3s ease;
  max-height: 562px;
  overflow-y: hidden;
  
}

.sidebarhide {
  margin-left: -80px;
  width: 0px;
}

.sidebar-group {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding-top: 6px;
  border-top: 1px solid #E5E5E5;
  width: 100%;
  &:first-child {
    border-top: none;
    padding-top: 0;
  }
  .sidebar-button {
    // width: 32px;
    // height: 32px;
    width: 100%;
    background-position: center center;
    background-repeat: no-repeat;
    cursor: pointer;
    border: none;
    margin-bottom: 6px;
    padding: 6px;
    background-position: top center;
    font-size: 13px;
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 18px;
    span{
      display: block;
      width: 18px;
      height: 18px;
      margin-bottom: 4px;
    }
    &.prevent-default {
      cursor: not-allowed;
    }
    &:hover, &.cur {
      color: #000;
    }
  }
  .home-button {
    margin-bottom: 5px;
    span{
      width: 32px;
      height: 32px;
      background-image: url(@/assets/image/logo.png);
      background-size: 32px 32px;
      border-radius: 8px;
    }
  }
  
  .agentcenter-button {
    span{
      background-image: url(@/assets/image/sidebar/agentcenter.svg);
    }
  }
  .agentcenter-button:hover, .agentcenter-button.cur { 
    span {
      background-image: url(@/assets/image/sidebar/agentcenterCur.svg);
    }
  }
  .myAgent-button {
    span{
      background-image: url(@/assets/image/sidebar/myAgent.svg);
    }
  }
  .myAgent-button:hover, .myAgent-button.cur { 
    span {
      background-image: url(@/assets/image/sidebar/myAgentCur.svg);
    }
  }
  .knowledge-button {
    span{
      background-image: url(@/assets/image/sidebar/knowledgeBase.svg);
    }
  }
  // 待放开
  // .knowledge-button:hover, .knowledge-button.cur {
  //   background-image: url(@/assets/image/sidebar/knowledgeBaseCur.svg);
  // }
  .knowledge-button:hover {
    background-color: #D9D9E0; border-radius: 5px;
  }
  .history-button {
    span{
      background-image: url(@/assets/image/sidebar/history.svg);
    }
  }
  .history-button:hover, .history-button.cur  {
    span{
      background-image: url(@/assets/image/sidebar/historyCur.svg);
    }
  }
  .setting-button {
    span{
      background-image: url(@/assets/image/sidebar/setting.svg);
    }
  }
  .setting-button:hover, .setting-button.cur   {
    span{
      background-image: url(@/assets/image/sidebar/settingCur.svg);
    }
  }
  .down-button {
    span{
      background-image: url(@/assets/image/sidebar/down.svg);
    }
  }
  .down-button:hover, .down-button.cur   {
    span{
      background-image: url(@/assets/image/sidebar/downCur.svg);
    }
  }
  .createAgent-button {
    span{
      background-image: url(@/assets/image/sidebar/createAgent.svg);
    }
    margin-bottom: 0px;
  }
  // .createAgent-button:hover, .createAgent-button.cur   {
  //   background-image: url(@/assets/image/sidebar/createAgentCur.svg);
  // }
  .createAgent-button:hover {
    background-color: #D9D9E0; border-radius: 5px;
  }

  .sidebar-button:hover, .sidebar-button.cur {
    border-radius: 8px;
    background: linear-gradient(92deg, rgba(61, 187, 215, 0.10) 2.47%, rgba(54, 74, 253, 0.10) 35.07%, rgba(76, 78, 251, 0.10) 92.41%, rgba(183, 110, 241, 0.10) 123.1%);
  }
}

.sidebarToggle {
  position: absolute;
  width: 25px;
  height: 25px;
  display: flex;
  border-radius: 15px;
  background-color: #FAFBFF;
  align-items: center;
  justify-content: space-around;
  top: 50%;
  left: 0px;
  transform: translateX(-50%);
  cursor: pointer;

  img {
    width: 15px;
    height: 15px;
    transform: rotate(180deg);
  }


  &:hover{
    img {
      animation: move 1s linear infinite;
      border-color: #364afd;
    }
  }

}

.sidebarToggleC {
  img {
    transform: rotate(0deg);
  }
}

@keyframes move {
  0% {
    margin-right: -5px;
  }
  50% {
    margin-right: 0px;
  }
  100% {
    margin-right: 5px;
  }
}


</style>
