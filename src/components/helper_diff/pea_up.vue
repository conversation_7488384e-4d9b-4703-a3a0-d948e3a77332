<template>
  <Modal v-model:visible="mdVisible" title="PEA报告生成" :footer="null" class="zpeabox" width="80%" getContainer="#main-wrap-right"
  @cancel="cancel" >

  <div class="pea_up_container">
    <div class="pea_header">
      <!-- <p>PEA报告生成</p> -->
      <p>
        欢迎使用PEA报告生成助手，我可以帮助你快速生成PEA文件，请按如下格式输入信息
      </p>
    </div>
    <!-- @finishFailed="onFinishFailed" -->
    <div class="pea_content">
      <div class="formbox">
        <Form
          ref="formRef"
          :model="formState"
          name="formRef"
          autocomplete="off"
          @finish="onFinish"
          
        >
          <FormItem
            label="需求名称"
            name="need_name"
            :rules="[
              { 
                required: true, 
                message: '请输入需求的具体名称' 
                }
            ]"
          >
            <a-textarea
              v-model:value="formState.need_name"
              show-count
              :maxlength="300"
              placeholder="请输入需求的具体名称"
            />
          </FormItem>

          <FormItem
            label="需求分类"
            name="need_classification"
            :rules="[
              {
                required: true,
                message: '请描述分类，例如车型需求，平台需求等',
              },
            ]"
          >
            <a-textarea
              v-model:value="formState.need_classification"
              show-count
              :maxlength="300"
              placeholder="请描述分类，例如车型需求，平台需求等"
            />
          </FormItem>

          <FormItem
            label="提出原因"
            name="give_reason"
            :rules="[
              {
                required: true,
                message: '请描述提出此需求的背景及目的',
              },
            ]"
          >
            <a-textarea
              v-model:value="formState.give_reason"
              show-count
              :maxlength="300"
              placeholder="请描述提出此需求的背景及目的"
            />
          </FormItem>

          <FormItem
            label="问题描述"
            name="problem"
            :rules="[
              {
                required: true,
                message: '请简要而准确地描述遇到的问题或挑战',
              },
            ]"
          >
            <a-textarea
              v-model:value="formState.problem"
              show-count
              :maxlength="300"
              placeholder="请简要而准确地描述遇到的问题或挑战"
            />
          </FormItem>

          <FormItem
            label="用户类型"
            name="user_type"
            :rules="[
              {
                required: true,
                message: '请描述用户（或群体）是谁',
              },
            ]"
          >
            <a-textarea
              v-model:value="formState.user_type"
              show-count
              :maxlength="300"
              placeholder="请描述用户（或群体）是谁"
            />
          </FormItem>

          <FormItem
            label="场景描述"
            name="scene"
            :rules="[
              {
                required: true,
                message:
                  '请描述用户在何种情况下会使用该功能。例如使用环境，操作流程等。',
              },
            ]"
          >
            <a-textarea
              v-model:value="formState.scene"
              show-count
              :maxlength="300"
              placeholder="请描述用户在何种情况下会使用该功能。例如使用环境，操作流程等。"
            />
          </FormItem>
          <FormItem
            label="期望结果"
            name="desired_result"
            :rules="[
              {
                required: true,
                message:
                  '请描述用户在何种情况下会使用该功能。例如使用环境，操作流程等。',
              },
            ]"
          >
            <a-textarea
              v-model:value="formState.desired_result"
              show-count
              :maxlength="300"
              placeholder="请描述用户在何种情况下会使用该功能。例如使用环境，操作流程等。"
            />
          </FormItem>
        </Form>
      </div>
      <Button type="primary" html-type="submit" class="pea-button" :loading="peaGenLoading" @click="submitForm">生成报告</Button>
    </div>
  </div>
  </Modal>
</template>
<script setup>
import { ref, defineProps, watch, defineEmits, onMounted, reactive } from 'vue'
import { Form, Modal } from 'ant-design-vue';
import { FormItem } from "ant-design-vue/es/form";

const props = defineProps({
  modalVisible: Boolean
})
const emits = defineEmits(['cancel', 'sendMsg'])


const peaGenLoading = ref(false); // 添加加载状态控制变量

const formRef = ref(null);

/**
 * need_name // 需求名称
 * need_classification // 需求分类
 * give_reason // 提出原因
 * problem // 问题描述
 * user_type // 客户类型
 * scene // 使用场景描述
 * desired_result // 期望结果
 * 
 */
const formState = reactive({
  need_name: "",
  need_classification: "",
  give_reason: "",
  problem:"",
  user_type: "",
  scene: "",
  desired_result: ""

});
const onFinish = (values) => {
  console.log("Success:", values);
};
// const onFinishFailed = (errorInfo) => {
//   console.log("Failed:", errorInfo);
// };
const submitForm = async () => {
  peaGenLoading.value = true;
  formRef.value.validateFields().then((values) => {
    console.log("values", values);
    // 在这里添加生成报告的逻辑
    let passData = {
      // type: 'pea_up',
      // data: {
        need_name: formState.need_name,
        need_classification: formState.need_classification,
        give_reason: formState.give_reason,
        problem: formState.problem,
        user_type: formState.user_type,
        scene: formState.scene,
        desired_result: formState.desired_result,
      // }
    }
    emits('sendMsg', JSON.stringify(passData))
    mdVisible.value = false
    peaGenLoading.value = false
    // 关闭弹窗
    cancel() // 调用父组件的cancel方法关闭弹窗


  }).catch((error) => {
    console.log("error", error);
  });
};


const mdVisible = ref(false)
const cancel = () => {
  formRef.value.resetFields();
  emits('cancel', mdVisible.value)
}

watch(
  () => props.modalVisible,
  (newVal, oldVal) => {
    console.log(newVal)
    mdVisible.value = newVal
  }
)
</script>

<style lang="less">
.zpeabox {
  top: 50% !important;
  transform: translateY(-50%) !important;
  border-radius: 10px;
  padding-bottom: 0px;
  overflow: hidden;
  height: 95%;
  .ant-modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .ant-modal-title{
    color: #5416ff;
    font-size: 18px;
    font-weight: bold;
  }
  .ant-modal-body{
    padding: 10px 0px;
    flex: 1;
    height: calc(100% - 55px);
  }
  .ant-form-item-with-help .ant-form-item-explain {
    text-align: left;
  }
  .pea-button{
    // width: 120px;
    margin-left: 82px;
    width: calc(80% - 82px);
    border: 1px solid #5416ff4f;
    border-radius: 5px;
    background-color: #898fff1c;
    height: 32px;
    cursor: pointer;
    color: #000000ac;
    &:hover{
      background-color: #898fff38;
      color: #5416ffd6;
    }
  }
}
</style>
<style lang="less" scoped>
.pea_up_container {
  min-width: 559px;
  position: relative;
  flex: 1;
  overflow: hidden;
  -webkit-backdrop-filter: blur(35px);
  backdrop-filter: blur(35px);
  border-radius: 10px;
  margin-left: 12px;
  border-image-source: linear-gradient(
    133.05deg,
    rgba(255, 255, 255, 0.2) 10.57%,
    rgba(255, 255, 255, 0) 74.15%
  );
  background-color: #fdfeff80;
  border: 1px solid;
  border-bottom: 0;
  border-image-source: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  display: flex;
  flex-direction: column;
  height: 100%;
  .pea_content{
    flex: 1;
    overflow: hidden;
  }
  .formbox{
    height: calc(100% - 50px);
    width: 100%;
    overflow-y: auto;
    margin-bottom: 18px;
  }
  form {
    width: 80%;
    margin: 0 auto;
    a,
    area,
    [role="button"],
    input:not([type="range"]),
    label,
    select,
    summary,
    textarea {
      width: 100%;
    }
  }
}

.pea_header{
  p { padding-bottom: 10px; font-size: 16px; }
}
</style>
