<template>
  <p class="pLine">
    <span>需求名称：</span>
    <span>
      {{ peaAskItemObj.need_name || '--' }}
    </span>
  </p>
  <p class="pLine">
    <span>需求分类：</span>
    <span>
      {{ peaAskItemObj.need_classification || '--' }}
    </span>
  </p>
  <p class="pLine">
    <span>提出原因：</span>
    <span>
      {{ peaAskItemObj.give_reason || '--' }}
    </span>
  </p>
  <p class="pLine">
    <span>问题描述：</span>
    <span>
      {{ peaAskItemObj.problem || '--' }}
    </span>
  </p>
  <p class="pLine">
    <span>用户类型：</span>
    <span>
      {{ peaAskItemObj.user_type || '--' }}
    </span>
  </p>
  <p class="pLine">
    <span>场景描述：</span>
    <span>
      {{ peaAskItemObj.scene || '--' }}
    </span>
  </p>
  <p class="pLine">
    <span>期望结果：</span>
    <span>
      {{ peaAskItemObj.desired_result || '--' }}
    </span>
  </p>
</template>
<script setup>
import { ref, defineProps, watch, defineEmits, onMounted, reactive } from 'vue'

const props = defineProps({
  item: Object
})
const peaAskItem = ref(props.item)

const peaAskItemObj = ref({})
try
{
  peaAskItemObj.value = JSON.parse(peaAskItem.value.content)

} catch (e) {
  peaAskItemObj.value = {}
  console.log('Error parsing JSON:', e)
}



</script>

<style lang="less">

</style>
