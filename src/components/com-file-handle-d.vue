<template>
  <div class="handle-box">
    <div class="handle-box-download" @click="download">
      <span class="download icon" />下载
    </div>
    <popover trigger="click" placement="bottom" v-model:visible="popVisible">
      <template #content>
        <div class="popover-confirm">
          <p v-if="props.delLoading"><loading-outlined style="margin-right:10px;" />正在删除...</p>
          <template v-else>
            <p class="red" @click="confirmDel">确认删除</p>
            <p @click="popVisible = false">再考虑下</p>
          </template>
        </div>
      </template>
      <div class="del icon" />
    </popover>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, reactive, watch, onMounted, computed } from 'vue'
import { popover } from 'ant-design-vue';
import {
  LoadingOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
  fileDetail: Object,
  delLoading: Boolean
})

const popVisible = ref(false)
const emits = defineEmits(['confirmDel'])
const confirmDel = (idx) => {
  emits('confirmDel', props.fileDetail)
  // popVisible.value = false
}

const download = () => {
  let file = props.fileDetail.file.uploadFile || props.fileDetail.content
  let fileName = props.fileDetail.file.name || props.fileDetail.file.fileTitle
  console.log(file, fileName)
  if (typeof file === 'string') { // 如果为地址则先去请求 相关文件
    downloadUrl(file, fileName)
  } else {
    downloadFile(file, fileName)
  }

}
const downloadFile = (file, fileName) => {
  let reader = new FileReader();
  reader.readAsArrayBuffer(file, "UTF-8");
  reader.addEventListener('load', (e) => {
    let base64 = e.target.result;
    let blob = new Blob([base64], { type: file.type });
    console.log("Blob对象", blob);
    const element = document.createElement("a");
    const href = URL.createObjectURL(blob);
    element.href = href;
    element.setAttribute("download", fileName);
    element.style.display = "none";
    element.click();
    //调用这个方法来让浏览器知道不用在内存中继续保留对这个文件的引用了。
    URL.revokeObjectURL(href);
    element.remove();
  })
}
const downloadUrl = (url, fileName) => {
  // let a = document.createElement("a");
  // a.href = url;
  // a.download = fileName;
  // a.click();
  const xhr = new XMLHttpRequest();
  xhr.open('get', url, true);
  xhr.responseType = 'blob';
  xhr.onload = () => {
    // 返回文件流，进行下载处理
    downloadFile(xhr.response, fileName);
  };
  xhr.send();
}

watch(
  () => props.delLoading,
  (val) => {
    popVisible.value = val
  }
)
</script>

<style lang="less" scoped>
.handle-box {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 14px;
  color: #7D7D94;
  display: flex;
  align-items: center;

  .handle-box-download {
    cursor: pointer;
    width: 60px;
    // margin-right: 20px;
    display: flex;
    align-items: center;

    &:hover {
      opacity: .8;
    }
  }

  .icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url('@/assets/image/icon_download.png') center no-repeat;
    background-size: cover;
  }

  .del {
    background-image: url('@/assets/image/icon_cancel2.png');
    background-size: unset;
    cursor: pointer;

    &:hover {
      background-color: rgba(125, 125, 148, 0.1);
    }
  }
}
</style>