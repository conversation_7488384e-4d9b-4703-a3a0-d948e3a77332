<template>
  <span class="icon mic-icon" :class="{ 'mic-recording': recording }" @click="handleRecording">
    <div class="recwave" style="width:1px;height:1px;" />
  </span>
</template>

<script setup>
import { ref, defineProps, defineEmits, reactive, watch, onMounted, computed } from 'vue'
import { SoundRecognizer } from '@/utils/recorder/SoundRecognizer.js'
// import { audioBufferToWav } from '@/utils/recorder/audiobuffer-to-wav.js'
import { message } from 'ant-design-vue'
import { getCookie } from '@/utils/common'
import { wavUp } from '@/api'
// import { wavText } from '@/api'
const emits = defineEmits(['recordingStatus', 'socketMsg'])

let recording = ref(false)
const handleRecording = () => {
  recording.value = !recording.value
  emits('recordingStatus', recording.value)
  if (recording.value) {
    startRecording()
  } else {
    stopRecording()
  }
}

// 开始录音
let timeLoop = null
let timeCount = ref(30)
const startRecording = () => {
  console.log('开始录音')
  openWebSocket().then(res => {
    // 初始化 录音方法
    SoundRecognizer.init({
      soundType: 'wav',
      sampleRate: 16000,
      recwaveElm: '.recwave',
      translerCallBack: TransferUpload,
    });
    //内容倒计时
    let count = 180;
    timeLoop = setInterval(function () {
      count--;
      // $('#timeCount').html(count);
      timeCount.value = count
      if (count == 0) {
        clearInterval(timeLoop);
        stopRecording();
      }
    }, 1000);
  })
}


// 录音转义
const TransferUpload = (number, blobOrNull, duration, blobRec, isClose) => {
  if (blobOrNull) {
    const blob = blobOrNull;
    const encTime = blob.encTime;
    const reader = new FileReader();
    // console.log(reader.result);
    reader.onloadend = function () {
      socektObj.send(reader.result);
      console.log(reader.result)
    };
    reader.readAsArrayBuffer(blob);
  }
}
// 结束录音

const stopRecording = () => {
  console.log('结束录音')
  // 发送结束t空流给后端
  let blob = new Blob([""])
  const reader = new FileReader();
  reader.onloadend = function () {
    socektObj.send(reader.result);
  };
  reader.readAsArrayBuffer(blob);
  // socektObj.send('123');
  // socektObj.send(null);
  // socektObj.send(JSON.stringify({ recordingEnd: 1 }));
  console.log(SoundRecognizer);
  SoundRecognizer.recordEnd().then((blob) => {
    // audioBlob = blob;
    const url = (window.URL || webkitURL).createObjectURL(blob);
    console.log(url)
    let formData = new FormData();
    formData.append('file', blob); 
    // 语言完成后发送 完整音频 给后台 做记录
    wavUp(formData).then(res => {
      console.log(res)
    })
    // wavText(blob).then(res => {
    //   console.log
    // })
    // eslint-disable-next-line no-undef
    // audioDom.src = (window.URL || webkitURL).createObjectURL(blob);
  });
  clearInterval(timeLoop);
  // // 信息内容存入接口
  // saveInfo()
  // setTimeout(() => {
  //   saveInfo()
  // }, 5000)
}

// 链接socket
let socektObj = null
const openWebSocket = () => {
  return new Promise((resolve, reject) => {
    if ('WebSocket' in window) {
      if (socektObj) {
        socektObj.close();
      }
      // java-gbot-cnpgateway-prod.gwm.cn
      socektObj = new WebSocket(`wss://${ process.env.VUE_APP_TTSURL }/websocket/wav/link?${getCookie('userToken')}`)
      // https://java-gbot-cnpgateway-prod.gwm.cn/
      //取地址+端口，配置80端口时port是空的，所以判断一下
      // console.log(6)
      // let address = document.location.port ?
      //   document.location.hostname + ':' + document.location.port : document.location.hostname;
      // //拼接请求地址
      // const wsuri = "wss://" + address + `/tts-socket/websocket/wav/link?${getCookie('userToken')}`
      // console.log(wsuri)
      // socektObj = new WebSocket(wsuri) // 正式环境地址

      socektObj.onopen = function () {
        console.log(`Websocket  连接成功，开始识别`);
        resolve()
      }
      socektObj.onmessage = function (msg) {
        console.log(msg)
        // parseResult(msg.data, resultStr);
        if (msg.data.length) {
          emits('socketMsg', JSON.parse(msg.data)['text'])
        }
      }
      socektObj.onclose = function () {
        console.log(`WebSocket 连接断开`);
      }
      socektObj.onerror = function () {
        console.log(`WebSocket 连接失败`);
        message.error('语音连接失败')
      }
    } else {
      message.error('浏览器不支持websocket')
      reject('浏览器不支持websocket')
    }
  })
}
onMounted(() => {

})
</script>

<style lang="less" scoped>
.mic-recording {
  background-image: url('@/assets/image/mic_pause_icon.svg') !important;
}
</style>