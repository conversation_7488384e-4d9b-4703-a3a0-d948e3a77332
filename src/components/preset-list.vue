<template>
  <div class="preset-list layoutcss">
    <ul>
      <li @click="handleSendMsg('你能帮我做什么?')"><span><img src="@/assets/image/pre_icon.png" /></span>你能帮我做什么?</li>
      <li><span><img src="@/assets/image/pre_icon.png" /></span>我想开一个出门证怎么处理</li>
      <li><span><img src="@/assets/image/pre_icon.png" /></span>191保修服务的流程是什么</li>
      <li><span><img src="@/assets/image/pre_icon.png" /></span>我出差想申请公寓从哪里申请，需要提前多久？</li>
      <li><span><img src="@/assets/image/pre_icon.png" /></span>5========</li>
    </ul>
  </div>
</template>

<script setup>
import { ref, defineEmits } from 'vue';
const emit = defineEmits(['handleSendMsg']);

const handleSendMsg = (msg) => {
  emit('handleSendMsg', msg);
};


</script>

<style lang="less" scoped>
  .preset-list {
    // max-width: 768px;
    width: 100%;
    margin: 40px auto 0px;
  }
  ul {
    list-style-type: none;
    padding: 0; display: flex; justify-content: space-between; flex-wrap: wrap; gap: 12px;
    li {
      padding: 6px; border-radius: 8px; border: 1px solid #fff; background: rgba(255, 255, 255, 0.50); display: flex; align-items: center; min-width: 30%; max-width: 54.5%; flex: 1;
      box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
      transition: all 0.1s linear; cursor: pointer;
      &:hover {
        background: rgba(242, 242, 242, 0.675); 
        border: 1px solid rgba(255, 255, 255, 0.675);
        // transform: scale(0.98);
      }
      span{
        display: block; width: 26px; height: 26px; border-radius: 4px; overflow: hidden; margin-right: 4px;
      }
    }

  }
</style>
