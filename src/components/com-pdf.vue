<template>
  <div class="pdfView" :id="props.panelName+props.page">

  </div>
</template>

<script setup>
import * as PDF from 'pdfjs-dist';
import PdfjsWorker from "pdfjs-dist/build/pdf.worker.entry";
import {
  DefaultAnnotationLayerFactory,
  DefaultTextLayerFactory,
  PDFFindController,
  PDFLinkService,
  PDFPageView,
  EventBus,
} from "pdfjs-dist/web/pdf_viewer.js";
import { ref, onMounted, nextTick, defineEmits, defineProps, watch } from 'vue';
import Bus from "@/utils/bus";
import { hasParentWithClass } from '@/utils/common'


const props = defineProps({
  // 获取到的数据
  dataUrl: {
    default: ""
  },

  page: {
    default: "1"
  },
  panelName: {
    default:''
  },
  scaleReate: {
    type: Number,
    default: 1
  }
})

// 缩放比例变化后，重绘
watch(
  () => props.scaleReate,
  (val, oldval) => {
    // debugger
    if(val !== oldval) {
      let scale = scaleInit.value * val;
      pdfViewer.value.update(scale, 0);
      pdfViewer.value.draw()
      Bus.$emit('coordinateHighlight')
    }
    
  }
)
// PDF.GlobalWorkerOptions.workerSrc = PdfjsWorker
const pdfPointer = ref(null); // 设置选中的pdf对象
// 设置容器预览组件
const pdfViewer = ref(null);
// 缩放原始值
const scaleInit = ref(1)
 
const viewPageNum = ref(0); // 当前文件页码
const viewPageTotal = ref(0); // 当前文件总页数
const pdfviewListLoading = ref(false); // 设置当前文件加载状态
// 渲染PDF文件
const pdfLoadInit = (url) => {
  const loadingTask = PDF.getDocument({
      url: url,
      // cMapUrl: "../../cmaps/", // pdf 字体文件 解决 有些pdf文档 某些文本不显示的问题
      // cMapPacked: true
    })
  try {
    // const pdf = await loadingTask.promise;
    // console.log(`PDF 加载成功，共 ${pdf.numPages} 页`);

    loadingTask.promise.then((pdf) => {
      pdfPointer.value = pdf; // 设置pdfPointer
      renderPage(1)
    });
  } catch (error) {
    console.error('PDF 加载失败', error);
  }

  
}

// 渲染page页
const renderPage = (page) => {
  let pdfPanel = document.getElementById(props.panelName+(props.page)+'');
  // 实现页面渲染逻辑
  pdfPointer.value.getPage(page).then((page) => {
    // nextTick(() => {


    pdfViewer.value = new PDFPageView({
      container: pdfPanel,
      id: page, // 页码
      scale: 1,
      defaultViewport: page.getViewport({ scale: 1 }),
      eventBus: new EventBus(), // 事件总线
      textLayerFactory: new DefaultTextLayerFactory(), // 文本层工厂
      annotationLayerFactory: new DefaultAnnotationLayerFactory(), // 注释层工厂
    });

    // 7. 关联 PDF 页面
    pdfViewer.value.setPdfPage(page);

    // 减去15 预留滚动条位置、避免横行滚动条。
    const containerWidth = pdfPanel.clientWidth - 15;
    let containerHeight = 0
    const hasFileContentBodyParentLegacy = hasParentWithClass(pdfPanel, 'fileContentBody');
    if(hasFileContentBodyParentLegacy) {
      containerHeight = document.getElementsByClassName('fileContentBody')[0].clientHeight;
    } else {
      containerHeight = pdfPanel.clientHeight;
    }
    // const containerHeight = pdfPanel.clientHeight;
    // const parentContainerHeight = document.getElementsByClassName('fileContentBody')[0].clientHeight;

    const pageWidth = pdfViewer.value.viewport.width;
    const pageHeight = pdfViewer.value.viewport.height;

    props.panelName == 'pdfPanelThum' ?
    scaleInit.value = Math.min(containerWidth / pageWidth, containerHeight / pageHeight)
    :
    scaleInit.value = containerWidth / pageWidth;


    // console.log(pageWidth,'pageWidth')
    // console.log(pageHeight,'pageHeight')
    // console.log(containerWidth,'containerWidth')
    // console.log(containerHeight,'containerHeight')
    // console.log(scaleInit.value, 'scale')

    pdfViewer.value.update(scaleInit.value * props.scaleReate, 0);

    pdfViewer.value.draw();
    // })



    // const canvas = document.createElement('canvas');
    // const context = canvas.getContext('2d');
    // canvas.width = viewport.width;
    // canvas.height = viewport.height;
    // pdfPanel.appendChild(canvas);
    // const renderContext = {
    //   canvasContext: context,
    //   viewport
    // };
    // page.render(renderContext);
  });

}

onMounted(() => {
  if(props.dataUrl) {
    pdfLoadInit(props.dataUrl)
  }
})


</script>

<style lang="less">
.pdfView {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-around;
  align-items: start;
}
.page {
  position: relative;
}
.textLayer {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1.0;
  span {
    position: absolute;
    color: transparent;
    white-space: pre;
    cursor: text;
    transform-origin: 0% 0%;
  }
}


</style>