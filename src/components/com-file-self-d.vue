<template>
  <!-- 加判断，当robot Type为个人上传单文档助手时，显示此box -->
  <div class="filebox">
    <Spin :spinning="flLoading && !props.hisMesLoading">
      <div class="content filecontent">
        <div class="contit" @click="fbtoggle">
          已上传{{ fileList.length }}个文件
          <template v-if="JSON.stringify(preFileList) != '{}'">
           , 上传中...(1)
          </template>
          <span :class="{ statetag: true, down: !fbtag }"></span>
        </div>
        <div
          :class="{ filelist: true, boxhidden: fbtag }"
          v-if="(fileList && fileList.length > 0) || JSON.stringify(preFileList) != '{}' "
        >
          <div class="fileitem" :class="{'cur': item.fileId == fileid}" v-for="(item, idx) in fileList" :key="idx" @click="handleFileClick(item)">
            <span class="filetag"
              ><img src="@/assets/image/pdficon.png"
            /></span>
            <div class="filename">
              <span>{{ item.fileName }}</span>
              <!-- <span class="kbspan">{{ sizeTostr(item.size) }}</span> -->
            </div>
            <div class="controlbox">
              <span class="success">已上传</span>
              <!-- <span class="preview" @click="previewDoc(item)">预览</span> -->
              <Spin size="small" :spinning="cancelLoading && idx == curidx">
                <Popconfirm
                  title="确定删除此文件吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="toFileDelete(item, idx)"
                  @cancel="cancel"
                >
                  <span class="cancel">删除</span>
                </Popconfirm>
              </Spin>
            </div>
          </div>
          <!-- 上传中的文件添加进度列表展上 -->
          <div class="fileitem" v-if="JSON.stringify(preFileList) != '{}'">
            <span class="filetag"
              ><img src="@/assets/image/pdficon.png"
            /></span>
            <div class="filename">
              <span>{{ preFileList.name }}</span>
              <!-- <span class="kbspan">{{ sizeTostr(item.size) }}</span> -->
            </div>
            <Progress style="width: 110px; margin-right: 10px;" :percent="Math.floor((preFileList.upsize/preFileList.size)*100)" size="small" />
            <div class="controlbox">
              <span class="progressing"><Spin />上传中</span>
            </div>
          </div>
        </div>
      </div>
    </Spin>
  </div>
</template>

<script setup>
import {
  ref,
  defineProps,
  defineEmits,
  reactive,
  watch,
  onMounted,
  computed,
  nextTick,
  onUnmounted,
} from "vue";
import { LoadingOutlined, FilePdfOutlined } from "@ant-design/icons-vue";
import "@/assets/css/dark.css";
import {
  message,
  Avatar,
  Image,
  Spin,
  Tooltip,
  Popconfirm,
  Progress
} from "ant-design-vue";
import { ordinaryMessage, fileDelete } from "@/api";

import Bus from "@/utils/bus";
// console.log(ClipboardJS)
const props = defineProps({
  messageId: Number,
  robotobj: Object,
  hisMesLoading: Boolean,
});
watch(
  () => props.msgLoading,
  (val) => {
    isGenerating.value = val;
  }
);

const fbtag = ref(true);
const fbtoggle = () => {
  fbtag.value = !fbtag.value;
};

/**
 * 用户上传的文件列表相关代码
 */
const fileid = ref()

const fileList = ref([]);
const flLoading = ref(false);
const getFileList = () => {
  flLoading.value = true;
  const param = {
    messageId: props.messageId,
  };
  ordinaryMessage(param)
    .then((res) => {
      if (res.ret == 200) {
        flLoading.value = false;
        fileList.value = res.data.documentList;
        const len = fileList.value.length
        fileid.value = fileList.value[len-1].fileId
        Bus.$emit('fileid',fileid.value)
        Bus.$emit('filenum',fileList.value.length)
      } else {
        flLoading.value = false;
        fileList.value = [];
        Bus.$emit('fileid', '');
      }
    })
    .catch((e) => {
      flLoading.value = false;
      fileList.value = [];
      Bus.$emit('fileid', '');
    });
};
const handleFileClick = (item) => {
  fileid.value = item.fileId;
  Bus.$emit('fileid', fileid.value);
}
watch(
  () => props.messageId,
  (messageId, oldmessageId) => {
    // 获取用户上传的文件列表
    // 修复immediate为true后，点击到助手中心后的问题
    // console.log(oldmessageId, document.getElementsByClassName('init').length,"=====",props.robotobj.)
    // if((!oldmessageId && document.getElementsByClassName('init').length==1)) {
    //   console.log('11111');
    //   return false
    // }
    // console.log(messageId,'messageId')
    getFileList();
    clearTimeout(presentTimeOut.value)
    preFileList.value = {}
  }
  // { immediate: true }
);
// 删除文件
const cancelLoading = ref(false);
const curidx = ref();
const toFileDelete = (item, idx) => {
  cancelLoading.value = true;
  curidx.value = idx;
  let param = {
    fileId: item.fileId,
    messageId: item.messageId,
  };
  fileDelete(param)
    .then((res) => {
      if (res.ret == 200) {
        cancelLoading.value = false;
        message.success("删除成功！");
        getFileList();
      } else {
        // message.error("删除失败");
        cancelLoading.value = false;
      }
      // console.log(currentDocId,item.robotDetailId)
      Bus.$emit('swapDoc', item.robotDetailId * 1) // 如果删除了当前文档则关闭doc
    })
    .catch((e) => {
      // message.error("删除失败");
      cancelLoading.value = false;
    });
};
const cancel = () => {};

const preFileList = ref({}); // 预上传文件列表-假进度条

// 文件大小转换
const sizeTostr = (size) => {
  let data = "";
  if (size < 0.5 * 1024) {
    //如果小于0.5KB转化成B
    data = size.toFixed(2) + "B";
  } else if (size < 0.5 * 1024 * 1024) {
    //如果小于0.5MB转化成KB
    data = (size / 1024).toFixed(2) + "KB";
  } else if (size < 0.5 * 1024 * 1024 * 1024) {
    //如果小于0.5GB转化成MB
    data = (size / (1024 * 1024)).toFixed(2) + "MB";
  } else {
    //其他转化成GB
    data = (size / (1024 * 1024 * 1024)).toFixed(2) + "GB";
  }
  let sizestr = data + "";
  let len = sizestr.indexOf("\.");
  let dec = sizestr.substr(len + 1, 2);
  if (dec == "00") {
    //当小数点后为 00 时 去掉小数部分
    return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
  }
  return sizestr;
};

// let currentDocId = null // 记录当前 展示doc
const previewDoc = (item) => {
  // currentDocId = item.robotDetailId
  // Bus.$emit('file_preview',item)
  item.area = [0,0,0,0,1,841,595]
  item.ele = {}
  item.ele.id = item.robotDetailId
  Bus.$emit("annotationList", item)
}

const presentTimeOut = ref(null)
const presentSet = () => {
  presentTimeOut.value = setTimeout(() => {
    preFileList.value.upsize += 100000
    // console.log((preFileList.value.upsize/preFileList.value.size).toFixed(2),'file_item.value.upsize')
    if(preFileList.value.upsize >= preFileList.value.size*0.9) {
      preFileList.value.upsize = preFileList.value.size*0.9
      return
    }
    presentSet()
  }, 100);
}

onMounted(() => {
  console.log(props.messageId,'props.messageId')
  Bus.$on("fileChatData", (filestate) => {
    clearTimeout(presentTimeOut.value)
    if (filestate == "success") {
      preFileList.value = {} // 将预加载的列表设为空
      message.success("上传成功！");
      getFileList();
    } else {
      preFileList.value.status = 'failed'
      // message.error("上传失败," + filestate);
    }
  });
  
  Bus.$on("preFile", (preFile => {
    preFileList.value.name = preFile.name
    preFileList.value.size = preFile.size
    preFileList.value.status = 'uploading'
    preFileList.value.upsize = 0
    presentSet()
  })
  )
});
onUnmounted(() => {
  Bus.$off("fileChatData");
  Bus.$off("preFile");
});
</script>

<style lang="less" scoped>
.filebox {
  position: fixed;
  top: -57px;
  left: 0px;
  width: 100%;
  z-index: 99;
  .filecontent {
    box-shadow: 3px 3px 5px #00000015;
    border-radius: 5px;
    background-color: #e9f3ff;
    color: #3656ff;
    position: relative;
    transition: all 0.3s ease-out;
    padding: 8px 30px;
    text-align: left;
    .contit {
      cursor: pointer;
      font-weight: 500;
      padding: 5px 0px;
    }
    .statetag {
      position: absolute;
      border-bottom: 8px solid #35353e;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-radius: 2px;
      top: 20px;
      right: 20px;
      cursor: pointer;
      transition: all 0.3s ease-out;
      &.down {
        transform: rotate(180deg);
      }
    }
    .filelist {
      background-color: #fff;
      border: 1px dashed #3656ff;
      padding: 10px;
      border-radius: 8px;
      margin-top: 12px;
      transition: all 0.2s ease-out;
      max-height: 262px;
      overflow-y: auto;
      .fileitem {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 10px;
        border-radius: 5px;
        cursor: pointer;
        .filetag {
          width: 24px;
          height: 24px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .filename {
          color: #000;
          flex: 1;
          text-align: left;
          display: block;
          padding: 0px 5px;
          .kbspan {
            color: #888;
            font-size: 14px;
            padding-left: 15px;
          }
        }
        /deep/.ant-progress-inner {
          background-color: #dcdcdc;
        }

        .controlbox {
          display: flex;
          width: 115px;
          justify-content: space-between;
          /deep/.ant-spin-dot-spin {
            width: 18px; height: 18px;
          }
          span.preview {
            padding-right: 12px;
            padding-left: 12px;
            line-height: 22px;
            cursor: pointer;
            position: relative;
            &:hover {
              font-size: 16px;
              padding-left: 10px;
              padding-right: 10px;
            }
            &::after {
              content: "";
              height: 12px;
              width: 1px;
              position: absolute;
              top: 50%;
              left: 0px;
              background-color: #ccc;
              margin-top: -6px;
            }
          }
          span.success {
            color: #000;
            padding-right: 12px;
            padding-left: 20px;
            background: url("@/assets/image/check-circle.svg") left center
              no-repeat;
          }
          span.cancel {
            padding-left: 12px;
            line-height: 22px;
            color: #d10000;
            position: relative;
            cursor: pointer;
            transition: all 0.1s ease-out;
            &:hover {
              font-size: 16px;
              padding-left: 8px;
            }
            &::after {
              content: "";
              height: 12px;
              width: 1px;
              position: absolute;
              top: 50%;
              left: 0px;
              background-color: #ccc;
              margin-top: -6px;
            }
          }
        }
        &:hover, &.cur {
          background: #f0f4ff;
        }
      }
    }
    .boxhidden {
      height: 0px;
      padding: 0px;
      border: 0px;
      overflow: hidden;
      margin: 0px;
    }
  }
}
</style>
