<!-- 来源组件 -->
<template>
  <div class="source-box">
    <div class="sourcebtn" @click="sourceShow(item)" >
      <span class="dssvg"></span>
      <span class="txt">引用来源</span>
      <span class="arrow" :class="{'arrow-down': item.sourceShow}" ></span>
    </div>
    <div class="source-content" :class="{'source-hide': item.sourceShow}">
      <!-- <vue-markdown :source="itemText || '很抱歉，我无法帮到您 ~~'" :breaks="true" :html="true" class="md-wrap"
      :class="`md-${idx}`" /> -->
      <div
        v-for="(item, idx) in sourceObj"
        :key="idx"
        class="source-item"
      >
       {{ '['+ item.chunk_id +']' }} {{ item.file_name }} -- {{ item.file_number }}
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, defineProps, computed } from 'vue'
import VueMarkdown from "@/components/markdown/VueMarkdown.js";


const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  idx: {
    type: Number,
    required: true
  }
})

const item = ref(props.item)
item.value.sourceShow = true
const sourceObj = computed(() => {
  // let txtMd = ''
  return item.value.sourceDataText.content || []
  // for(let i=0;i<sourceObj.length;i++) {
  //   for (const key in sourceObj[i]) {
  //     if (sourceObj[i].hasOwnProperty(key)) { // 确保是对象自身的属性，而不是继承的属性
  //       txtMd += sourceObj[i][key]
  //     }
  //   }
  // }
  // return sourceObj;
})


const sourceShow = (item) => {
  item.sourceShow = !item.sourceShow
}


</script>
<style lang="less" scoped>
  .source-box {
    display: flex; flex-direction: column; align-items: start; padding-bottom: 10px;
    .sourcebtn {
      display: flex; align-items: center; cursor: pointer; background-color: #d4dbe4; padding: 10px 8px; border-radius: 12px; background: #EEF0FC; margin-top: 8px;
      .dssvg{
        background: url('@/assets/image/sourcelink.svg') no-repeat;
        background-position: top center;
        margin-right: 0px;
        transition: all .3s linear;
        width: 15px; height: 15px; display: inline-block;
        background-size: 15px 15px;
      }
      .txt{ 
        color: #60646C;
        font-size: 14px;
        font-weight: 400;
        line-height: 16px;
        padding: 0px 8px;
      }
      .arrow {
        background: url('@/assets/image/history_up.svg') no-repeat;
        background-position: center center;
        margin-right: 0px;
        transition: all .2s linear;
        width: 15px; height: 15px; display: inline-block;
        opacity: 0.5;
        &.arrow-down {
          transform: rotate(180deg);
        }
      }
      &:hover {
        background-color: #dbddeb;
      }
    }
    .source-content{
      padding: 8px 10px; 
      background-color: #F0F0F3; 
      transition: all .2s linear;
      border-radius: 10px; width: 100%;
      margin-top: 10px;
      &.source-hide{
        height: 0px; padding: 0 0 0 10px; overflow: hidden;
      }
      .source-item {
        padding: 5px 0px; color: #1C2024; padding:6px 10px; 
      }
    }
  }
</style>