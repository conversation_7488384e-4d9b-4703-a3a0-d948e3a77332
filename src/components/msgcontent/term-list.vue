<!-- 术语查询助手 -->
<template>
  <div class="termlist">
    <div
      class="termItem"
      v-if="props.termlist.length > 0"
      v-for="(item, index) in props.termlist"
      :key="index"
    >
      <div class="topbox">
        <div class="titlebox">
          <div class="cnwarp">
            <span class="cnTitle">{{ item.termCn }}</span>
            <span class="timespan">{{ timerChange(item.updateTime) }} 更新</span>
          </div>
          <span class="enTitle">{{ item.termEn }}</span>
        </div>
        <a href="javascript:;" @click="linkSkip(item)">详情</a>
      </div>
      <div class="definebox">
        {{ item.termDefinition }}
      </div>
      <!-- <div class="bottombox">
        <span>{{ timerChange(item.updateTime) }} 更新</span>
      </div> -->
    </div>
    <div v-else>未查询到相关术语...</div>
  </div>
</template>
<script setup>
import { ref, onMounted, defineProps } from "vue";
import { formatDateToYYMMDDHHMM } from "@/utils/common";

const props = defineProps({
  termlist: Array,
});

const timerChange = (date) => {
  return formatDateToYYMMDDHHMM(date);
};

const linkSkip = (item) => {
  window.open("https://shuyu-gqdp.gwm.cn/TermDetail?termId=" + item.id+"");
};
</script>
<style lang="less" scoped>
.termItem {
  padding: 16px;
  border-radius: 12px;
  background: linear-gradient(0deg, #FFF 0%, #EBEFFF 100%);
  // background: #ebefff91;
  margin-bottom: 8px;
  .topbox {
    display: flex;
    justify-content: space-between;
    a {
      flex-shrink: 0;
      font-size: 12px; 
      line-height: 24px; 
      color: #364AFD; 
      border-radius: 16px;
      border: 1px solid #FFF;
      background: rgba(255, 255, 255, 0.50);
      box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
      backdrop-filter: blur(4.300000190734863px);
      width: 50px;
      height: 24px;
      text-align: center;
      &:hover {
        background: rgb(236 236 236 / 50%);
        border: 1px solid rgba(255, 255, 255, 0.5);
      }
    }
  }
  &:last-child {
    border: none;
    margin-bottom: 0px;
  }
}
.titlebox {
  display: flex;
  flex: 1;
  margin-right: 10px;
  flex-direction: column;
  .cnwarp {
    // margin-right: 8px;
    display: flex;
    line-height: 24px;

    .cnTitle {
      word-break: keep-all;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1; /* 限制文本显示的行数为 3 行 */
      text-overflow: ellipsis;
      color: #1C2024;
      font-size: 16px;
      font-weight: 700;
      padding-right: 10px;
      flex: 1;
    }

    .timespan {
      color: #8B8D98;
      font-size: 12px;
      font-weight: 400;
    }
  }
  .enTitle {
    word-break: keep-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1; /* 限制文本显示的行数为 3 行 */
    text-overflow: ellipsis;
    font-size: 12px;
    color: #60646C;
    font-weight: 400;
    line-height: 16px;
    margin-top: -3px;
  }
}
.definebox {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  flex: 1;
  color: #1C2024;
  font-size: 12px;
  font-weight: 400;
  margin-top: 10px;
}
.bottombox {
  display: flex;
  justify-content: flex-end;
}
</style>
