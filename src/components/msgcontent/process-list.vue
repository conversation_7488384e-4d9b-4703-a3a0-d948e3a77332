<!-- 流程查询助手 -->

<template>
  <div class="processlist">
    <div
      class="processItem"
      v-if="props.processlist.length > 0"
      v-for="(item, index) in props.processlist"
      :key="index"
    >
      <div class="topbox">
        <div class="titlebox">
          <div class="cnwarp">
            <span class="cnTitle"> {{ item.name }} </span>
            <!-- <span class="timespan">{{ timerChange(item.updateTime) }} 更新</span> -->
          </div>
          <div class="processUrl">
            <span>流程中心: </span>
            <a :title="item.url"> {{ item.url }} </a>
          </div>
        </div>
        <a class="skipbtn" href="javascript:;" @click="linkSkip(item)">发起流程</a>
      </div>
      <!-- <div class="definebox">
        流程中心: https://bpm.gwm.cn/devops/processCall/jumpUrlurl=https%3A%2F%2Fbpm.gwm.cn
      </div> -->
      <!-- <div class="bottombox">
        <span>{{ timerChange(item.updateTime) }} 更新</span>
      </div> -->
    </div>
    <div v-else>未查询到相关流程...</div>
    <!-- <div class="topbox">
      <div class="titlebox">
        <div class="cnwarp">
          <span class="cnTitle"> 9001文件签批流程 </span>
        </div>
        <div class="processUrl"><span>流程中心: </span><a href="https://bpm.gwm.cn/devops/processCall/jumpUrlurl=https%3A%2F%2Fbpm.gwm.cn">https://bpm.gwm.cn/devops/processCall/jumpUrlurl=https%3A%2F%2Fbpm.gwm.cn</a></div>
      </div>
      <a class="skipbtn" href="javascript:;" @click="linkSkip(item)">发起流程</a>
    </div> -->
    <!-- <div class="topbox">
      <div class="titlebox">
        <div class="cnwarp">
          <span class="cnTitle"> 9001文件签批流程 </span>
        </div>
        <div class="processUrl"><span>流程中心: </span><a href="https://bpm.gwm.cn/devops/processCall/jumpUrlurl=https%3A%2F%2Fbpm.gwm.cn">https://bpm.gwm.cn/devops/processCall/jumpUrlurl=https%3A%2F%2Fbpm.gwm.cnhttps://bpm.gwm.cn/devops/processCall/jumpUrlurl=https%3A%2F%2Fbpm.gwm.cn</a></div>
      </div>
      <a class="skipbtn" href="javascript:;" @click="linkSkip(item)">发起流程</a>
    </div> -->
  </div>
</template>
<script setup>
import { ref, onMounted, defineProps } from "vue";
import { formatDateToYYMMDDHHMM } from "@/utils/common";

const props = defineProps({
  processlist: Array,
});

const timerChange = (date) => {
  return formatDateToYYMMDDHHMM(date);
};

const linkSkip = (item) => {
  window.open(item.url, '_blank');
};
</script>
<style lang="less" scoped>
.processItem {
  padding: 16px;
  border-radius: 12px;
  background: linear-gradient(0deg, #FFF 0%, #EBEFFF 100%);
  // background: #ebefff91;
  margin-bottom: 8px;
  .topbox {
    display: flex;
    justify-content: space-between;
    a.skipbtn {
      flex-shrink: 0;
      font-size: 12px; 
      line-height: 22px; 
      color: #364AFD; 
      border-radius: 16px;
      border: 1px solid #FFF;
      background: rgba(255, 255, 255, 0.50);
      box-shadow: 0px 4px 29.2px 0px rgba(67, 72, 169, 0.13);
      backdrop-filter: blur(4.300000190734863px);
      // width: 50px;
      padding: 0 14px;
      height: 24px;
      text-align: center;
      align-self: center;
      &:hover {
        background: rgb(236 236 236 / 50%);
        // border: 1px solid rgba(59, 48, 48, 0.5);
      }
    }
  }
  &:last-child {
    border: none;
    margin-bottom: 0px;
  }
}
.titlebox {
  display: flex;
  flex: 1;
  margin-right: 10px;
  flex-direction: column;
  max-width: calc(100% - 80px);
  .cnwarp {
    // margin-right: 8px;
    display: flex;
    line-height: 24px;

    .cnTitle {
      word-break: keep-all;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1; /* 限制文本显示的行数为 3 行 */
      text-overflow: ellipsis;
      color: #1C2024;
      font-size: 16px;
      font-weight: 700;
      padding-right: 10px;
      flex: 1;
    }

    .timespan {
      color: #8B8D98;
      font-size: 12px;
      font-weight: 400;
    }
  }
  .processUrl {
    display: flex;
    font-size: 12px;
    color: #60646C;
    font-weight: 400;
    line-height: 16px;
    // margin-top: -3px;
    span {
      flex-shrink: 0;
      padding-right: 5px;
    }
    a {
      // color: #364AFD;
      // word-break: keep-all;
      // overflow: hidden;
      // display: -webkit-box;
      // -webkit-box-orient: vertical;
      // -webkit-line-clamp: 1; /* 限制文本显示的行数为 3 行 */
      // text-overflow: ellipsis;
      // cursor: auto;
      // white-space: nowrap;
      color: #364AFD;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block; /* 或 block */
      max-width: 100%; /* 或设置具体宽度，如 200px */
      cursor: auto;
    }
  }
}
.definebox {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  flex: 1;
  color: #1C2024;
  font-size: 12px;
  font-weight: 400;
  margin-top: 10px;
}
.bottombox {
  display: flex;
  justify-content: flex-end;
}
</style>
