<template>
        <!-- 猜你想问模块 -->
        <skeleton v-if="recommendListLoading" :title="false"  :paragraph="{ rows: 3, width: '300px' }" active />
        <div class="guess-box" v-else-if="guessList.length>0">
          <!-- <div class="guess-title">猜你想问</div> -->
          <div class="guess-list">
            <div class="guess-item" v-for="(item, index) in guessList" :key="index" @click="guessClick(item)">
              <!-- {{ index+1 }}. -->
              {{ item }}
            </div>
          </div>
        </div>

</template>
<script setup>
import {
  ref,
  defineProps,
  defineEmits,
  reactive,
  watch,
  onMounted,
  computed,
  nextTick,
} from "vue";
import { Skeleton } from 'ant-design-vue';

const props = defineProps({
  recommendList: {
    type: Array,
    default: () => [],
  },
  recommendListLoading: {
    type: Boolean,
    default: false,
  },
});
const guessList = ref(props.recommendList);
watch(
  () => props.recommendList,
  (newVal) => {
    guessList.value = newVal;
  }
);
// 加载状态的骨架屏
const recommendListLoading = ref(props.recommendListLoading);
watch(
  () => props.recommendListLoading,
  (newVal) => {
    recommendListLoading.value = newVal;
  }
);
const emits = defineEmits(["sendMsg"]);
const guessClick = (item) => {
  emits("sendMsg", item);
};



</script>
<style lang="less" scoped>
  :deep.ant-skeleton { width: 350px !important; margin-top: -20px; 
    margin-left: 52px;
    .ant-skeleton-content .ant-skeleton-paragraph > li {
      height: 25px;
    }
    .ant-skeleton-content .ant-skeleton-paragraph > li + li {
      margin-top: 10px;
    }
  }
  .guess-box{
    display: flex;
    flex-direction: column;
    text-align: left;
    margin: -30px 5px 0px 52px;
    padding: 0px 30px 14px 0px;
    align-items: start;
    position: absolute;
    .guess-list {
      // background-color: #f1f4ff;
      padding: 10px 0px;
      border-radius: 10px;
      min-width: 300px;
      .guess-item {
        margin-bottom: 5px;
        cursor: pointer;
        transition: all 0.1s ease-in-out;
        display: table;
        // border-bottom: 1px solid #f1f4ff;
        line-height: 18px;
        padding: 8px 12px;
        background-color: rgba(0,0,0,.04);
        border-radius: 5px;
        &:hover {
          // color: #5317fd;
          background-color: rgba(0, 0, 0, 0.1);
        }
        &:last-child { margin-bottom: 0px; }
      }
    }
  }

</style>