<!-- 联网检索 -->
<template>
  <div class="net-box" v-if=" item.webSearchText !== null && JSON.stringify(item.webSearchText) !== '{}' ">
    <div class="netbtn" @click="netShow(item)" >
      <span class="dssvg"></span><span class="txt">已搜索到{{ item.webSearchText.list.length }}条相关网页</span><span class="arrow" :class="{'arrow-down': item.netShow}" ></span>
    </div>
    <div class="net-content" :class="{'net-hide': item.netShow}">
      <!-- <vue-markdown :source="item.netResult  || '很抱歉，我无法帮到您 ~~'" :breaks="true" :html="true" class="md-wrap"
      :class="`md-${idx}`" /> -->
      <div v-if="item.webSearchText" v-for="(result, index) in item.webSearchText.list" :key="index" class="result-item">
        <a :href="result.url" target="_blank" class="result-link">{{ result.title || result.content.substring(0, 60)+'...' || '--' }}</a>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, defineProps  } from 'vue'
import Bus from '@/utils/bus'


const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  idx: {
    type: Number,
    required: true
  }
})
const item = ref(props.item)
item.value.netShow = true
// console.log(item.webSearchText)
const netShow = (item) => {
  // item.netShow = !item.netShow
  Bus.$emit('expand', item)
}

</script>

<style lang="less" scoped>
  .net-box {
    display: flex; flex-direction: column; align-items: start; padding-bottom: 10px;
    .netbtn {
      // display: flex; align-items: center; border-radius: 5px; border: 1px solid #ccd4fc; padding: 0px 5px; cursor: pointer; background-color: #d4dbe4;
      display: flex; 
      align-items: center; 
      border-radius: 12px; 
      padding: 16px; 
      font-size: 16px;
      cursor: pointer;
      background: linear-gradient(348deg, #FFF -396.51%, #EBEFFF 69.11%);
      width: 100%;
      .dssvg{
        background: url('@/assets/image/netSure.svg') no-repeat;
        background-position: top center;
        margin-right: 0px;
        transition: all .3s ease-in-out;
        width: 15px; height: 15px; display: inline-block;
        background-size: 15px 15px;
      }
      .txt{ padding: 0 8px; flex: 1; }
      .arrow {
        background: url('@/assets/image/arrow.svg') no-repeat;
        background-position: center center;
        margin-right: 0px;
        transition: all .2s ease-in-out;
        width: 15px; height: 15px; display: inline-block;
        &.arrow-down {
          transform: rotate(180deg);
        }
      }
    }
    .net-content{
      margin: 10px 0px 0px;
      color: #8d8d8d;
      transition: all 0.1s ease-in-out;
      background-color: #fff;
      padding: 10px 0px;
      width: 100%;
      border-radius: 15px;
      &.net-hide{
        height: 0px; padding: 0px; margin:0px; overflow: hidden;
      }
      .result-item {
        margin:0px 10px;
        padding: 5px 0px;
        a:hover{ text-decoration: underline; }
      }
    }
  }

</style>