<!-- AI阅读中 来源组件 -->
<template>
  <div class="source-box">
    <div class="sourcebtn" @click="sourceShow(item)" >
      <span class="dssvg"></span>
      <span class="txt">相关片段</span>
      <span class="arrow" :class="{'arrow-down': item.sourceShow}" ></span>
    </div>
    <div class="source-content" :class="{'source-hide': item.sourceShow}">
      <!-- <vue-markdown :source="itemText || '很抱歉，我无法帮到您 ~~'" :breaks="true" :html="true" class="md-wrap"
      :class="`md-${idx}`" /> -->
      <a
        v-for="(item, idx) in sourceObj"
        :key="idx"
        class="coordinate_item"
        @click="coordinateHighlight($event,item, idx)"
        :class="{coord_cur: idx == currentIdx}"
      >
        【{{ idx+1 }}】
        <!-- <span @click="coordinateHighlight()">片段 {{ idx }}</span> -->
       <!-- <span v-for="(item, idx) in item"> {{ item.page }} </span> -->
      </a>
      <div class="pagenum">
        <p>关联页码:</p>
        <span v-for="(item,i) in sourceObj[currentIdx]" :key="i"> {{ item.page }} </span>
      </div>

    </div>
  </div>
</template>
<script setup>
import { ref, defineProps, computed, onMounted } from 'vue'
import VueMarkdown from "@/components/markdown/VueMarkdown.js";
import Bus from "@/utils/bus";
import { viewPanel } from '@/utils/common.js'



const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  // idx: {
  //   type: Number,
  //   required: true
  // }
})

const item = ref(props.item)
item.value.sourceShow = false
const sourceObj = computed(() => {
  // let txtMd = ''

  return item.value.sourceDataText.content || []
  // for(let i=0;i<sourceObj.length;i++) {
  //   for (const key in sourceObj[i]) {
  //     if (sourceObj[i].hasOwnProperty(key)) { // 确保是对象自身的属性，而不是继承的属性
  //       txtMd += sourceObj[i][key]
  //     }
  //   }
  // }
  // return sourceObj;
})


const sourceShow = (item) => {
  item.sourceShow = !item.sourceShow
}

const currentIdx = ref()
// 需要高亮的片段组合
const coordinateHighlight = (event, item, idx) => {
  const targetEl = event.target.closest('.coordinate_item');
  if (!targetEl) return;

  if (targetEl.classList.contains('coord_cur')) {
    let pageDiv = document.getElementById('pdfPanelView-'+item[0].page)
    viewPanel(pageDiv, 'auto')
    return; // 已选中，不执行操作
  }

  Array.from(document.querySelectorAll('.coord_cur')).forEach(function(element) {
    element.classList.remove('coord_cur');
  });
  Array.from(document.querySelectorAll('.showpanel')).forEach(function(element) {
    element.classList.remove('showpanel');
  });

  // 给父元素添加 showpanel 类
  const parentEl = targetEl.parentNode; // 获取父元素
  if (parentEl) { // 安全校验：确保父元素存在
    parentEl.classList.add('showpanel'); // 添加类名
  }

  currentIdx.value = idx
  Bus.$emit('coordinateHighlight', item)
}

</script>
<style lang="less" scoped>
  .source-box {
    display: flex; flex-direction: column; align-items: start; padding-bottom: 10px;
    .sourcebtn {
      display: flex; align-items: center; cursor: pointer; background-color: #d4dbe4; padding: 6px 8px; border-radius: 6px; background: #EEF0FC; margin-top: 8px;
      .dssvg{
        background: url('@/assets/image/sourcelink.svg') no-repeat;
        background-position: top center;
        margin-right: 0px;
        transition: all .3s linear;
        width: 15px; height: 15px; display: inline-block;
        background-size: 15px 15px;
      }
      .txt{ 
        color: #60646C;
        font-size: 14px;
        font-weight: 400;
        line-height: 16px;
        padding: 0px 8px;
      }
      .arrow {
        background: url('@/assets/image/history_up.svg') no-repeat;
        background-position: center center;
        margin-right: 0px;
        transition: all .2s linear;
        width: 15px; height: 15px; display: inline-block;
        opacity: 0.5;
        &.arrow-down {
          transform: rotate(180deg);
        }
      }
      &:hover {
        background-color: #dbddeb;
      }
    }
    .source-content{
      padding: 8px 10px; 
      background-color: #f0f0f387; 
      transition: all .2s linear;
      border-radius: 6px; width: 100%;
      margin-top: 10px;
      &.source-hide{
        height: 0px; padding: 0 0 0 10px; overflow: hidden;
      }
      .source-item {
        padding: 5px 0px; color: #1C2024; padding:6px 10px; 
        span {
          padding: 0px 8px;
        }
      }
      .coordinate_item {
        display: inline-block; color: #000;
        &.coord_cur {
          color: #3133ff;
        }
      }
      .pagenum {
        background: #fff;
        padding: 10px;
        margin-top: 5px;
        border-radius: 8px;
        color: #919191;
        display: none;
        p {
          
        }
        span {
          background-color: #fff; display: inline-block; margin-right: 5px; padding: 0px 5px; border-radius: 3px;
        }
      }
    }
    // 当前选择的片段  页码显示
    .showpanel .pagenum {
      display: block;
    }

  }
</style>