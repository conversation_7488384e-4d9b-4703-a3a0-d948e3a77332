<!-- 思考线组件 -->
<template>
  <div class="think-box" v-if="item.reasoningText">
    <div class="thinkHeader" @click="thinkShow(item)">
      <span class="txt" style="display: none">正在思考...</span>
      <span class="txt">已深度思考</span>
      <span class="arrow" :class="{ 'arrow-down': item.thinkShow }"></span>
    </div>
    <div class="think-content" :class="{ 'think-hide': item.thinkShow }">
      <vue-markdown
        :source="item.reasoningText || '很抱歉，我无法帮到您 ~~'"
        :breaks="true"
        :html="true"
        class="md-wrap"
        :class="`md-${idx}`"
      />
    </div>
  </div>
</template>
<script setup>
import { ref, defineProps } from "vue";
import VueMarkdown from "@/components/markdown/VueMarkdown.js";

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  idx: {
    type: Number,
    required: true,
  }, // Add idx prop here if it's used in the component. Otherwise, remove it.
});
const item = ref(props.item);

const thinkShow = (item) => {
  item.thinkShow = !item.thinkShow;
};
</script>
<style lang="less" scoped>
.think-box {
  display: flex;
  flex-direction: column;
  align-items: start;
  padding: 0px 16px 16px;
  align-items: flex-start;
  align-self: stretch;
  margin-bottom: 8px;
  border-radius: 12px;
  background: linear-gradient(0deg, #FFF 0%, #EBEFFF 100%);
  .thinkHeader {
    display: flex;
    align-items: center;
    border-radius: 5px;
    padding: 16px 0px 0px;
    cursor: pointer;
    justify-content: space-between;
    width: 100%;

    .txt {
      padding: 0 5px;
      font-size: 16px;
    }
    .arrow {
      background: url("@/assets/image/arrow.svg") no-repeat;
      background-position: center center;
      margin-right: 0px;
      transition: all 0.2s ease-in-out;
      width: 15px;
      height: 15px;
      display: inline-block;
      &.arrow-down {
        transform: rotate(180deg);
      }
    }
  }
  .think-content {
    padding: 0px 0px 0px 10px;
    color: #8d8d8d;
    transition: all 0.2s linear;
    border-left: 1px solid #B9BBC6;
    margin: 10px 0px 5px 8px;
    &.think-hide {
      height: 0px;
      padding: 0 0 0 10px;
      margin: 0px 0px 0px 8px;
      overflow: hidden;
    }
  }
}
</style>
