<!-- 流程查询助手 -->

<template>
  <div class="answerlist" v-if="props.answerlist.content.content">
    <div class="answerTit">
      你是不是想问：
    </div>
    <div
      class="answerItem"
      v-for="(item, idx) in props.answerlist.content.content"
      :key="idx"
    >
      <div class="topbox">
        <div class="titlebox" @click="answersFn(item)">
          {{ item.question }}
        </div>
      </div>
    </div>
    <!-- <div
      class="answerItem"
    >
      <div class="topbox">
        <div class="titlebox">
          为什么液压保护故障？
        </div>
      </div>
    </div>
    <div
      class="answerItem"
    >
      <div class="topbox">
        <div class="titlebox">
          为什么一车液压保护故障？
        </div>
      </div>
    </div>
    <div
      class="answerItem"
    >
      <div class="topbox">
        <div class="titlebox">
          为什么A2液压保护故障？
        </div>
      </div>
    </div>
    <div
      class="answerItem"
    >
      <div class="topbox">
        <div class="titlebox">
          为什么压力机报警液压保护异常？
        </div>
      </div>
    </div>
    <div
      class="answerItem"
    >
      <div class="topbox">
        <div class="titlebox">
          为什么液压保护故障？
        </div>
      </div>
    </div> -->
  </div>

</template>
<script setup>
import { ref, onMounted, defineProps, defineEmits } from "vue";

const props = defineProps({
  answerlist: Object
});

const emit = defineEmits(['answerClick'])

const answersFn = (item) => {
  // console.log(item, 'item...')
  item.listId = props.answerlist.content.listId
  emit('answerClick', item)
}

onMounted(() => {
  console.log(props.answerlist.content, 'answerlist')
})


</script>

<style lang="less" scoped>
.answerlist {
  .answerTit {
    // color: #364AFD;
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 8px;
  }
}
.answerItem {
  color: #364AFD;
  margin-bottom: 8px;
  cursor: pointer;
}



</style>
