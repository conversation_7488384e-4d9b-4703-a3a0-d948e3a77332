import { createApp } from 'vue'
import App from './App.vue'
// import './registerServiceWorker'
import router from './router'
import store from './store'
import { Button, Input, Icon, Message, Tabs } from 'ant-design-vue'
import '@/assets/css/global.less'
import '@/themes/dark.less'
createApp(App).use(store).use(Button).use(Input).use(Message).use(Icon).use(Tabs).use(router).mount('#app')

console.log(
  '%c %s %c %s',
  'padding: 2px 5px;color: white;background-color: green;font-size:12px;',
  process.env.VUE_APP_ENVTXT,
  'padding: 2px 5px;color: white;border-left: none;background-color: skyblue',
  `版本：V1.3`
);

// 获取当前主题样式
const storageDark = localStorage.getItem('darkTheme');
if(storageDark === 'dark') {
  document.getElementById('app').setAttribute('class', 'dark-theme');  
} else {
  document.getElementById('app').setAttribute('class', '');  
}

// 取消serviceWorkder 注册事件
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(let registration of registrations) {
      registration.unregister();
    }
  })
}
