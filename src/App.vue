<template>
  <router-view/>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, reactive, onBeforeUnmount, onUnmounted, computed } from 'vue'
// import client from 'webpack-theme-color-replacer/client';
// import generate from '@ant-design/colors/lib/generate';
// import _objectSpread from "@babel/runtime/helpers/objectSpread";

// const getThemeList = () => {
//   var list = window.umi_plugin_ant_themeVar || [];

//   // console.log('测试', list, window.umi_plugin_ant_themeVar)

//   let themeList = [{
//     key: 'light',
//     url: 'https://gw.alipayobjects.com/zos/antfincdn/NQ%24zoisaD2/jpRkZQMyYRryryPNtyIC.svg',
//   }, {
//     key: 'dark',
//     url: 'https://gw.alipayobjects.com/zos/antfincdn/XwFOFbLkSM/LCkqqYNmvBEbokSDscrm.svg',
//   }];
//   let darkColorList = [{
//     key: '#2351f3',
//     color: '#2351f3',
//     theme: 'dark'
//   }];
//   let lightColorList = [{
//     key: '#2351f3',
//     color: '#2351f3',
//     theme: 'dark'
//   }];
//   if (list.find(function (item) {
//     return item.theme === 'dark';
//   })) {
//     themeList.push({
//       // disable click
//       disable: true,
//       key: 'realDark',
//       url: 'https://gw.alipayobjects.com/zos/antfincdn/hmKaLQvmY2/LCkqqYNmvBEbokSDscrm.svg',
//     });
//   }
//   // insert  theme color List
//   list.forEach(function (item) {
//     var color = (item.modifyVars || {})['@primary-color'];
//     if (item.theme === 'dark' && color) {
//       darkColorList.push(_objectSpread({
//         color: color
//       }, item));
//     }
//     if (!item.theme || item.theme === 'light') {
//       lightColorList.push(_objectSpread({
//         color: color
//       }, item));
//     }
//   });
//   return {
//     colorList: {
//       dark: darkColorList,
//       light: lightColorList
//     },
//     themeList: themeList
//   };
// };


// const  getAntdSerials = (color) => {
//   // 淡化（即less的tint）
//   var lightens = new Array(9).fill().map(function (t, i) {
//     return client.varyColor.lighten(color, i / 10);
//   });
//   // colorPalette 变换得到颜色值
//   // console.log('获取颜色', color);
//   var colorPalettes = generate(color);
//   var rgb = client.varyColor.toNum3(color.replace('#', '')).join(',');
//   return lightens.concat(colorPalettes).concat(rgb);
// }

// setTimeout(() => {
//   console.log(client.changer)
//   console.log(getThemeList())
//     client.changer.changeColor({
//       newColors: getAntdSerials('yellow'),
//       changeUrl: (cssUrl)=> {
//         console.log('---', cssUrl)
//         return  '/'.concat(cssUrl)
//       }
//     },Promise)
// }, 2000);
</script>
<style lang="less">
html,
body {
  height: 100%;
  width: 100%;
  background-color: #F1F2F5;
}

#app {
  height: 100%;
  width: 100%;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  min-height: 558px;
}

nav {
  padding: 30px;

  a {
    font-weight: bold;
    color: #2c3e50;

    &.router-link-exact-active {
      color: #42b983;
    }
  }
}
</style>
