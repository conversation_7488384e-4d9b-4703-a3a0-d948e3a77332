// 设置
export function setCookie (cname, cvalue, exdays) {
  const d = new Date();
  d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
  const expires = "expires=" + d.toGMTString();
  document.cookie = cname + "=" + cvalue + "; " + expires;
}
//读取
export function getCookie(name) { 
  let arr; 
  const reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)"); 
  // eslint-disable-next-line no-cond-assign
  if (arr = document.cookie.match(reg)) {
    return arr[2] // unescape(arr[2])
  } else {
    return null
  }
}
//删除
export function delCookie(name) { 
  const exp = new Date(); 
  exp.setTime(exp.getTime() - 1); 
  const cval = getCookie(name); 
  if (cval != null) { document.cookie = name + "=" + cval + ";expires=" + exp.toGMTString(); } 
}
/**
 * 将日期转换为 yy-mm-dd hh:mm 格式
 * @param {Date|string|number} dateInput - 可以是 Date 对象、时间戳或日期字符串
 * @returns {string} 格式化后的日期字符串，格式为 yy-mm-dd hh:mm
 */
export function formatDateToYYMMDDHHMM(dateInput) {
  const date = new Date(dateInput);
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date input');
  }
  
  // 获取各个时间部分
  const year = date.getFullYear(); // 取年份后两位
  const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份补零
  const day = date.getDate().toString().padStart(2, '0'); // 日期补零
  const hours = date.getHours().toString().padStart(2, '0'); // 小时补零
  const minutes = date.getMinutes().toString().padStart(2, '0'); // 分钟补零
  
  // 组合成 yy-mm-dd hh:mm 格式
  return `${year}-${month}-${day} ${hours}:${minutes}`;
}

// 判断容器是否在可视范围内
export function isElementInViewport(el) {
  const rect = el.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}

// 设置容器滚动到可视范围内
export function viewPanel(toViewPanel, behavior) {
  toViewPanel.scrollIntoView({
    // 平滑滚动
    behavior: behavior, 
    // 对齐到视口顶部（可选：'start', 'center', 'end', 'nearest'）
    block: 'start',     
  });
}

// 遍历是否含有父节点
export function hasParentWithClass(element, className) {
  let current = element.parentElement;
  while (current) {
    if (current.classList.contains(className)) {
      return true;
    }
    current = current.parentElement;
  }
  return false;
}

