/* eslint-disable */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).MediaInfo=e()}(this,(function(){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var t,e=function(){return e=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},e.apply(this,arguments)},n=(t="undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("mediainfo.min.js",document.baseURI).href,async function(e){var n,r;(e=void 0!==(e=e||{})?e:{}).ready=new Promise((function(t,e){n=t,r=e}));var i,o,a=Object.assign({},e),u="./this.program",s="object"==typeof window,c="function"==typeof importScripts,f="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,l="";if(f){const{createRequire:t}=await import("module");var p=t("undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("mediainfo.min.js",document.baseURI).href),d=p("fs"),h=p("path");l=c?h.dirname(l)+"/":p("url").fileURLToPath(new URL("./","undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("mediainfo.min.js",document.baseURI).href)),i=(t,e)=>(t=function(t){return t.startsWith("file://")}(t)?new URL(t):h.normalize(t),d.readFileSync(t,e?void 0:"utf8")),o=t=>{var e=i(t,!0);return e.buffer||(e=new Uint8Array(e)),e},process.argv.length>1&&(u=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("uncaughtException",(function(t){if(!(t instanceof L))throw t})),process.on("unhandledRejection",(function(t){throw t})),e.inspect=function(){return"[Emscripten Module object]"}}else(s||c)&&(c?l=self.location.href:"undefined"!=typeof document&&document.currentScript&&(l=document.currentScript.src),t&&(l=t),l=0!==l.indexOf("blob:")?l.substr(0,l.replace(/[?#].*/,"").lastIndexOf("/")+1):"",i=t=>{var e=new XMLHttpRequest;return e.open("GET",t,!1),e.send(null),e.responseText},c&&(o=t=>{var e=new XMLHttpRequest;return e.open("GET",t,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)}));e.print||console.log.bind(console);var m,y,v=e.printErr||console.warn.bind(console);Object.assign(e,a),a=null,e.arguments&&e.arguments,e.thisProgram&&(u=e.thisProgram),e.quit&&e.quit,e.wasmBinary&&(m=e.wasmBinary),e.noExitRuntime,"object"!=typeof WebAssembly&&H("no native wasm support detected");var g,w,_,b,T,C,$,P,A,S,F=!1,D="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function j(t,e){return t?function(t,e,n){for(var r=e+n,i=e;t[i]&&!(i>=r);)++i;if(i-e>16&&t.buffer&&D)return D.decode(t.subarray(e,i));for(var o="";e<i;){var a=t[e++];if(128&a){var u=63&t[e++];if(192!=(224&a)){var s=63&t[e++];if((a=224==(240&a)?(15&a)<<12|u<<6|s:(7&a)<<18|u<<12|s<<6|63&t[e++])<65536)o+=String.fromCharCode(a);else{var c=a-65536;o+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else o+=String.fromCharCode((31&a)<<6|u)}else o+=String.fromCharCode(a)}return o}(_,t,e):""}function O(t,e,n,r){if(!(r>0))return 0;for(var i=n,o=n+r-1,a=0;a<t.length;++a){var u=t.charCodeAt(a);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&t.charCodeAt(++a)),u<=127){if(n>=o)break;e[n++]=u}else if(u<=2047){if(n+1>=o)break;e[n++]=192|u>>6,e[n++]=128|63&u}else if(u<=65535){if(n+2>=o)break;e[n++]=224|u>>12,e[n++]=128|u>>6&63,e[n++]=128|63&u}else{if(n+3>=o)break;e[n++]=240|u>>18,e[n++]=128|u>>12&63,e[n++]=128|u>>6&63,e[n++]=128|63&u}}return e[n]=0,n-i}function E(t){for(var e=0,n=0;n<t.length;++n){var r=t.charCodeAt(n);r<=127?e++:r<=2047?e+=2:r>=55296&&r<=57343?(e+=4,++n):e+=3}return e}function R(t){g=t,e.HEAP8=w=new Int8Array(t),e.HEAP16=b=new Int16Array(t),e.HEAP32=C=new Int32Array(t),e.HEAPU8=_=new Uint8Array(t),e.HEAPU16=T=new Uint16Array(t),e.HEAPU32=$=new Uint32Array(t),e.HEAPF32=P=new Float32Array(t),e.HEAPF64=A=new Float64Array(t)}e.INITIAL_MEMORY;var U,I,W=[],M=[],k=[],x=0,z=null;function H(t){e.onAbort&&e.onAbort(t),v(t="Aborted("+t+")"),F=!0,t+=". Build with -sASSERTIONS for more info.";var n=new WebAssembly.RuntimeError(t);throw r(n),n}function Y(t){return t.startsWith("data:application/octet-stream;base64,")}function B(t){try{if(t==U&&m)return new Uint8Array(m);if(o)return o(t);throw"both async and sync fetching of the wasm failed"}catch(t){H(t)}}function L(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t}function V(t){for(;t.length>0;)t.shift()(e)}function G(t){this.excPtr=t,this.ptr=t-24,this.set_type=function(t){$[this.ptr+4>>2]=t},this.get_type=function(){return $[this.ptr+4>>2]},this.set_destructor=function(t){$[this.ptr+8>>2]=t},this.get_destructor=function(){return $[this.ptr+8>>2]},this.set_refcount=function(t){C[this.ptr>>2]=t},this.set_caught=function(t){t=t?1:0,w[this.ptr+12>>0]=t},this.get_caught=function(){return 0!=w[this.ptr+12>>0]},this.set_rethrown=function(t){t=t?1:0,w[this.ptr+13>>0]=t},this.get_rethrown=function(){return 0!=w[this.ptr+13>>0]},this.init=function(t,e){this.set_adjusted_ptr(0),this.set_type(t),this.set_destructor(e),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var t=C[this.ptr>>2];C[this.ptr>>2]=t+1},this.release_ref=function(){var t=C[this.ptr>>2];return C[this.ptr>>2]=t-1,1===t},this.set_adjusted_ptr=function(t){$[this.ptr+16>>2]=t},this.get_adjusted_ptr=function(){return $[this.ptr+16>>2]},this.get_exception_ptr=function(){if(Pe(this.get_type()))return $[this.excPtr>>2];var t=this.get_adjusted_ptr();return 0!==t?t:this.excPtr}}function N(t){switch(t){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+t)}}e.locateFile?Y(U="MediaInfoModule.wasm")||(I=U,U=e.locateFile?e.locateFile(I,l):l+I):U=new URL("MediaInfoModule.wasm","undefined"==typeof document&&"undefined"==typeof location?new(require("url").URL)("file:"+__filename).href:"undefined"==typeof document?location.href:document.currentScript&&document.currentScript.src||new URL("mediainfo.min.js",document.baseURI).href).href;var q=void 0;function J(t){for(var e="",n=t;_[n];)e+=q[_[n++]];return e}var X={},Z={},K={};function Q(t){if(void 0===t)return"_unknown";var e=(t=t.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return e>=48&&e<=57?"_"+t:t}function tt(t,e){return t=Q(t),new Function("body","return function "+t+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(e)}function et(t,e){var n=tt(e,(function(t){this.name=e,this.message=t;var n=new Error(t).stack;void 0!==n&&(this.stack=this.toString()+"\n"+n.replace(/^Error(:[^\n]*)?\n/,""))}));return n.prototype=Object.create(t.prototype),n.prototype.constructor=n,n.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},n}var nt=void 0;function rt(t){throw new nt(t)}var it=void 0;function ot(t){throw new it(t)}function at(t,e,n){function r(e){var r=n(e);r.length!==t.length&&ot("Mismatched type converter count");for(var i=0;i<t.length;++i)ut(t[i],r[i])}t.forEach((function(t){K[t]=e}));var i=new Array(e.length),o=[],a=0;e.forEach(((t,e)=>{Z.hasOwnProperty(t)?i[e]=Z[t]:(o.push(t),X.hasOwnProperty(t)||(X[t]=[]),X[t].push((()=>{i[e]=Z[t],++a===o.length&&r(i)})))})),0===o.length&&r(i)}function ut(t,e,n={}){if(!("argPackAdvance"in e))throw new TypeError("registerType registeredInstance requires argPackAdvance");var r=e.name;if(t||rt('type "'+r+'" must have a positive integer typeid pointer'),Z.hasOwnProperty(t)){if(n.ignoreDuplicateRegistrations)return;rt("Cannot register type '"+r+"' twice")}if(Z[t]=e,delete K[t],X.hasOwnProperty(t)){var i=X[t];delete X[t],i.forEach((t=>t()))}}function st(t){if(!(this instanceof jt))return!1;if(!(t instanceof jt))return!1;for(var e=this.$$.ptrType.registeredClass,n=this.$$.ptr,r=t.$$.ptrType.registeredClass,i=t.$$.ptr;e.baseClass;)n=e.upcast(n),e=e.baseClass;for(;r.baseClass;)i=r.upcast(i),r=r.baseClass;return e===r&&n===i}function ct(t){rt(t.$$.ptrType.registeredClass.name+" instance already deleted")}var ft=!1;function lt(t){}function pt(t){t.count.value-=1,0===t.count.value&&function(t){t.smartPtr?t.smartPtrType.rawDestructor(t.smartPtr):t.ptrType.registeredClass.rawDestructor(t.ptr)}(t)}function dt(t,e,n){if(e===n)return t;if(void 0===n.baseClass)return null;var r=dt(t,e,n.baseClass);return null===r?null:n.downcast(r)}var ht={};function mt(){return Object.keys(bt).length}function yt(){var t=[];for(var e in bt)bt.hasOwnProperty(e)&&t.push(bt[e]);return t}var vt=[];function gt(){for(;vt.length;){var t=vt.pop();t.$$.deleteScheduled=!1,t.delete()}}var wt=void 0;function _t(t){wt=t,vt.length&&wt&&wt(gt)}var bt={};function Tt(t,e){return e=function(t,e){for(void 0===e&&rt("ptr should not be undefined");t.baseClass;)e=t.upcast(e),t=t.baseClass;return e}(t,e),bt[e]}function Ct(t,e){return e.ptrType&&e.ptr||ot("makeClassHandle requires ptr and ptrType"),!!e.smartPtrType!=!!e.smartPtr&&ot("Both smartPtrType and smartPtr must be specified"),e.count={value:1},Pt(Object.create(t,{$$:{value:e}}))}function $t(t){var e=this.getPointee(t);if(!e)return this.destructor(t),null;var n=Tt(this.registeredClass,e);if(void 0!==n){if(0===n.$$.count.value)return n.$$.ptr=e,n.$$.smartPtr=t,n.clone();var r=n.clone();return this.destructor(t),r}function i(){return this.isSmartPointer?Ct(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:e,smartPtrType:this,smartPtr:t}):Ct(this.registeredClass.instancePrototype,{ptrType:this,ptr:t})}var o,a=this.registeredClass.getActualType(e),u=ht[a];if(!u)return i.call(this);o=this.isConst?u.constPointerType:u.pointerType;var s=dt(e,this.registeredClass,o.registeredClass);return null===s?i.call(this):this.isSmartPointer?Ct(o.registeredClass.instancePrototype,{ptrType:o,ptr:s,smartPtrType:this,smartPtr:t}):Ct(o.registeredClass.instancePrototype,{ptrType:o,ptr:s})}function Pt(t){return"undefined"==typeof FinalizationRegistry?(Pt=t=>t,t):(ft=new FinalizationRegistry((t=>{pt(t.$$)})),Pt=t=>{var e=t.$$;if(e.smartPtr){var n={$$:e};ft.register(t,n,t)}return t},lt=t=>ft.unregister(t),Pt(t))}function At(){if(this.$$.ptr||ct(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var t,e=Pt(Object.create(Object.getPrototypeOf(this),{$$:{value:(t=this.$$,{count:t.count,deleteScheduled:t.deleteScheduled,preservePointerOnDelete:t.preservePointerOnDelete,ptr:t.ptr,ptrType:t.ptrType,smartPtr:t.smartPtr,smartPtrType:t.smartPtrType})}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e}function St(){this.$$.ptr||ct(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&rt("Object already scheduled for deletion"),lt(this),pt(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Ft(){return!this.$$.ptr}function Dt(){return this.$$.ptr||ct(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&rt("Object already scheduled for deletion"),vt.push(this),1===vt.length&&wt&&wt(gt),this.$$.deleteScheduled=!0,this}function jt(){}function Ot(t,e,n){if(void 0===t[e].overloadTable){var r=t[e];t[e]=function(){return t[e].overloadTable.hasOwnProperty(arguments.length)||rt("Function '"+n+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+t[e].overloadTable+")!"),t[e].overloadTable[arguments.length].apply(this,arguments)},t[e].overloadTable=[],t[e].overloadTable[r.argCount]=r}}function Et(t,e,n,r,i,o,a,u){this.name=t,this.constructor=e,this.instancePrototype=n,this.rawDestructor=r,this.baseClass=i,this.getActualType=o,this.upcast=a,this.downcast=u,this.pureVirtualFunctions=[]}function Rt(t,e,n){for(;e!==n;)e.upcast||rt("Expected null or instance of "+n.name+", got an instance of "+e.name),t=e.upcast(t),e=e.baseClass;return t}function Ut(t,e){if(null===e)return this.isReference&&rt("null is not a valid "+this.name),0;e.$$||rt('Cannot pass "'+ne(e)+'" as a '+this.name),e.$$.ptr||rt("Cannot pass deleted object as a pointer of type "+this.name);var n=e.$$.ptrType.registeredClass;return Rt(e.$$.ptr,n,this.registeredClass)}function It(t,e){var n;if(null===e)return this.isReference&&rt("null is not a valid "+this.name),this.isSmartPointer?(n=this.rawConstructor(),null!==t&&t.push(this.rawDestructor,n),n):0;e.$$||rt('Cannot pass "'+ne(e)+'" as a '+this.name),e.$$.ptr||rt("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&e.$$.ptrType.isConst&&rt("Cannot convert argument of type "+(e.$$.smartPtrType?e.$$.smartPtrType.name:e.$$.ptrType.name)+" to parameter type "+this.name);var r=e.$$.ptrType.registeredClass;if(n=Rt(e.$$.ptr,r,this.registeredClass),this.isSmartPointer)switch(void 0===e.$$.smartPtr&&rt("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:e.$$.smartPtrType===this?n=e.$$.smartPtr:rt("Cannot convert argument of type "+(e.$$.smartPtrType?e.$$.smartPtrType.name:e.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:n=e.$$.smartPtr;break;case 2:if(e.$$.smartPtrType===this)n=e.$$.smartPtr;else{var i=e.clone();n=this.rawShare(n,ee.toHandle((function(){i.delete()}))),null!==t&&t.push(this.rawDestructor,n)}break;default:rt("Unsupporting sharing policy")}return n}function Wt(t,e){if(null===e)return this.isReference&&rt("null is not a valid "+this.name),0;e.$$||rt('Cannot pass "'+ne(e)+'" as a '+this.name),e.$$.ptr||rt("Cannot pass deleted object as a pointer of type "+this.name),e.$$.ptrType.isConst&&rt("Cannot convert argument of type "+e.$$.ptrType.name+" to parameter type "+this.name);var n=e.$$.ptrType.registeredClass;return Rt(e.$$.ptr,n,this.registeredClass)}function Mt(t){return this.fromWireType(C[t>>2])}function kt(t){return this.rawGetPointee&&(t=this.rawGetPointee(t)),t}function xt(t){this.rawDestructor&&this.rawDestructor(t)}function zt(t){null!==t&&t.delete()}function Ht(t,e,n,r,i,o,a,u,s,c,f){this.name=t,this.registeredClass=e,this.isReference=n,this.isConst=r,this.isSmartPointer=i,this.pointeeType=o,this.sharingPolicy=a,this.rawGetPointee=u,this.rawConstructor=s,this.rawShare=c,this.rawDestructor=f,i||void 0!==e.baseClass?this.toWireType=It:r?(this.toWireType=Ut,this.destructorFunction=null):(this.toWireType=Wt,this.destructorFunction=null)}function Yt(t){return S.get(t)}function Bt(t,n,r){return t.includes("j")?function(t,n,r){var i=e["dynCall_"+t];return r&&r.length?i.apply(null,[n].concat(r)):i.call(null,n)}(t,n,r):Yt(n).apply(null,r)}function Lt(t,e){var n,r,i,o=(t=J(t)).includes("j")?(n=t,r=e,i=[],function(){return i.length=0,Object.assign(i,arguments),Bt(n,r,i)}):Yt(e);return"function"!=typeof o&&rt("unknown function pointer with signature "+t+": "+e),o}var Vt=void 0;function Gt(t){var e=Ce(t),n=J(e);return Te(e),n}function Nt(t,e){var n=[],r={};throw e.forEach((function t(e){r[e]||Z[e]||(K[e]?K[e].forEach(t):(n.push(e),r[e]=!0))})),new Vt(t+": "+n.map(Gt).join([", "]))}function qt(t,e){for(var n=[],r=0;r<t;r++)n.push($[e+4*r>>2]);return n}function Jt(t){for(;t.length;){var e=t.pop();t.pop()(e)}}function Xt(t,e,n,r,i){var o=e.length;o<2&&rt("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var a=null!==e[1]&&null!==n,u=!1,s=1;s<e.length;++s)if(null!==e[s]&&void 0===e[s].destructorFunction){u=!0;break}var c="void"!==e[0].name,f="",l="";for(s=0;s<o-2;++s)f+=(0!==s?", ":"")+"arg"+s,l+=(0!==s?", ":"")+"arg"+s+"Wired";var p="return function "+Q(t)+"("+f+") {\nif (arguments.length !== "+(o-2)+") {\nthrowBindingError('function "+t+" called with ' + arguments.length + ' arguments, expected "+(o-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var d=u?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],m=[rt,r,i,Jt,e[0],e[1]];for(a&&(p+="var thisWired = classParam.toWireType("+d+", this);\n"),s=0;s<o-2;++s)p+="var arg"+s+"Wired = argType"+s+".toWireType("+d+", arg"+s+"); // "+e[s+2].name+"\n",h.push("argType"+s),m.push(e[s+2]);if(a&&(l="thisWired"+(l.length>0?", ":"")+l),p+=(c?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)p+="runDestructors(destructors);\n";else for(s=a?1:2;s<e.length;++s){var y=1===s?"thisWired":"arg"+(s-2)+"Wired";null!==e[s].destructorFunction&&(p+=y+"_dtor("+y+"); // "+e[s].name+"\n",h.push(y+"_dtor"),m.push(e[s].destructorFunction))}return c&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",h.push(p),function(t,e){if(!(t instanceof Function))throw new TypeError("new_ called with constructor type "+typeof t+" which is not a function");var n=tt(t.name||"unknownFunctionName",(function(){}));n.prototype=t.prototype;var r=new n,i=t.apply(r,e);return i instanceof Object?i:r}(Function,h).apply(null,m)}var Zt=[],Kt=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Qt(){for(var t=0,e=5;e<Kt.length;++e)void 0!==Kt[e]&&++t;return t}function te(){for(var t=5;t<Kt.length;++t)if(void 0!==Kt[t])return Kt[t];return null}var ee={toValue:t=>(t||rt("Cannot use deleted val. handle = "+t),Kt[t].value),toHandle:t=>{switch(t){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var e=Zt.length?Zt.pop():Kt.length;return Kt[e]={refcount:1,value:t},e}}};function ne(t){if(null===t)return"null";var e=typeof t;return"object"===e||"array"===e||"function"===e?t.toString():""+t}function re(t,e){switch(e){case 2:return function(t){return this.fromWireType(P[t>>2])};case 3:return function(t){return this.fromWireType(A[t>>3])};default:throw new TypeError("Unknown float type: "+t)}}function ie(t,e,n){switch(e){case 0:return n?function(t){return w[t]}:function(t){return _[t]};case 1:return n?function(t){return b[t>>1]}:function(t){return T[t>>1]};case 2:return n?function(t){return C[t>>2]}:function(t){return $[t>>2]};default:throw new TypeError("Unknown integer type: "+t)}}var oe="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function ae(t,e){for(var n=t,r=n>>1,i=r+e/2;!(r>=i)&&T[r];)++r;if((n=r<<1)-t>32&&oe)return oe.decode(_.subarray(t,n));for(var o="",a=0;!(a>=e/2);++a){var u=b[t+2*a>>1];if(0==u)break;o+=String.fromCharCode(u)}return o}function ue(t,e,n){if(void 0===n&&(n=2147483647),n<2)return 0;for(var r=e,i=(n-=2)<2*t.length?n/2:t.length,o=0;o<i;++o){var a=t.charCodeAt(o);b[e>>1]=a,e+=2}return b[e>>1]=0,e-r}function se(t){return 2*t.length}function ce(t,e){for(var n=0,r="";!(n>=e/4);){var i=C[t+4*n>>2];if(0==i)break;if(++n,i>=65536){var o=i-65536;r+=String.fromCharCode(55296|o>>10,56320|1023&o)}else r+=String.fromCharCode(i)}return r}function fe(t,e,n){if(void 0===n&&(n=2147483647),n<4)return 0;for(var r=e,i=r+n-4,o=0;o<t.length;++o){var a=t.charCodeAt(o);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&t.charCodeAt(++o)),C[e>>2]=a,(e+=4)+4>i)break}return C[e>>2]=0,e-r}function le(t){for(var e=0,n=0;n<t.length;++n){var r=t.charCodeAt(n);r>=55296&&r<=57343&&++n,e+=4}return e}function pe(t){var e=E(t)+1,n=be(e);return n&&O(t,w,n,e),n}function de(t){try{return y.grow(t-g.byteLength+65535>>>16),R(y.buffer),1}catch(t){}}var he={};function me(){if(!me.strings){var t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:u||"./this.program"};for(var e in he)void 0===he[e]?delete t[e]:t[e]=he[e];var n=[];for(var e in t)n.push(e+"="+t[e]);me.strings=n}return me.strings}function ye(t){return t%4==0&&(t%100!=0||t%400==0)}var ve=[31,29,31,30,31,30,31,31,30,31,30,31],ge=[31,28,31,30,31,30,31,31,30,31,30,31];function we(t,e,n,r){var i=C[r+40>>2],o={tm_sec:C[r>>2],tm_min:C[r+4>>2],tm_hour:C[r+8>>2],tm_mday:C[r+12>>2],tm_mon:C[r+16>>2],tm_year:C[r+20>>2],tm_wday:C[r+24>>2],tm_yday:C[r+28>>2],tm_isdst:C[r+32>>2],tm_gmtoff:C[r+36>>2],tm_zone:i?j(i):""},a=j(n),u={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var s in u)a=a.replace(new RegExp(s,"g"),u[s]);var c=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],f=["January","February","March","April","May","June","July","August","September","October","November","December"];function l(t,e,n){for(var r="number"==typeof t?t.toString():t||"";r.length<e;)r=n[0]+r;return r}function p(t,e){return l(t,e,"0")}function d(t,e){function n(t){return t<0?-1:t>0?1:0}var r;return 0===(r=n(t.getFullYear()-e.getFullYear()))&&0===(r=n(t.getMonth()-e.getMonth()))&&(r=n(t.getDate()-e.getDate())),r}function h(t){switch(t.getDay()){case 0:return new Date(t.getFullYear()-1,11,29);case 1:return t;case 2:return new Date(t.getFullYear(),0,3);case 3:return new Date(t.getFullYear(),0,2);case 4:return new Date(t.getFullYear(),0,1);case 5:return new Date(t.getFullYear()-1,11,31);case 6:return new Date(t.getFullYear()-1,11,30)}}function m(t){var e=function(t,e){for(var n=new Date(t.getTime());e>0;){var r=ye(n.getFullYear()),i=n.getMonth(),o=(r?ve:ge)[i];if(!(e>o-n.getDate()))return n.setDate(n.getDate()+e),n;e-=o-n.getDate()+1,n.setDate(1),i<11?n.setMonth(i+1):(n.setMonth(0),n.setFullYear(n.getFullYear()+1))}return n}(new Date(t.tm_year+1900,0,1),t.tm_yday),n=new Date(e.getFullYear(),0,4),r=new Date(e.getFullYear()+1,0,4),i=h(n),o=h(r);return d(i,e)<=0?d(o,e)<=0?e.getFullYear()+1:e.getFullYear():e.getFullYear()-1}var y={"%a":function(t){return c[t.tm_wday].substring(0,3)},"%A":function(t){return c[t.tm_wday]},"%b":function(t){return f[t.tm_mon].substring(0,3)},"%B":function(t){return f[t.tm_mon]},"%C":function(t){return p((t.tm_year+1900)/100|0,2)},"%d":function(t){return p(t.tm_mday,2)},"%e":function(t){return l(t.tm_mday,2," ")},"%g":function(t){return m(t).toString().substring(2)},"%G":function(t){return m(t)},"%H":function(t){return p(t.tm_hour,2)},"%I":function(t){var e=t.tm_hour;return 0==e?e=12:e>12&&(e-=12),p(e,2)},"%j":function(t){return p(t.tm_mday+function(t,e){for(var n=0,r=0;r<=e;n+=t[r++]);return n}(ye(t.tm_year+1900)?ve:ge,t.tm_mon-1),3)},"%m":function(t){return p(t.tm_mon+1,2)},"%M":function(t){return p(t.tm_min,2)},"%n":function(){return"\n"},"%p":function(t){return t.tm_hour>=0&&t.tm_hour<12?"AM":"PM"},"%S":function(t){return p(t.tm_sec,2)},"%t":function(){return"\t"},"%u":function(t){return t.tm_wday||7},"%U":function(t){var e=t.tm_yday+7-t.tm_wday;return p(Math.floor(e/7),2)},"%V":function(t){var e=Math.floor((t.tm_yday+7-(t.tm_wday+6)%7)/7);if((t.tm_wday+371-t.tm_yday-2)%7<=2&&e++,e){if(53==e){var n=(t.tm_wday+371-t.tm_yday)%7;4==n||3==n&&ye(t.tm_year)||(e=1)}}else{e=52;var r=(t.tm_wday+7-t.tm_yday-1)%7;(4==r||5==r&&ye(t.tm_year%400-1))&&e++}return p(e,2)},"%w":function(t){return t.tm_wday},"%W":function(t){var e=t.tm_yday+7-(t.tm_wday+6)%7;return p(Math.floor(e/7),2)},"%y":function(t){return(t.tm_year+1900).toString().substring(2)},"%Y":function(t){return t.tm_year+1900},"%z":function(t){var e=t.tm_gmtoff,n=e>=0;return e=(e=Math.abs(e)/60)/60*100+e%60,(n?"+":"-")+String("0000"+e).slice(-4)},"%Z":function(t){return t.tm_zone},"%%":function(){return"%"}};for(var s in a=a.replace(/%%/g,"\0\0"),y)a.includes(s)&&(a=a.replace(new RegExp(s,"g"),y[s](o)));a=a.replace(/\0\0/g,"%");var v,g,_,b,T,$,P=(v=a,g=!1,b=_>0?_:E(v)+1,T=new Array(b),$=O(v,T,0,T.length),g&&(T.length=$),T);return P.length>e?0:(function(t,e){w.set(t,e)}(P,t),P.length-1)}!function(){for(var t=new Array(256),e=0;e<256;++e)t[e]=String.fromCharCode(e);q=t}(),nt=e.BindingError=et(Error,"BindingError"),it=e.InternalError=et(Error,"InternalError"),jt.prototype.isAliasOf=st,jt.prototype.clone=At,jt.prototype.delete=St,jt.prototype.isDeleted=Ft,jt.prototype.deleteLater=Dt,e.getInheritedInstanceCount=mt,e.getLiveInheritedInstances=yt,e.flushPendingDeletes=gt,e.setDelayFunction=_t,Ht.prototype.getPointee=kt,Ht.prototype.destructor=xt,Ht.prototype.argPackAdvance=8,Ht.prototype.readValueFromPointer=Mt,Ht.prototype.deleteObject=zt,Ht.prototype.fromWireType=$t,Vt=e.UnboundTypeError=et(Error,"UnboundTypeError"),e.count_emval_handles=Qt,e.get_first_emval=te;var _e={e:function(t,e,n){throw new G(t).init(e,n),t},k:function(t,e,n,r,i){},i:function(t,e,n,r,i){var o=N(n);ut(t,{name:e=J(e),fromWireType:function(t){return!!t},toWireType:function(t,e){return e?r:i},argPackAdvance:8,readValueFromPointer:function(t){var r;if(1===n)r=w;else if(2===n)r=b;else{if(4!==n)throw new TypeError("Unknown boolean type size: "+e);r=C}return this.fromWireType(r[t>>o])},destructorFunction:null})},u:function(t,n,r,i,o,a,u,s,c,f,l,p,d){l=J(l),a=Lt(o,a),s&&(s=Lt(u,s)),f&&(f=Lt(c,f)),d=Lt(p,d);var h=Q(l);!function(t,n,r){e.hasOwnProperty(t)?((void 0===r||void 0!==e[t].overloadTable&&void 0!==e[t].overloadTable[r])&&rt("Cannot register public name '"+t+"' twice"),Ot(e,t,t),e.hasOwnProperty(r)&&rt("Cannot register multiple overloads of a function with the same number of arguments ("+r+")!"),e[t].overloadTable[r]=n):(e[t]=n,void 0!==r&&(e[t].numArguments=r))}(h,(function(){Nt("Cannot construct "+l+" due to unbound types",[i])})),at([t,n,r],i?[i]:[],(function(n){var r,o;n=n[0],o=i?(r=n.registeredClass).instancePrototype:jt.prototype;var u=tt(h,(function(){if(Object.getPrototypeOf(this)!==c)throw new nt("Use 'new' to construct "+l);if(void 0===p.constructor_body)throw new nt(l+" has no accessible constructor");var t=p.constructor_body[arguments.length];if(void 0===t)throw new nt("Tried to invoke ctor of "+l+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(p.constructor_body).toString()+") parameters instead!");return t.apply(this,arguments)})),c=Object.create(o,{constructor:{value:u}});u.prototype=c;var p=new Et(l,u,c,d,r,a,s,f),m=new Ht(l,p,!0,!1,!1),y=new Ht(l+"*",p,!1,!1,!1),v=new Ht(l+" const*",p,!1,!0,!1);return ht[t]={pointerType:y,constPointerType:v},function(t,n,r){e.hasOwnProperty(t)||ot("Replacing nonexistant public symbol"),void 0!==e[t].overloadTable&&void 0!==r?e[t].overloadTable[r]=n:(e[t]=n,e[t].argCount=r)}(h,u),[m,y,v]}))},t:function(t,e,n,r,i,o){var a;e>0||H(a);var u=qt(e,n);i=Lt(r,i),at([],[t],(function(t){var n="constructor "+(t=t[0]).name;if(void 0===t.registeredClass.constructor_body&&(t.registeredClass.constructor_body=[]),void 0!==t.registeredClass.constructor_body[e-1])throw new nt("Cannot register multiple constructors with identical number of parameters ("+(e-1)+") for class '"+t.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return t.registeredClass.constructor_body[e-1]=()=>{Nt("Cannot construct "+t.name+" due to unbound types",u)},at([],u,(function(r){return r.splice(1,0,null),t.registeredClass.constructor_body[e-1]=Xt(n,r,null,i,o),[]})),[]}))},c:function(t,e,n,r,i,o,a,u){var s=qt(n,r);e=J(e),o=Lt(i,o),at([],[t],(function(t){var r=(t=t[0]).name+"."+e;function i(){Nt("Cannot call "+r+" due to unbound types",s)}e.startsWith("@@")&&(e=Symbol[e.substring(2)]),u&&t.registeredClass.pureVirtualFunctions.push(e);var c=t.registeredClass.instancePrototype,f=c[e];return void 0===f||void 0===f.overloadTable&&f.className!==t.name&&f.argCount===n-2?(i.argCount=n-2,i.className=t.name,c[e]=i):(Ot(c,e,r),c[e].overloadTable[n-2]=i),at([],s,(function(i){var u=Xt(r,i,t,o,a);return void 0===c[e].overloadTable?(u.argCount=n-2,c[e]=u):c[e].overloadTable[n-2]=u,[]})),[]}))},s:function(t,e){ut(t,{name:e=J(e),fromWireType:function(t){var e=ee.toValue(t);return function(t){t>4&&0==--Kt[t].refcount&&(Kt[t]=void 0,Zt.push(t))}(t),e},toWireType:function(t,e){return ee.toHandle(e)},argPackAdvance:8,readValueFromPointer:Mt,destructorFunction:null})},g:function(t,e,n){var r=N(n);ut(t,{name:e=J(e),fromWireType:function(t){return t},toWireType:function(t,e){return e},argPackAdvance:8,readValueFromPointer:re(e,r),destructorFunction:null})},b:function(t,e,n,r,i){e=J(e);var o=N(n),a=t=>t;if(0===r){var u=32-8*n;a=t=>t<<u>>>u}var s=e.includes("unsigned");ut(t,{name:e,fromWireType:a,toWireType:s?function(t,e){return this.name,e>>>0}:function(t,e){return this.name,e},argPackAdvance:8,readValueFromPointer:ie(e,o,0!==r),destructorFunction:null})},a:function(t,e,n){var r=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][e];function i(t){var e=$,n=e[t>>=2],i=e[t+1];return new r(g,i,n)}ut(t,{name:n=J(n),fromWireType:i,argPackAdvance:8,readValueFromPointer:i},{ignoreDuplicateRegistrations:!0})},f:function(t,e){var n="std::string"===(e=J(e));ut(t,{name:e,fromWireType:function(t){var e,r=$[t>>2],i=t+4;if(n)for(var o=i,a=0;a<=r;++a){var u=i+a;if(a==r||0==_[u]){var s=j(o,u-o);void 0===e?e=s:(e+=String.fromCharCode(0),e+=s),o=u+1}}else{var c=new Array(r);for(a=0;a<r;++a)c[a]=String.fromCharCode(_[i+a]);e=c.join("")}return Te(t),e},toWireType:function(t,e){var r;e instanceof ArrayBuffer&&(e=new Uint8Array(e));var i="string"==typeof e;i||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int8Array||rt("Cannot pass non-string to std::string"),r=n&&i?E(e):e.length;var o=be(4+r+1),a=o+4;if($[o>>2]=r,n&&i)O(e,_,a,r+1);else if(i)for(var u=0;u<r;++u){var s=e.charCodeAt(u);s>255&&(Te(a),rt("String has UTF-16 code units that do not fit in 8 bits")),_[a+u]=s}else for(u=0;u<r;++u)_[a+u]=e[u];return null!==t&&t.push(Te,o),o},argPackAdvance:8,readValueFromPointer:Mt,destructorFunction:function(t){Te(t)}})},d:function(t,e,n){var r,i,o,a,u;n=J(n),2===e?(r=ae,i=ue,a=se,o=()=>T,u=1):4===e&&(r=ce,i=fe,a=le,o=()=>$,u=2),ut(t,{name:n,fromWireType:function(t){for(var n,i=$[t>>2],a=o(),s=t+4,c=0;c<=i;++c){var f=t+4+c*e;if(c==i||0==a[f>>u]){var l=r(s,f-s);void 0===n?n=l:(n+=String.fromCharCode(0),n+=l),s=f+e}}return Te(t),n},toWireType:function(t,r){"string"!=typeof r&&rt("Cannot pass non-string to C++ string type "+n);var o=a(r),s=be(4+o+e);return $[s>>2]=o>>u,i(r,s+4,o+e),null!==t&&t.push(Te,s),s},argPackAdvance:8,readValueFromPointer:Mt,destructorFunction:function(t){Te(t)}})},j:function(t,e){ut(t,{isVoid:!0,name:e=J(e),argPackAdvance:0,fromWireType:function(){},toWireType:function(t,e){}})},q:function(t,e){var n,r=new Date(1e3*($[(n=t)>>2]+4294967296*C[n+4>>2]));C[e>>2]=r.getUTCSeconds(),C[e+4>>2]=r.getUTCMinutes(),C[e+8>>2]=r.getUTCHours(),C[e+12>>2]=r.getUTCDate(),C[e+16>>2]=r.getUTCMonth(),C[e+20>>2]=r.getUTCFullYear()-1900,C[e+24>>2]=r.getUTCDay();var i=Date.UTC(r.getUTCFullYear(),0,1,0,0,0,0),o=(r.getTime()-i)/864e5|0;C[e+28>>2]=o},r:function(t,e,n){var r=(new Date).getFullYear(),i=new Date(r,0,1),o=new Date(r,6,1),a=i.getTimezoneOffset(),u=o.getTimezoneOffset(),s=Math.max(a,u);function c(t){var e=t.toTimeString().match(/\(([A-Za-z ]+)\)$/);return e?e[1]:"GMT"}$[t>>2]=60*s,C[e>>2]=Number(a!=u);var f=c(i),l=c(o),p=pe(f),d=pe(l);u<a?($[n>>2]=p,$[n+4>>2]=d):($[n>>2]=d,$[n+4>>2]=p)},h:function(){H("")},p:function(){return Date.now()},o:function(t){var e,n,r=_.length,i=2147483648;if((t>>>=0)>i)return!1;for(var o=1;o<=4;o*=2){var a=r*(1+.2/o);if(a=Math.min(a,t+100663296),de(Math.min(i,(e=Math.max(t,a))+((n=65536)-e%n)%n)))return!0}return!1},m:function(t,e){var n=0;return me().forEach((function(r,i){var o=e+n;$[t+4*i>>2]=o,function(t,e,n){for(var r=0;r<t.length;++r)w[e++>>0]=t.charCodeAt(r);n||(w[e>>0]=0)}(r,o),n+=r.length+1})),0},n:function(t,e){var n=me();$[t>>2]=n.length;var r=0;return n.forEach((function(t){r+=t.length+1})),$[e>>2]=r,0},l:function(t,e,n,r,i){return we(t,e,n,r)}};!function(){var t={a:_e};function n(t,n){var r,i=t.exports;e.asm=i,R((y=e.asm.v).buffer),S=e.asm.y,r=e.asm.w,M.unshift(r),function(t){if(x--,e.monitorRunDependencies&&e.monitorRunDependencies(x),0==x&&z){var n=z;z=null,n()}}()}function i(t){n(t.instance)}function o(e){return(m||!s&&!c||"function"!=typeof fetch?Promise.resolve().then((function(){return B(U)})):fetch(U,{credentials:"same-origin"}).then((function(t){if(!t.ok)throw"failed to load wasm binary file at '"+U+"'";return t.arrayBuffer()})).catch((function(){return B(U)}))).then((function(e){return WebAssembly.instantiate(e,t)})).then((function(t){return t})).then(e,(function(t){v("failed to asynchronously prepare wasm: "+t),H(t)}))}if(x++,e.monitorRunDependencies&&e.monitorRunDependencies(x),e.instantiateWasm)try{return e.instantiateWasm(t,n)}catch(t){v("Module.instantiateWasm callback failed with error: "+t),r(t)}(m||"function"!=typeof WebAssembly.instantiateStreaming||Y(U)||f||"function"!=typeof fetch?o(i):fetch(U,{credentials:"same-origin"}).then((function(e){return WebAssembly.instantiateStreaming(e,t).then(i,(function(t){return v("wasm streaming compile failed: "+t),v("falling back to ArrayBuffer instantiation"),o(i)}))}))).catch(r)}(),e.___wasm_call_ctors=function(){return(e.___wasm_call_ctors=e.asm.w).apply(null,arguments)};var be=e._malloc=function(){return(be=e._malloc=e.asm.x).apply(null,arguments)},Te=e._free=function(){return(Te=e._free=e.asm.z).apply(null,arguments)},Ce=e.___getTypeName=function(){return(Ce=e.___getTypeName=e.asm.A).apply(null,arguments)};e.__embind_initialize_bindings=function(){return(e.__embind_initialize_bindings=e.asm.B).apply(null,arguments)};var $e,Pe=e.___cxa_is_pointer_type=function(){return(Pe=e.___cxa_is_pointer_type=e.asm.C).apply(null,arguments)};function Ae(t){function r(){$e||($e=!0,e.calledRun=!0,F||(V(M),n(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),function(){if(e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;)t=e.postRun.shift(),k.unshift(t);var t;V(k)}()))}x>0||(function(){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)t=e.preRun.shift(),W.unshift(t);var t;V(W)}(),x>0||(e.setStatus?(e.setStatus("Running..."),setTimeout((function(){setTimeout((function(){e.setStatus("")}),1),r()}),1)):r()))}if(e.dynCall_iiijj=function(){return(e.dynCall_iiijj=e.asm.D).apply(null,arguments)},e.dynCall_viijii=function(){return(e.dynCall_viijii=e.asm.E).apply(null,arguments)},e.dynCall_iiiiij=function(){return(e.dynCall_iiiiij=e.asm.F).apply(null,arguments)},e.dynCall_iiiiijj=function(){return(e.dynCall_iiiiijj=e.asm.G).apply(null,arguments)},e.dynCall_iiiiiijj=function(){return(e.dynCall_iiiiiijj=e.asm.H).apply(null,arguments)},z=function t(){$e||Ae(),$e||(z=t)},e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();return Ae(),e.ready}),r={coverData:!1,chunkSize:262144,format:"object",full:!1},i=function(){},o=function(){function t(t,e){this.wasmInstance=t,this.options=e}return t.prototype.analyzeData=function(t,e,n){var i=this,o=0;if(void 0===n)return new Promise((function(n,r){return i.analyzeData(t,e,(function(t,e){return e?r(e):n(t)}))}));var a=function(t){var a=function(){var c,f,l=function(t){u(t)?a():s()};try{var p=Math.min(null!==(c=i.options.chunkSize)&&void 0!==c?c:r.chunkSize,t-o);f=e(p,o)}catch(t){if(t instanceof Error)return n("",t);if("string"==typeof t)return n("",new Error(t))}f instanceof Promise?f.then(l).catch((function(t){return n("",t)})):void 0!==f&&l(f)},u=function(e){if(0===e.length||i.openBufferContinue(e,e.length))return!1;var n=i.openBufferContinueGotoGet();return-1===n?o+=e.length:(o=n,i.openBufferInit(t,n)),!0},s=function(){i.openBufferFinalize();var t=i.inform();n("object"===i.options.format?JSON.parse(t):t)};i.openBufferInit(t,o),a()},u=t();u instanceof Promise?u.then(a):a(u)},t.prototype.close=function(){this.wasmInstance.close()},t.prototype.inform=function(){return this.wasmInstance.inform()},t.prototype.openBufferContinue=function(t,e){return!!(8&this.wasmInstance.open_buffer_continue(t,e))},t.prototype.openBufferContinueGotoGet=function(){var t=this.wasmInstance.open_buffer_continue_goto_get_lower(),e=this.wasmInstance.open_buffer_continue_goto_get_upper();return-1==t&&-1==e?-1:t<0?t+4294967296+4294967296*e:t+4294967296*e},t.prototype.openBufferFinalize=function(){this.wasmInstance.open_buffer_finalize()},t.prototype.openBufferInit=function(t,e){this.wasmInstance.open_buffer_init(t,e)},t}();return function t(a,u,s){if(void 0===a&&(a={}),void 0===u)return new Promise((function(e,n){return t(a,e,n)}));var c=e(e({},r),a),f={};f.print=i,f.printErr=i,f.onAbort=function(t){s&&s(t)},c.locateFile&&(f.locateFile=c.locateFile,delete c.locateFile),n(f).then((function(t){var e,n,i="object"===c.format?"JSON":c.format,a=new t.MediaInfo(null!=i?i:r.format,null!==(e=c.coverData)&&void 0!==e?e:r.coverData,null!==(n=c.full)&&void 0!==n?n:r.full);u(new o(a,c))})).catch((function(t){s&&s(t)}))}}));
