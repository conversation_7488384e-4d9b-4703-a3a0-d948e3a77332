import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCFJenukulmI4K3YO4aNCdmJm/0hlR2TW3FCashutpeaEWwtS281bHnZAuMmBJ9YPz/Fsk9XegONgKoxb0myNypLh3I+zi3BLTp6JcPyjgA4cBFz+f2Vf5sVCuinCiXAlIcPKhbZ80p9PtF42KurpPRDXccfyNRWdWG/wtE7saDVQIDAQAB'

const privateKey = '' //无需解密就不用私钥

// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export function decrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey) // 设置私钥
  return encryptor.decrypt(txt) // 对数据进行解密
}

// 用法 
// 引入 
// import { encrypt, decrypt } from '@/utils/jsencrypt'
// 使用
// 加密 encrypt('密码字符串')
// 解密 decrypt('加密的密码字符串')