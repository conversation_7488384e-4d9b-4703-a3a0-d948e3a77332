import axios from 'axios'
import store from '@/store'
import storage from 'store'
// import { Notify } from '@idc-design/web-ui-mobile';
import { VueAxios } from './axios'
import { message } from 'ant-design-vue'
import router from '@/router'
import { getCookie, delCookie } from '@/utils/common'
// console.log(router)
// import { ACCESS_TOKEN } from '@/store/mutation-types'

// 创建 axios 实例
const request = axios.create({
  // API 请求的默认前缀
  baseURL: process.env.VUE_APP_API_BASE_URL || '',
  timeout: 300000, // 请求超时时间
  withCredentials: true,
  headers: {
    // Connection: 'keep-alive'
  }
})

// 异常拦截处理器
const errorHandler = (error) => {
  console.log(error.response)
  message.error(error.response.request.statusText)
  // if (error.response) {
  //   const data = error.response.data
  //   // 从 localstorage 获取 token
  //   // const token = storage.get(ACCESS_TOKEN)
  //   if (error.response.status === 403) {
  //     // Notify({ type: 'danger', message: data.message });
  //     message.error(data.message)
  //   }
  //   if (error.response.status === 401 && !(data.result && data.result.isLogin)) {
  //     // Notify({ type: 'danger', message: 'Authorization verification failed' });
  //     message.error('Authorization verification failed')
  //     // if (token) {
  //     // store.dispatch('Logout').then(() => {
  //     //   setTimeout(() => {
  //     //     window.location.reload()
  //     //   }, 1500)
  //     // })
  //     // }
  //   }
  // }
  return Promise.reject(error)
}

// request interceptor
request.interceptors.request.use(config => {
  const token = getCookie('userToken')
  // console.log(token, '存储的token')
  // const token = storage.get(userToken)
  // 如果 token 存在
  // 让每个请求携带自定义 token 请根据实际情况自行修改
  if (token) {
    config.headers[`Authorization`] = 'Bearer ' +  token

  }
  return config
}, errorHandler)

// response interceptor
request.interceptors.response.use((response) => {
  const res = response.data
  const params = typeof response.config.data ==='string' ? JSON.parse(response.config.data || '{}') : {}
  if(params.getHeaders) { // 返回responseHeader
    Object.assign(res, {
      responseHeaders:response.headers
    })
  }
  if(res.code === 401) {
    delCookie('userToken')
    localStorage.setItem('userName', '')
    message.warn('登录信息失效请重新登录')
    router.push('/login')
  } else if((response.status  !== 200 || res.code !== 200 ) && !(params.noMsgTips)) { // 配置noMsgTips就不提示相应请求内容
    message.error(res.msg || response.error)
  } else {
    if((response.status !== 200 && res.code !== 401 )&& typeof res !== 'string'&& !['text/event-stream', 'text/xml'].includes(res.type) ) { // 移除文字问答和语音播报的错误提示 
      message.error(res.msg)
    }
  }
  return res
}, errorHandler)

const installer = {
  vm: {},
  install (Vue) {
    Vue.use(VueAxios, request)
  }
}

export default request

export {
  installer as VueAxios,
  request as axios
}
// {
//   "messageDetailVOList": [
//       {
//           "recordDetailId": 460,
//           "messageContent": "",
//           "type": 2,
//           "userType": 0,
//           "messageSize": 0,
//           "commentStatus": 0,
//           fileList:[{
//               file: "http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/platform-oss/file/2023-05-18/1236683542746127819325.xls",
//               fileTitle: "运筹优化分享培训试题.xls.xls",
//               fileSize:1024
//             }, {
//               file: "http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/platform-oss/file/2023-05-18/1236683542746127819325.xls",
//               fileTitle: "绩效辅导面谈表.xls.xls",
//               fileSize:1024
//             }
//           ]
//       },
//       {
//           "recordDetailId": 464,
//           "messageContent": "检索文档啊",
//           "type": 0,
//           "userType": 0,
//           "messageSize": 5,
//           "commentStatus": 0,
//           "fileTitle": "检索文档啊"
//       },
//       {
//           "recordDetailId": 465,
//           "messageContent": "要检索文档,需要知道希望检索的文档的类型和名称。如果是在互联网上检索文档,可以使用各种搜索引擎,例如Google、Bing或Baidu等。这些搜索引擎可以根据关键词搜索文档,并返回相关的文档链接。如果想要更加精确的搜索结果,可以使用分类目录或文档数据库,例如Web of Science、Scopus或ProQuest等。这些数据库提供各种主题和领域的文档,可以方便地检索到需要的信息。\n\n如果需要检索纸质文档,可以使用各种图书馆或档案馆。这些机构提供免费或收费的图书馆服务,可以检索到各种领域的纸质文档。还可以使用在线图书馆服务,例如Open Library,来检索数字化的文档。",
//           "type": 0,
//           "userType": 1,
//           "messageSize": 0,
//           "commentStatus": 0,
//           "fileTitle": "要检索文档,需要知道希望检索的文档的类型和名称。如果是在互联网上检索文档,可以使用各种搜索引擎,例如Google、Bing或Baidu等。这些搜索引擎可以根据关键词搜索文档,并返回相关的文档链接。如果想要更加精确的搜索结果,可以使用分类目录或文档数据库,例如Web of Science、Scopus或ProQuest等。这些数据库提供各种主题和领域的文档,可以方便地检索到需要的信息。\n\n如果需要检索纸质文档,可以使用各种图书馆或档案馆。这些机构提供免费或收费的图书馆服务,可以检索到各种领域的纸质文档。还可以使用在线图书馆服务,例如Open Library,来检索数字化的文档。"
//       }
//   ]
// }