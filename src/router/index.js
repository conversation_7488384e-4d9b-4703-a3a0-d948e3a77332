import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router'
import { getCookie } from '@/utils/common';
const routes = [
  {
    path: '/',
    redirect: 'home',
    component: () => import('../layout/main-layout.vue'),
    children: [
      {
        path: '/home',
        name: 'home',
        component: () => import('../views/home-page.vue')
      },
      {
        path: '/chat-hello',
        name: 'chat-hello',
        component: () => import('../views/chat-hello-page.vue')
      },
      {
        path: '/chat/:messageId',
        name: 'chat',
        component: () => import('../views/chat-page.vue')
      },
      {
        path: '/history',
        name: 'history',
        component: () => import('../views/history-page.vue')
      },
      {
        path: '/agent-page',
        name: 'agent-page',
        component: () => import('../views/agent-page.vue')
      },
      {
        path: '/myagent-page',
        name: 'myAgent-page',
        component: () => import('../views/myAgent-page.vue')
      },
      {
        path: '/ai-reader',
        name: 'ai-reader',
        component: () => import('../views/agents/ai-reader/ai-reader.vue')
      },
      {
        path: '/ai-image',
        name: 'ai-image',
        component: () => import('../views/agents/ai-image/ai-image-index.vue')
      },
      {
        path: '/id-card',
        name: 'id-card',
        component: () => import('../views/agents/ai-image/id_card.vue')
      },
      {
        path: '/id-driver',
        name: 'id-driver',
        component: () => import('../views/agents/ai-image/driver_license.vue')
      },
      {
        path: '/ai-image-general',
        name: 'ai-image-general',
        component: () => import('../views/agents/ai-image/ai-image-general.vue')
      },
      {
        path: '/ai-translate',
        name: 'ai-translate',
        component: () => import('../views/agents/ai-translate/ai-translate-idx.vue')
      },
      {
        path: '/ai-translate-example',
        name: 'ai-translate-example',
        component: () => import('../views/agents/ai-translate/ai-translate.vue')
      },
      {
        path: '/ai-translate-text',
        name: 'ai-translate-text',
        component: () => import('../views/agents/ai-translate/ai-translate-text.vue')
      },
      {
        path: '/ai-translate-file',
        name: 'ai-translate-file',
        component: () => import('../views/agents/ai-translate/ai-translate-file.vue')
      },

    ]
  },
  // {
  //   path: '/home-sse',
  //   name: 'home-sse',
  //   component: () => import('../views/main-view-sse作废.vue')
  // },
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/login-view-oss.vue')
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: 'home'
  },
  // {
  //   path: '/test',
  //   name: 'test',
  //   component: () => import('../views/test-view.vue')
  // },
  // 临时屏蔽
  // {
  //   path: '/',
  //   redirect: 'home',
  //   component: () => import('../views/main-view-sse.vue'),
  //   children: [
  //     {
  //       path: '/home',
  //       name: 'home',
  //       component: () => import('../views/main-right-sse.vue')
  //     },
  //     {
  //       path: '/helper',
  //       name: 'helper',
  //       component: () => import('../components/helpers/helper-view.vue')
  //     },
  //     // {
  //     //   path: '/pea_up',
  //     //   name: 'pea_up',
  //     //   component: () => import('../views/pea_up.vue')
  //     // }
  //   ]
  // }
    
]

// const router = createRouter({
//   history: createWebHashHistory(),
//   routes  
// })
const router = createRouter({
  // mode: 'hash',
  // base: process.env.BASE_URL,
  history: createWebHistory(), // createWebHistory(), // createWebHashHistory(),
  routes,
});
router.beforeEach((to, from, next) => {
  //首先，我们先看一看to和from参数，next就是执行的意思，不写页面是不会跳转的
  // console.log(to, from);
  // if (to.meta.isAuth) { //判断是否需要鉴权
  if (to.name === 'login') {
    next();
    return;
  }
  const isAuthed_loc = localStorage.getItem('userToken');
  const isAuthed = getCookie('userToken')
  if (isAuthed && isAuthed.length && isAuthed_loc) {
    next();
  } else {
    console.log('返回登录页');
    router.push('/login');
  }
  // } else {
  //   next()
  // }
});
export default router
