{"name": "gobt", "version": "0.1.0", "private": true, "scripts": {"serve": "cross-env NODE_OPTIONS='--max-http-header-size=999999999' vue-cli-service serve --mode development ", "test": "vue-cli-service build --mode testenv", "build": "vue-cli-service build --mode production", "prod": "vue-cli-service build --mode production", "lint": "vue-cli-service lint"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@microsoft/fetch-event-source": "^2.0.1", "ant-design-vue": "^3.2.19", "axios": "^0.26.1", "better-scroll": "^2.5.1", "clipboard": "^2.0.11", "core-js": "^3.8.3", "cross-env": "^7.0.3", "highlight.js": "^11.7.0", "jsencrypt": "3.0.0-rc.1", "katex": "^0.16.21", "markdown-it": "^13.0.1", "markdown-it-abbr": "^1.0.4", "markdown-it-deflist": "^2.1.0", "markdown-it-emoji": "^2.0.2", "markdown-it-footnote": "^3.0.3", "markdown-it-ins": "^3.0.1", "markdown-it-katex": "^2.0.3", "markdown-it-mark": "^3.0.1", "markdown-it-mathjax3": "^4.3.2", "markdown-it-sub": "^1.0.0", "markdown-it-sup": "^1.0.0", "markdown-it-task-lists": "^2.1.1", "markdown-it-toc-and-anchor": "^4.2.0", "pdfjs-dist": "2.5.207", "pdfvuer": "^2.0.1", "recorder-core": "^1.2.23020100", "register-service-worker": "^1.7.2", "store": "^2.0.12", "v-click-outside": "^3.2.0", "vue": "^3.2.26", "vue-markdown": "^2.2.4", "vue-router": "^4.0.3", "vuex": "^4.0.0"}, "devDependencies": {"@ant-design/colors": "^3.2.2", "@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-pwa": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/compiler-sfc": "^3.3.10", "babel-plugin-import": "^1.13.6", "copy-webpack-plugin": "^11.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.0.0", "less-loader": "^8.0.0", "webpack-theme-color-replacer": "^1.3.26"}}