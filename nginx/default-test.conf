server {
    listen       80;
    underscores_in_headers on;
    server_name  location;
    client_max_body_size 50000M;
    client_body_buffer_size 50000M;
    client_header_buffer_size 50M;
    large_client_header_buffers 6 50M; 
    client_header_timeout 3600;
    client_body_timeout 3600;
    proxy_read_timeout  3600;
    keepalive_timeout 3600;
    location / {
        root   /usr/share/nginx/html/dist;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
        # rewrite ^/#/login(.*)$ /new/#/login$1 last;  
        # return 301 https://gbot-test.gwm.cn/new$request_uri;
        # rewrite ^/#/login(.*)$ /new/#/login$1 permanent; 
    }

    location /gbot2/ {
        proxy_pass http://4ea777.natappfree.cc/api/;
        # proxy_pass https://java-gbot-cnpgateway-prod.gwm.cn/api/;
        # porxy_pass http://sgf98p.natappfree.cc/;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /imgGbot/ {
        proxy_pass http://oss-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/gbot/images/;
        # rewrite ^/imgGbot/(.*)$ /gbot/ break;
        # http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/platform-oss/gbotImg/2023-05-23/16848227285728370853303423496671.png
        # porxy_pass http://sgf98p.natappfree.cc/;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /videoIt/ {
        proxy_pass https://gwkb.gwm.cn/oss/;
        # rewrite ^/imgGbot/(.*)$ /gbot/ break;
        # http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/platform-oss/gbotImg/2023-05-23/16848227285728370853303423496671.png
        # porxy_pass http://sgf98p.natappfree.cc/;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    location /agentImg/ {
        proxy_pass http://agent-dev.oss-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn;
        # rewrite ^/imgGbot/(.*)$ /gbot/ break;
        # http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/platform-oss/gbotImg/2023-05-23/16848227285728370853303423496671.png
        # porxy_pass http://sgf98p.natappfree.cc/;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /pdfGbot/ {
        proxy_pass http://oss-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/;
        # rewrite ^/imgGbot/(.*)$ /gbot/ break;
        # http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/platform-oss/gbotImg/2023-05-23/16848227285728370853303423496671.png
        # porxy_pass http://sgf98p.natappfree.cc/;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /gbotApi/ {
        proxy_buffering  off;
        proxy_pass http://6e10ca8b76797fcc.bd.test.paas.gwm.cn:40634/api/;
        # proxy_pass https://java-gbot-cnpgateway-prod.gwm.cn/api/;
        # porxy_pass http://sgf98p.natappfree.cc/;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /platform-oss/ {
        proxy_pass http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/platform-oss/;
        # http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn/platform-oss/gbotImg/2023-05-23/16848227285728370853303423496671.png
        # porxy_pass http://sgf98p.natappfree.cc/;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /tts-websocket/ {
        add_header backendIP $upstream_addr;
        add_header backendCode $upstream_status;
        proxy_redirect off;
        proxy_pass https://java-bot.gwm.cn/;
        # 重点，转发websocket需要的设置
        proxy_set_header X-Real_IP $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header X_Forward_For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
    }
}