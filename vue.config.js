const { defineConfig } = require('@vue/cli-service')
// eslint-disable-next-line no-unused-vars
const isProd = process.env.NODE_ENV === 'production'
const isTest = process.env.NODE_ENV === 'testenv'
// const copyPath = isProd ? "dist/cmaps" : "public/cmaps"
// const createThemeColorReplacerPlugin = require('./src/themes/plugin.config.js')
const path = require("path")
module.exports = defineConfig({
  transpileDependencies: true,
  devServer: {
    historyApiFallback: true,
    allowedHosts: "all",
    // development server port 8899
    port: 8080,
    // If you want to turn on the proxy, please remove the mockjs /src/main.jsL11
    proxy: {
      '/chatgpt': {
        // target: 'http://sgf98p.natappfree.cc/',
        // target: 'http://aichat.paas.gwm.cn/',
        target: 'http://6e10ca8b76797fcc.bd.test.paas.gwm.cn:17555',
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/chatgpt': ''
        }
      },
      '/platform-oss': { // 解决oss无法用https协议访问问题
        // target: 'https://platform-oss.oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
        target: 'http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
        changeOrigin: true,
        ws: false
        // pathRewrite: {
        //   '^/platform-oss': ''
        // }
      },
      '/imgGbot': {
        // target: 'https://platform-oss.oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
        // target: 'http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
        target: 'http://oss-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
        changeOrigin: true,
        ws: false,
        pathRewrite: {
          '^/imgGbot': '/gbot/images'
        }
      },
      '/videoIt': {
        target: 'https://gwkb.gwm.cn/oss',
        changeOrigin: true,
        ws: false,
        pathRewrite: {
          '^/videoIt': ''
        }
      },
      '/agentImg': {
        // target: 'https://platform-oss.oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
        // target: 'http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
        target: 'http://oss-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
        changeOrigin: true,
        ws: false,
        pathRewrite: {
          '^/agentImg': ''
        }
      },
      // '/imgGbot': { // 解决oss无法用https协议访问问题
      //   // target: 'https://platform-oss.oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
      //   // target: 'http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
      //   target: 'http://oss-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
      //   changeOrigin: true,
      //   ws: false,
      //   pathRewrite: {
      //     '^/imgGbot': '/gbot'
      //   }
      // },
      '/pdfGbot': { // 解决oss无法用https协议访问问题
        // target: 'https://platform-oss.oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
        // target: 'http://oss0c83-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
        target: 'http://oss-cn-baoding-gwmcloud-d01-a.ops.cloud.gwm.cn',
        changeOrigin: true,
        ws: false,
        pathRewrite: {
          '^/pdfGbot': ''
        }
      },
      // '/tts-socket': { // socket请求转发
      //   target: 'wss://java-bot.gwm.cn',
      //   // changeOrigin: false,
      //   secure: false,
      //   ws: true,
      //   pathRewrite: {
      //     '^/tts-socket': ''
      //   }
      // },,
      '/gbotApi': {
        // target: 'http://6e10ca8b76797fcc.bd.test.paas.gwm.cn:40634/api/',// 测试地址
        target: 'http://6e10ca8b76797fcc.bd.test.paas.gwm.cn:17555/', // 新版测试地址
        // target: 'https://agent-prod-java.gwm.cn/',// 0529最新正式地址
        // target: 'http://6e10ca8b76797fcc.bd.release.paas.gwm.cn:31581/api/',// 新版正式地址
        changeOrigin: true,
        ws: true,
        proxy_buffering: false,
        pathRewrite: {
          '^/gbotApi': ''
        }
      },
      // //文档问答（测试用）
      // 'testTag': {
      //   target: 'https://java-bot.gwm.cn/plug/',// 测试地址
      //   // target: 'https://java-gbot-cnpgateway-prod.gwm.cn/api/',// 正式地址
      //   changeOrigin: true,
      //   ws: true,
      //   proxy_buffering: false,
      //   pathRewrite: {
      //     '^/testTag': ''
      //   }
      // },
    },
    // BASE_URL: 'http://chatgpt-gateway-cnp-bdtest.gwmit.cn/',
    // BASE_4A_URL: 'http://10.251.82.37:7000', //4a测试环境地址
    client: {
      overlay: false,
    },
  },
  configureWebpack: {
    plugins:[
      // createThemeColorReplacerPlugin()
    ]
  },
  // 修改标题
  pages: { 
    index: {
      entry: 'src/main.js', // 入口文件
      title: '灵犀-长城办公AI'
    }
  },
  pwa: {
    iconPaths: {
      favicon32: './favicon.ico',
      favicon192: './favicon.ico',
      favicon16: './favicon.ico',
      appleTouchIcon: './favicon.ico',
      maskIcon: './favicon.ico',
      msTileImage: './favicon.ico'
    }
  },
  chainWebpack: config => {
    config.resolve.symlinks(true) // 修复热更新失效
    config.plugins.delete('pwa');
    config.plugins.delete('workbox');

    config.merge({
      devtool: isProd ? 'hidden-source-map' : 'eval-cheap-source-map'
    })
    // config.plugin('copy').use(require('copy-webpack-plugin'), [ // 解决pdf文档字体丢失问题 生产环境编译会报错 所以生产环境 改为 通过cp命令将 字体文件从public 复制到 dist当中去 （具体见 流水线流程配置中 构建自定义命令 内容）
    //   {
    //     patterns:[
    //       {
    //         from: path.resolve(__dirname, "public/cmaps"),
    //         to: path.resolve( __dirname, "dist/cmaps"),
    //         force: true
    //       }
    //     ]
    //   }
    // ], {
    //   copyUnmodified: true
    // })
  },
  css: {
    extract: (isProd || isTest) ? { ignoreOrder: true } : false,
    loaderOptions: {
      less: {
        lessOptions: {
          modifyVars: {
            // less vars，customize Idc Design theme
            'primary-color': '#5416FF',
            // 'link-color': '#F5222D',
            // 'border-radius-base': '2px'
            // 'tabs-default-color': 'blue',
            // 'toast-default-width': 'auto',
            // 'toast-line-height': 'auto'
            // 或者可以通过 less 文件覆盖（文件路径为绝对路径）
            // hack: `true; @import "/src/assets/var.less";`,
          },
          // DO NOT REMOVE THIS LINE
          javascriptEnabled: true
        }
      }
    }
  },
})
