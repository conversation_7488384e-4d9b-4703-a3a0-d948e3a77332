module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: [
    'plugin:vue/essential',
  ],
  parserOptions: {
    ecmaVersion: 2020
  },
  rules: {
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'generator-star-spacing': 'off',
    'no-mixed-operators': 0,
    'vue/attribute-hyphenation': 0,
    'vue/html-self-closing': 0,
    'vue/component-name-in-template-casing': 0,
    'vue/html-closing-bracket-spacing': 0,
    'vue/singleline-html-element-content-newline': 0,
    'vue/multiline-html-element-content-newline': 0,
    'vue/no-use-v-if-with-v-for': 0,
    'vue/html-closing-bracket-newline': 0,
    'vue/no-parsing-error': 0,
    'vue/max-attributes-per-line': 0,
    'vue/no-v-model-argument': 'off',
    'vue/no-multiple-template-root':'off',
    'no-tabs': 0,
    quotes: 0,
    'no-delete-var': 2,
    'prefer-const': [
      2,
      {
        ignoreReadBeforeAssign: false,
      },
    ],
    'template-curly-spacing': 'off',
    // 末尾不加分号，只有在有可能语法错误时才会加分号
    semi: 0,
    // 箭头函数需要有括号 (a) => {}
    'arrow-parens': 0,
    // 两个空格缩进， switch 语句中的 case 为 1 个空格
    indent: [
      'error',
      2,
      {
        SwitchCase: 1,
        'ignoredNodes': ['TemplateLiteral']
      },
    ],
    // 关闭不允许回调未定义的变量
    'standard/no-callback-literal': 0,
    // 关闭副作⽤的 new
    'no-new': 'off',
    // 关闭每⾏最⼤⻓度⼩于 80
    'max-len': 0,
    // 函数括号前⾯不加空格
    'space-before-function-paren': ['error', 'never'],
    // 关闭要求 require() 出现在顶层模块作⽤域中
    'global-require': 0,
    // 关闭关闭类⽅法中必须使⽤this
    'class-methods-use-this': 0,
    // 关闭禁⽌对原⽣对象或只读的全局对象进⾏赋值
    'no-global-assign': 0,
    // 关闭禁⽌对关系运算符的左操作数使⽤否定操作符
    'no-unsafe-negation': 0,
    // 关闭禁⽌使⽤ console
    'no-console': 0,
    // 关闭禁⽌末尾空⾏
    'eol-last': 0,
    // 关闭强制在注释中 // 或 /* 使⽤⼀致的空格
    'spaced-comment': 0,
    // 关闭禁⽌对 function 的参数进⾏重新赋值
    'no-param-reassign': 0,
    // 强制使⽤⼀致的换⾏符⻛格 (linebreak-style)
    // 'linebreak-style': ['error', 'unix'],
    // 关闭全等 === 校验
    eqeqeq: 'off',
    // 禁⽌使⽤拖尾逗号（即末尾不加逗号）
    'comma-dangle': ['error', 'never'],
    // 关闭强制使⽤骆驼拼写法命名约定
    camelcase: 0,
    'space-before-function-paren': 0,
    'comma-dangle': 0,
    'no-trailing-spaces': 0,
    "allowJs": 0,
    'import/no-duplicates': 0,
    "@typescript-eslint/no-this-alias": ['off'],
    // "linebreak-style": [0, "error", "windows"],
    "linebreak-style": 0,
    "prefer-const": 0,
    'indent': 0,
  }
}
